#!/bin/bash

# start-https.sh - <PERSON><PERSON><PERSON> to start both client and server with HTTPS support

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# Function to handle cleanup on exit
cleanup() {
  echo "Shutting down servers..."
  kill $CLIENT_PID $SERVER_PID 2>/dev/null
  exit 0
}

# Set up trap to catch Ctrl+C
trap cleanup INT

echo "Starting booking system with HTTPS support..."

# Check if certificates exist
if [ ! -f "$SCRIPT_DIR/server/certs/server.key" ] || [ ! -f "$SCRIPT_DIR/server/certs/server.cert" ]; then
  echo "HTTPS certificates not found. Creating self-signed certificates..."
  cd "$SCRIPT_DIR/server"
  mkdir -p certs
  openssl req -x509 -newkey rsa:4096 -keyout certs/server.key -out certs/server.cert -days 365 -nodes -subj "/C=SE/ST=Stockholm/L=Stockholm/O=KTH/OU=IT/CN=localhost"
  cd "$SCRIPT_DIR"
  echo "Self-signed certificates created."
fi

# Check if node_modules exist in client and server, install if not
if [ ! -d "$SCRIPT_DIR/client/node_modules" ]; then
  echo "Installing client dependencies..."
  cd "$SCRIPT_DIR/client" && npm install
  cd "$SCRIPT_DIR"
fi

if [ ! -d "$SCRIPT_DIR/server/node_modules" ]; then
  echo "Installing server dependencies..."
  cd "$SCRIPT_DIR/server" && npm install
  cd "$SCRIPT_DIR"
fi

# Build the client first to ensure dist directory exists
echo "Building client..."
cd "$SCRIPT_DIR/client" && npm run build
cd "$SCRIPT_DIR"

# Start the server
echo "Starting server with HTTPS support..."
cd "$SCRIPT_DIR/server" && npm run dev &
SERVER_PID=$!

# Wait a moment for the server to initialize
sleep 3

# Start the client in development mode with HTTPS proxy
echo "Starting client development server..."
cd "$SCRIPT_DIR/client" && HTTPS_ENABLED=true npm run dev &
CLIENT_PID=$!

echo "Booking system is running with HTTPS support!"
echo "- Server HTTPS API: https://localhost:8990/api"
echo "- Server HTTP (redirects to HTTPS): http://localhost:8989"
echo "- Client development server: http://localhost:5173"
echo ""
echo "Note: You may need to accept the self-signed certificate in your browser."
echo "Press Ctrl+C to stop both servers."

# Wait for both processes
wait $CLIENT_PID $SERVER_PID

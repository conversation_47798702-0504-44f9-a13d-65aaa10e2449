/**
 * navigationTracking.js - Vue mixin for tracking user navigation state
 * 
 * Tracks where users are in the booking flow to restore state after server restart
 */

export default {
  data() {
    return {
      navigationTrackingEnabled: true
    };
  },

  mounted() {
    if (this.navigationTrackingEnabled) {
      this.trackPageEntry();
    }
  },

  beforeUnmount() {
    if (this.navigationTrackingEnabled) {
      this.trackPageExit();
    }
  },

  methods: {
    /**
     * Track when user enters a page
     */
    async trackPageEntry() {
      try {
        const navigationState = {
          currentPage: this.$route.path,
          timestamp: Date.now()
        };

        // Add specific state based on component
        if (this.$options.name === 'ConfirmBookingView') {
          navigationState.selectedTimeslotId = this.$route.params.id;
          navigationState.bookingType = this.bookingType || 'individual';
          navigationState.selectedGroupId = this.selectedGroupId || null;
          
          // Get reservation expiration from timeslot
          if (this.timeslot && this.timeslot.reservedUntil) {
            navigationState.reservationExpiresAt = this.timeslot.reservedUntil;
          }
        }

        await this.saveNavigationState(navigationState);
        console.log('📍 Page entry tracked:', navigationState);
      } catch (error) {
        console.error('❌ Error tracking page entry:', error);
      }
    },

    /**
     * Track when user exits a page
     */
    async trackPageExit() {
      try {
        // Only clear navigation state if moving away from booking flow
        const bookingFlowPages = ['/booking-lists', '/confirm-booking'];
        const isLeavingBookingFlow = !bookingFlowPages.some(page => 
          this.$route.path.includes(page)
        );

        if (isLeavingBookingFlow) {
          await this.clearNavigationState();
          console.log('🚪 Exited booking flow - navigation state cleared');
        }
      } catch (error) {
        console.error('❌ Error tracking page exit:', error);
      }
    },

    /**
     * Save navigation state to server
     */
    async saveNavigationState(state) {
      try {
        await fetch('/api/navigation-state', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(state)
        });
      } catch (error) {
        console.error('❌ Error saving navigation state:', error);
      }
    },

    /**
     * Clear navigation state from server
     */
    async clearNavigationState() {
      try {
        await fetch('/api/navigation-state', {
          method: 'DELETE'
        });
      } catch (error) {
        console.error('❌ Error clearing navigation state:', error);
      }
    },

    /**
     * Restore navigation state after server restart
     */
    async restoreNavigationState() {
      try {
        const response = await fetch('/api/navigation-state');
        if (response.ok) {
          const state = await response.json();
          
          if (state && state.currentPage) {
            console.log('🔄 Restoring navigation state:', state);
            
            // Redirect to the page user was on
            if (state.currentPage !== this.$route.path) {
              this.$router.push(state.currentPage);
              return;
            }

            // Restore specific component state
            if (this.$options.name === 'ConfirmBookingView' && state.selectedTimeslotId) {
              // Restore booking confirmation state
              await this.restoreBookingState(state);
            }
          }
        }
      } catch (error) {
        console.error('❌ Error restoring navigation state:', error);
      }
    },

    /**
     * Restore booking confirmation state
     */
    async restoreBookingState(state) {
      try {
        if (state.selectedTimeslotId) {
          // Fetch the timeslot data
          const response = await fetch(`/api/timeslots/${state.selectedTimeslotId}`);
          if (response.ok) {
            const timeslot = await response.json();
            
            // Check if reservation is still valid
            if (timeslot.reservedUntil && timeslot.reservedUntil > Date.now()) {
              // Restore component state
              this.timeslot = timeslot;
              this.bookingType = state.bookingType || 'individual';
              this.selectedGroupId = state.selectedGroupId || '';
              
              // Restart countdown
              this.startCountdown();
              
              console.log('✅ Booking state restored successfully');
            } else {
              // Reservation expired, redirect back to booking list
              console.log('⏰ Reservation expired during server restart');
              this.$router.push(`/booking-lists/${timeslot.bookingListId}`);
            }
          }
        }
      } catch (error) {
        console.error('❌ Error restoring booking state:', error);
      }
    },

    /**
     * Update navigation state (for dynamic changes)
     */
    async updateNavigationState(updates) {
      try {
        const currentState = {
          currentPage: this.$route.path,
          timestamp: Date.now(),
          ...updates
        };

        await this.saveNavigationState(currentState);
        console.log('📍 Navigation state updated:', updates);
      } catch (error) {
        console.error('❌ Error updating navigation state:', error);
      }
    }
  }
};

<template>
  <div>
    <h1 class="mb-4">Manage Timeslots</h1>

    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">Loading booking list details...</p>
    </div>

    <div v-else-if="error" class="alert alert-danger">
      {{ error }}
    </div>

    <div v-else>
      <div class="card mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h5 class="mb-0">{{ bookingList.title }}</h5>
          <button class="btn btn-light btn-sm" @click="showEditModal = true">Edit</button>
        </div>
        <div class="card-body">
          <p v-if="bookingList.description">{{ bookingList.description }}</p>

          <div class="row mb-4">
            <div class="col-md-6">
              <h6>Booking Details</h6>
              <ul class="list-group">
                <li class="list-group-item">
                  <strong>Examination Type:</strong>
                  <span v-if="getExaminationTypeName(bookingList.examinationTypeId)">
                    {{ getExaminationTypeName(bookingList.examinationTypeId) }}
                  </span>
                  <span v-else>Not specified</span>
                </li>
                <li class="list-group-item">
                  <strong>Default Location:</strong>
                  <span v-if="getLocationName(bookingList.locationId)">
                    {{ getLocationName(bookingList.locationId) }}
                  </span>
                  <span v-else>Not specified</span>
                </li>
                <li class="list-group-item">
                  <strong>Max Bookings:</strong>
                  {{ bookingList.maxBookingsPerStudent }} per student/group
                </li>
              </ul>
            </div>

            <div class="col-md-6">
              <h6>Time Constraints</h6>
              <ul class="list-group">
                <li class="list-group-item">
                  <strong>Booking Period:</strong>
                  <span v-if="bookingList.bookingStartDate && bookingList.bookingEndDate">
                    {{ formatDate(bookingList.bookingStartDate) }} to {{ formatDate(bookingList.bookingEndDate) }}
                  </span>
                  <span v-else-if="bookingList.bookingStartDate">
                    From {{ formatDate(bookingList.bookingStartDate) }}
                  </span>
                  <span v-else-if="bookingList.bookingEndDate">
                    Until {{ formatDate(bookingList.bookingEndDate) }}
                  </span>
                  <span v-else>No restrictions</span>
                </li>
                <li class="list-group-item">
                  <strong>Visibility Period:</strong>
                  <span v-if="bookingList.visibilityStartDate && bookingList.visibilityEndDate">
                    {{ formatDate(bookingList.visibilityStartDate) }} to {{ formatDate(bookingList.visibilityEndDate) }}
                  </span>
                  <span v-else-if="bookingList.visibilityStartDate">
                    From {{ formatDate(bookingList.visibilityStartDate) }}
                  </span>
                  <span v-else-if="bookingList.visibilityEndDate">
                    Until {{ formatDate(bookingList.visibilityEndDate) }}
                  </span>
                  <span v-else>Always visible</span>
                </li>
                <li class="list-group-item">
                  <strong>Cancellation Deadline:</strong>
                  <span v-if="bookingList.cancellationDeadline">
                    {{ formatDate(bookingList.cancellationDeadline) }}
                  </span>
                  <span v-else>No deadline</span>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-4">
            <h5>Add Timeslot</h5>
            <form @submit.prevent="addTimeslot" class="row g-3">
              <div class="col-md-3">
                <label for="date" class="form-label">Date</label>
                <input
                  id="date"
                  v-model="newTimeslot.date"
                  type="date"
                  class="form-control"
                  required
                />
              </div>

              <div class="col-md-3">
                <label for="time" class="form-label">Time</label>
                <input
                  id="time"
                  v-model="newTimeslot.time"
                  type="time"
                  class="form-control"
                  required
                />
              </div>

              <div class="col-md-4">
                <label for="location" class="form-label">Location (Optional)</label>
                <select
                  id="location"
                  v-model="newTimeslot.locationId"
                  class="form-select"
                >
                  <option value="">Use default location</option>
                  <option v-for="location in locations" :key="location.id" :value="location.id">
                    {{ location.name }}
                  </option>
                </select>
              </div>

              <div class="col-md-2 d-flex align-items-end">
                <button
                  type="submit"
                  class="btn btn-primary w-100"
                  :disabled="addingTimeslot"
                >
                  <span v-if="addingTimeslot" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                  Add
                </button>
              </div>
            </form>
          </div>

          <div class="mb-4">
            <h5>Timeslots</h5>

            <div v-if="timeslots.length === 0" class="alert alert-info">
              No timeslots available for this booking list.
            </div>

            <div v-else class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Location</th>
                    <th>Status</th>
                    <th>Booked By</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="timeslot in timeslots" :key="timeslot.id" :class="{ 'table-secondary': timeslot.booked || timeslot.reserved }">
                    <td>{{ formatDate(timeslot.date) }}</td>
                    <td>{{ timeslot.time }}</td>
                    <td>{{ getLocationName(timeslot.locationId) || getLocationName(bookingList.locationId) || 'Not specified' }}</td>
                    <td>
                      <span v-if="timeslot.booked && timeslot.cancelled" class="badge bg-secondary">Cancelled</span>
                      <span v-else-if="timeslot.booked" class="badge bg-danger">Booked</span>
                      <span v-else-if="timeslot.reserved" class="badge bg-warning">Reserved</span>
                      <span v-else class="badge bg-success">Available</span>
                    </td>
                    <td>
                      <span v-if="timeslot.bookedByStudentId">
                        {{ getStudentName(timeslot.bookedByStudentId) }}
                        <span v-if="timeslot.bookedByGroupId">(Group: {{ getGroupName(timeslot.bookedByGroupId) }})</span>
                        <span v-if="timeslot.cancelled && timeslot.cancelledById">
                          <br>
                          <small class="text-muted">Cancelled by: {{ getStudentName(timeslot.cancelledById) }}</small>
                        </span>
                      </span>
                      <span v-else>-</span>
                    </td>
                    <td>
                      <button
                        class="btn btn-sm btn-danger"
                        @click="deleteTimeslot(timeslot.id)"
                        :disabled="deletingTimeslot === timeslot.id"
                      >
                        <span v-if="deletingTimeslot === timeslot.id" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        Delete
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Booking List Modal -->
    <div v-if="showEditModal" class="modal d-block" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header bg-primary text-white">
            <h5 class="modal-title">Edit Booking List</h5>
            <button type="button" class="btn-close" @click="showEditModal = false"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="updateBookingList">
              <div class="mb-3">
                <label for="editTitle" class="form-label">Title *</label>
                <input
                  id="editTitle"
                  v-model="editedBookingList.title"
                  type="text"
                  class="form-control"
                  required
                />
              </div>

              <div class="mb-3">
                <label for="editDescription" class="form-label">Description</label>
                <textarea
                  id="editDescription"
                  v-model="editedBookingList.description"
                  class="form-control"
                  rows="3"
                ></textarea>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="editLocationType" class="form-label">Default Location</label>
                  <select
                    id="editLocationType"
                    v-model="editedBookingList.locationId"
                    class="form-select"
                  >
                    <option value="">None</option>
                    <option v-for="location in locations" :key="location.id" :value="location.id">
                      {{ location.name }}
                    </option>
                  </select>
                </div>

                <div class="col-md-6">
                  <label for="editExaminationType" class="form-label">Examination Type</label>
                  <select
                    id="editExaminationType"
                    v-model="editedBookingList.examinationTypeId"
                    class="form-select"
                  >
                    <option value="">None</option>
                    <option v-for="type in examinationTypes" :key="type.id" :value="type.id">
                      {{ type.name }}
                    </option>
                  </select>
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="editBookingStartDate" class="form-label">Booking Start Date</label>
                  <input
                    id="editBookingStartDate"
                    v-model="editedBookingList.bookingStartDate"
                    type="date"
                    class="form-control"
                  />
                </div>

                <div class="col-md-6">
                  <label for="editBookingEndDate" class="form-label">Booking End Date</label>
                  <input
                    id="editBookingEndDate"
                    v-model="editedBookingList.bookingEndDate"
                    type="date"
                    class="form-control"
                  />
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="editVisibilityStartDate" class="form-label">Visibility Start Date</label>
                  <input
                    id="editVisibilityStartDate"
                    v-model="editedBookingList.visibilityStartDate"
                    type="date"
                    class="form-control"
                  />
                </div>

                <div class="col-md-6">
                  <label for="editVisibilityEndDate" class="form-label">Visibility End Date</label>
                  <input
                    id="editVisibilityEndDate"
                    v-model="editedBookingList.visibilityEndDate"
                    type="date"
                    class="form-control"
                  />
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="editCancellationDeadline" class="form-label">Cancellation Deadline</label>
                  <input
                    id="editCancellationDeadline"
                    v-model="editedBookingList.cancellationDeadline"
                    type="date"
                    class="form-control"
                  />
                </div>

                <div class="col-md-6">
                  <label for="editMaxBookings" class="form-label">Max Bookings per Student/Group</label>
                  <input
                    id="editMaxBookings"
                    v-model.number="editedBookingList.maxBookingsPerStudent"
                    type="number"
                    min="1"
                    class="form-control"
                    required
                  />
                </div>
              </div>

              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @click="showEditModal = false">Cancel</button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  :disabled="updating"
                >
                  <span v-if="updating" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Update
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal backdrop -->
    <div v-if="showEditModal" class="modal-backdrop fade show"></div>
  </div>
</template>

<script>
export default {
  name: 'AdminBookingListDetailView',
  inject: ['socket'],
  data() {
    return {
      loading: true,
      error: null,
      bookingList: {},
      timeslots: [],
      locations: [],
      examinationTypes: [],
      students: [],
      groups: [],
      showEditModal: false,
      updating: false,
      addingTimeslot: false,
      deletingTimeslot: null,
      newTimeslot: {
        date: new Date().toISOString().split('T')[0],
        time: '',
        locationId: ''
      },
      editedBookingList: {}
    };
  },
  created() {
    this.fetchData();

    // Set up socket.io event listeners
    this.socket.on('timeslot_created', this.handleTimeslotCreated);
    this.socket.on('timeslot_deleted', this.handleTimeslotDeleted);
    this.socket.on('booking_list_updated', this.handleBookingListUpdated);
    this.socket.on('booking_list_deleted', this.handleBookingListDeleted);
  },
  beforeUnmount() {
    // Clean up socket.io event listeners
    this.socket.off('timeslot_created', this.handleTimeslotCreated);
    this.socket.off('timeslot_deleted', this.handleTimeslotDeleted);
    this.socket.off('booking_list_updated', this.handleBookingListUpdated);
    this.socket.off('booking_list_deleted', this.handleBookingListDeleted);
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true;
        const bookingListId = this.$route.params.id;

        // Fetch booking list details
        const bookingListsResponse = await fetch('/api/admin/booking-lists');
        if (!bookingListsResponse.ok) {
          throw new Error('Failed to fetch booking lists');
        }

        const bookingListsData = await bookingListsResponse.json();
        this.bookingList = bookingListsData.bookingLists.find(list => list.id === Number(bookingListId));

        if (!this.bookingList) {
          this.error = 'Booking list not found';
          this.loading = false;
          return;
        }

        // Initialize edited booking list
        this.editedBookingList = { ...this.bookingList };

        // Fetch timeslots for this booking list
        const timeslotsResponse = await fetch(`/api/admin/booking-lists/${bookingListId}/timeslots`);
        if (!timeslotsResponse.ok) {
          throw new Error('Failed to fetch timeslots');
        }

        const timeslotsData = await timeslotsResponse.json();
        this.timeslots = timeslotsData.timeslots;

        // Fetch locations
        const locationsResponse = await fetch('/api/admin/locations');
        if (!locationsResponse.ok) {
          throw new Error('Failed to fetch locations');
        }

        const locationsData = await locationsResponse.json();
        this.locations = locationsData.locations;

        // Fetch examination types
        const examinationTypesResponse = await fetch('/api/admin/examination-types');
        if (!examinationTypesResponse.ok) {
          throw new Error('Failed to fetch examination types');
        }

        const examinationTypesData = await examinationTypesResponse.json();
        this.examinationTypes = examinationTypesData.examinationTypes;

        // Fetch students
        const studentsResponse = await fetch('/api/admin/students');
        if (!studentsResponse.ok) {
          console.warn('Failed to fetch students, will display IDs instead of names');
        } else {
          const studentsData = await studentsResponse.json();
          this.students = studentsData.students;
        }

        // Fetch groups
        const groupsResponse = await fetch('/api/admin/groups');
        if (!groupsResponse.ok) {
          console.warn('Failed to fetch groups, will display IDs instead of names');
        } else {
          const groupsData = await groupsResponse.json();
          this.groups = groupsData.groups;
        }

        this.loading = false;
      } catch (error) {
        console.error('Error fetching data:', error);
        this.error = 'Failed to load data: ' + error.message;
        this.loading = false;
      }
    },
    async addTimeslot() {
      try {
        this.addingTimeslot = true;

        const timeslot = {
          ...this.newTimeslot,
          bookingListId: this.bookingList.id
        };

        const response = await fetch('/api/admin/timeslots', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(timeslot)
        });

        if (response.ok) {
          // Don't add the timeslot here, it will be added via socket event
          // to prevent duplication
          this.newTimeslot = {
            date: new Date().toISOString().split('T')[0],
            time: '',
            locationId: ''
          };
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to add timeslot');
        }
      } catch (error) {
        console.error('Error adding timeslot:', error);
        alert(error.message || 'Failed to add timeslot');
      } finally {
        this.addingTimeslot = false;
      }
    },
    async deleteTimeslot(timeslotId) {
      if (!confirm('Are you sure you want to delete this timeslot?')) {
        return;
      }

      try {
        this.deletingTimeslot = timeslotId;

        const response = await fetch(`/api/admin/timeslots/${timeslotId}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          this.timeslots = this.timeslots.filter(t => t.id !== timeslotId);
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete timeslot');
        }
      } catch (error) {
        console.error('Error deleting timeslot:', error);
        alert(error.message || 'Failed to delete timeslot');
      } finally {
        this.deletingTimeslot = null;
      }
    },
    async updateBookingList() {
      try {
        this.updating = true;

        // Convert empty strings to null
        const bookingList = { ...this.editedBookingList };
        for (const key in bookingList) {
          if (bookingList[key] === '') {
            bookingList[key] = null;
          }
        }

        const response = await fetch(`/api/admin/booking-lists/${bookingList.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(bookingList)
        });

        if (response.ok) {
          const updatedBookingList = await response.json();
          this.bookingList = updatedBookingList;
          this.showEditModal = false;
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update booking list');
        }
      } catch (error) {
        console.error('Error updating booking list:', error);
        alert(error.message || 'Failed to update booking list');
      } finally {
        this.updating = false;
      }
    },
    getExaminationTypeName(id) {
      if (!id) return null;
      const type = this.examinationTypes.find(t => t.id === id);
      return type ? type.name : null;
    },
    getLocationName(id) {
      if (!id) return null;
      const location = this.locations.find(l => l.id === id);
      return location ? location.name : null;
    },
    getStudentName(id) {
      if (!id) return 'Unknown';
      const student = this.students.find(s => s.id === id);
      return student ? student.username : `Student ID: ${id}`;
    },
    getGroupName(id) {
      if (!id) return 'Unknown';
      const group = this.groups.find(g => g.id === id);
      return group ? group.name : `Group ID: ${id}`;
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString();
    },
    handleTimeslotCreated(timeslot) {
      if (timeslot.bookingListId === this.bookingList.id) {
        this.timeslots.push(timeslot);
      }
    },
    handleTimeslotDeleted(timeslotId) {
      this.timeslots = this.timeslots.filter(t => t.id !== timeslotId);
    },

    handleBookingListUpdated(updatedBookingList) {
      if (updatedBookingList.id === this.bookingList.id) {
        this.bookingList = updatedBookingList;
      }
    },

    handleBookingListDeleted(bookingListId) {
      if (bookingListId === this.bookingList.id) {
        // Redirect to the booking lists page if the current booking list is deleted
        alert('This booking list has been deleted by another administrator.');
        this.$router.push('/admin/booking-lists');
      }
    }
  }
};
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}
</style>

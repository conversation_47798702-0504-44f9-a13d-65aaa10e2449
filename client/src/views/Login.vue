<template>
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">Login</h3>
        </div>
        <div class="card-body">
          <form @submit.prevent="login">
            <div class="mb-3">
              <label for="username" class="form-label">Username</label>
              <input
                id="username"
                v-model="username"
                type="text"
                class="form-control"
                placeholder="Enter your username"
                required
              />
            </div>
            <div class="mb-3">
              <label for="password" class="form-label">Password</label>
              <input
                id="password"
                v-model="password"
                type="password"
                class="form-control"
                placeholder="Enter your password"
                required
              />
            </div>
            <div v-if="error" class="alert alert-danger mt-3">
              {{ error }}
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="loading"
              >
                <span v-if="loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Login
              </button>
              <router-link to="/register" class="text-decoration-none">Don't have an account? Register</router-link>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoginView',
  data() {
    return {
      username: '',
      password: '',
      loading: false,
      error: null
    };
  },
  methods: {
    async login() {
      if (!this.username || !this.password) {
        this.error = 'Please enter both username and password';
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        console.log('Attempting to login with username:', this.username);

        const response = await fetch('/api/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            username: this.username,
            password: this.password
          })
        });

        console.log('Login response status:', response.status);

        // Check if the response is empty
        const text = await response.text();
        console.log('Login response text:', text);

        if (!text) {
          throw new Error('Empty response from server');
        }

        // Parse the JSON response
        const data = JSON.parse(text);

        if (!response.ok) {
          throw new Error(data.error || 'Login failed');
        }

        console.log('Login successful:', data);

        // Update store with authentication status and user data
        this.$store.commit('setAuthenticated', true);
        this.$store.commit('setUser', data.user);

        // Redirect to booking lists page
        this.$router.push('/booking-lists');
      } catch (error) {
        console.error('Login error:', error);
        this.error = error.message || 'Login failed. Please try again.';
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

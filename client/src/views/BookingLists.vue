<template>
  <div>
    <h1 class="mb-4">Available Booking Lists</h1>
    
    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">Loading booking lists...</p>
    </div>
    
    <div v-else-if="error" class="alert alert-danger">
      {{ error }}
    </div>
    
    <div v-else-if="bookingLists.length === 0" class="alert alert-info">
      No booking lists are currently available.
    </div>
    
    <div v-else class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
      <div v-for="list in bookingLists" :key="list.id" class="col">
        <div class="card h-100">
          <div class="card-header">
            <h5 class="card-title mb-0">{{ list.title }}</h5>
          </div>
          <div class="card-body">
            <p class="card-text" v-if="list.description">{{ list.description }}</p>
            <p class="card-text" v-else>No description available.</p>
            
            <div class="mb-3">
              <strong>Examination Type:</strong>
              <span v-if="getExaminationTypeName(list.examinationTypeId)">
                {{ getExaminationTypeName(list.examinationTypeId) }}
              </span>
              <span v-else>Not specified</span>
            </div>
            
            <div class="mb-3">
              <strong>Default Location:</strong>
              <span v-if="getLocationName(list.locationId)">
                {{ getLocationName(list.locationId) }}
              </span>
              <span v-else>Not specified</span>
            </div>
            
            <div class="mb-3">
              <strong>Booking Period:</strong>
              <span v-if="list.bookingStartDate && list.bookingEndDate">
                {{ formatDate(list.bookingStartDate) }} to {{ formatDate(list.bookingEndDate) }}
              </span>
              <span v-else-if="list.bookingStartDate">
                From {{ formatDate(list.bookingStartDate) }}
              </span>
              <span v-else-if="list.bookingEndDate">
                Until {{ formatDate(list.bookingEndDate) }}
              </span>
              <span v-else>No restrictions</span>
            </div>
            
            <div class="mb-3">
              <strong>Max Bookings:</strong>
              {{ list.maxBookingsPerStudent }} per student/group
            </div>
          </div>
          <div class="card-footer">
            <button 
              class="btn btn-primary w-100" 
              @click="viewBookingList(list.id)"
              :disabled="!list.bookingAllowed"
            >
              {{ list.bookingAllowed ? 'View Available Times' : 'Booking Not Currently Available' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'BookingListsView',
  data() {
    return {
      loading: true,
      error: null,
      examinationTypes: [],
      locations: []
    };
  },
  computed: {
    ...mapGetters(['getBookingLists']),
    bookingLists() {
      return this.getBookingLists;
    }
  },
  async created() {
    try {
      // Fetch booking lists
      await this.$store.dispatch('fetchBookingLists');
      
      // Fetch examination types
      const examinationTypesResponse = await fetch('/api/examination-types');
      const examinationTypesData = await examinationTypesResponse.json();
      this.examinationTypes = examinationTypesData.examinationTypes;
      
      // Fetch locations
      const locationsResponse = await fetch('/api/locations');
      const locationsData = await locationsResponse.json();
      this.locations = locationsData.locations;
      
      this.loading = false;
    } catch (error) {
      console.error('Error loading data:', error);
      this.error = 'Failed to load booking lists. Please try again later.';
      this.loading = false;
    }
  },
  methods: {
    viewBookingList(id) {
      this.$router.push(`/booking-lists/${id}`);
    },
    getExaminationTypeName(id) {
      if (!id) return null;
      const type = this.examinationTypes.find(t => t.id === id);
      return type ? type.name : null;
    },
    getLocationName(id) {
      if (!id) return null;
      const location = this.locations.find(l => l.id === id);
      return location ? location.name : null;
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString();
    }
  }
};
</script>

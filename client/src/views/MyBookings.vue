<template>
  <div>
    <h1 class="mb-4">My Bookings</h1>

    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">Loading your bookings...</p>
    </div>

    <div v-else-if="error" class="alert alert-danger">
      {{ error }}
    </div>

    <div v-else-if="bookings.length === 0" class="alert alert-info">
      You don't have any bookings yet. <router-link to="/booking-lists">Browse booking lists</router-link> to make a booking.
    </div>

    <div v-else>
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Active Bookings</h5>
        </div>
        <div class="card-body">
          <div v-if="activeBookings.length === 0" class="alert alert-info">
            You don't have any active bookings.
          </div>

          <div v-else class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Time</th>
                  <th>Location</th>
                  <th>Booking List</th>
                  <th>Booked As</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="booking in activeBookings" :key="booking.id">
                  <td>{{ formatDate(booking.date) }}</td>
                  <td>{{ booking.time }}</td>
                  <td>{{ booking.location_name || getLocationName(booking.location_id || booking.locationId) }}</td>
                  <td>{{ booking.booking_list_title || getBookingListTitle(booking.bookingListId) }}</td>
                  <td>
                    <span v-if="booking.booked_by_group_id || booking.bookedByGroupId">
                      Group: {{ booking.group_name || getGroupName(booking.booked_by_group_id || booking.bookedByGroupId) }}
                    </span>
                    <span v-else>Individual</span>
                  </td>
                  <td>
                    <button
                      class="btn btn-sm btn-danger"
                      @click="cancelBooking(booking.id)"
                      :disabled="cancelling === booking.id || !canCancel(booking.bookingListId) || !canCancelBooking(booking)"
                    >
                      <span v-if="cancelling === booking.id" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                      Cancel
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header bg-secondary text-white">
          <h5 class="mb-0">Booking History</h5>
        </div>
        <div class="card-body">
          <div v-if="cancelledBookings.length === 0" class="alert alert-info">
            You don't have any cancelled bookings.
          </div>

          <div v-else class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Time</th>
                  <th>Location</th>
                  <th>Booking List</th>
                  <th>Booked As</th>
                  <th>Status</th>
                  <th>Cancelled At</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="booking in cancelledBookings" :key="booking.id" class="table-secondary">
                  <td>{{ formatDate(booking.date) }}</td>
                  <td>{{ booking.time }}</td>
                  <td>{{ booking.location_name || getLocationName(booking.location_id || booking.locationId) }}</td>
                  <td>{{ booking.booking_list_title || getBookingListTitle(booking.bookingListId) }}</td>
                  <td>
                    <span v-if="booking.booked_by_group_id || booking.bookedByGroupId">
                      Group: {{ booking.group_name || getGroupName(booking.booked_by_group_id || booking.bookedByGroupId) }}
                    </span>
                    <span v-else>Individual</span>
                  </td>
                  <td>
                    <span class="badge bg-secondary">Cancelled</span>
                  </td>
                  <td>{{ formatDateTime(booking.cancelledAt) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'MyBookingsView',
  inject: ['socket'],
  data() {
    return {
      loading: true,
      error: null,
      bookings: [],
      bookingLists: [],
      locations: [],
      groups: [],
      cancelling: null
    };
  },
  computed: {
    ...mapGetters(['getUser', 'getGroups']),
    user() {
      return this.getUser;
    },
    activeBookings() {
      return this.bookings.filter(booking => {
        // Check both camelCase and snake_case properties for cancelled flag
        const isCancelled = booking.cancelled || booking.cancelled === 1 || booking.cancelled === true;
        return !isCancelled;
      });
    },
    cancelledBookings() {
      return this.bookings.filter(booking => {
        // Check both camelCase and snake_case properties for cancelled flag
        const isCancelled = booking.cancelled || booking.cancelled === 1 || booking.cancelled === true;
        return isCancelled;
      });
    }
  },
  created() {
    this.fetchData();

    // Set up socket.io event listeners
    this.socket.on('timeslot_cancelled', this.handleTimeslotCancelled);

    // Also listen on the root socket for backward compatibility
    if (this.$root.socket) {
      this.$root.socket.on('timeslot_cancelled', this.handleTimeslotCancelled);
    }
  },
  beforeUnmount() {
    // Clean up socket.io event listeners
    this.socket.off('timeslot_cancelled', this.handleTimeslotCancelled);

    // Also clean up the root socket
    if (this.$root.socket) {
      this.$root.socket.off('timeslot_cancelled', this.handleTimeslotCancelled);
    }
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true;
        console.log('MyBookings: Starting to fetch data');

        // Fetch user's bookings
        console.log('MyBookings: Fetching user bookings');
        const bookingsResponse = await fetch('/api/my-bookings');
        if (!bookingsResponse.ok) {
          throw new Error(`Failed to fetch bookings: ${bookingsResponse.status} ${bookingsResponse.statusText}`);
        }
        const bookingsData = await bookingsResponse.json();
        console.log('MyBookings: Received bookings data', bookingsData);
        this.bookings = bookingsData.bookings || [];

        // Fetch booking lists
        console.log('MyBookings: Fetching booking lists');
        const bookingListsResponse = await fetch('/api/booking-lists');
        if (!bookingListsResponse.ok) {
          throw new Error(`Failed to fetch booking lists: ${bookingListsResponse.status} ${bookingListsResponse.statusText}`);
        }
        const bookingListsData = await bookingListsResponse.json();
        console.log('MyBookings: Received booking lists data', bookingListsData);
        this.bookingLists = bookingListsData.bookingLists || [];

        // Fetch locations
        console.log('MyBookings: Fetching locations');
        const locationsResponse = await fetch('/api/locations');
        if (!locationsResponse.ok) {
          throw new Error(`Failed to fetch locations: ${locationsResponse.status} ${locationsResponse.statusText}`);
        }
        const locationsData = await locationsResponse.json();
        console.log('MyBookings: Received locations data', locationsData);
        this.locations = locationsData.locations || [];

        // Fetch groups
        console.log('MyBookings: Fetching groups');
        await this.$store.dispatch('fetchGroups');

        console.log('MyBookings: All data fetched successfully');
        this.loading = false;
      } catch (error) {
        console.error('Error fetching data:', error);
        this.error = `Failed to load bookings: ${error.message}`;
        this.loading = false;
      }
    },
    async cancelBooking(bookingId) {
      try {
        this.cancelling = bookingId;
        console.log(`Attempting to cancel booking with ID: ${bookingId}`);

        const response = await fetch(`/api/timeslots/${bookingId}/cancel`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          // Update the booking in the list
          const updatedBooking = await response.json();
          console.log('Received updated booking after cancellation:', updatedBooking);

          const index = this.bookings.findIndex(b => b.id === bookingId);
          if (index !== -1) {
            // Ensure the cancelled flag is set correctly
            updatedBooking.cancelled = true;
            this.bookings.splice(index, 1, updatedBooking);
            console.log('Updated booking in local state:', this.bookings[index]);
          }

          // Force a refresh of the computed properties
          this.$forceUpdate();
        } else {
          const errorData = await response.json();
          alert(errorData.error || 'Failed to cancel booking');
        }
      } catch (error) {
        console.error('Error cancelling booking:', error);
        alert('Failed to cancel booking. Please try again.');
      } finally {
        this.cancelling = null;
      }
    },
    getBookingListTitle(bookingListId) {
      const bookingList = this.bookingLists.find(list => list.id === bookingListId);
      return bookingList ? bookingList.title : 'Unknown';
    },
    getLocationName(locationId) {
      if (!locationId) return 'Not specified';
      const location = this.locations.find(loc => loc.id === locationId);
      return location ? location.name : 'Unknown';
    },
    getGroupName(groupId) {
      const group = this.getGroups.find(g => g.id === groupId);
      return group ? group.name : 'Unknown';
    },
    canCancel(bookingListId) {
      const bookingList = this.bookingLists.find(list => list.id === bookingListId);
      return bookingList ? bookingList.cancellationAllowed : true;
    },

    /**
     * Check if the current user can cancel a specific booking
     * @param {Object} booking - The booking to check
     * @returns {boolean} - Whether the user can cancel this booking
     */
    canCancelBooking(booking) {
      console.log('Checking if user can cancel booking:', booking);
      console.log('Current user:', this.user);

      // First check if the booking is already cancelled
      const isCancelled = booking.cancelled || booking.cancelled === 1 || booking.cancelled === true;
      if (isCancelled) {
        console.log('Booking is already cancelled');
        return false;
      }

      // If booked directly by this user, they can cancel it
      const bookedByStudentId = booking.booked_by_student_id || booking.bookedByStudentId;
      if (bookedByStudentId === this.user.id) {
        console.log('Booking was made by this user');
        return true;
      }

      // If booked by a group, check if the user is a member of that group
      const bookedByGroupId = booking.booked_by_group_id || booking.bookedByGroupId;
      if (bookedByGroupId) {
        console.log('Booking was made by group:', bookedByGroupId);
        console.log('User groups from store:', this.getGroups);
        console.log('User groups from user object:', this.user.groups);

        // First try to check using the groups from the Vuex store (more reliable)
        if (this.getGroups && this.getGroups.length > 0) {
          const isMember = this.getGroups.some(group => group.id === bookedByGroupId);
          console.log('User is member of the booking group (from store):', isMember);
          return isMember;
        }

        // Fallback to user.groups if available
        if (this.user.groups && this.user.groups.length > 0) {
          // Convert both to numbers to ensure consistent comparison
          const isMember = this.user.groups.some(group => Number(group.id) === Number(bookedByGroupId));
          console.log('User is member of the booking group (from user object):', isMember);
          return isMember;
        }

        console.log('No group information available for user');
      }

      console.log('User cannot cancel this booking');
      return false;
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString();
    },
    formatDateTime(dateTimeString) {
      if (!dateTimeString) return '';
      const date = new Date(dateTimeString);
      return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
    },
    handleTimeslotCancelled(updatedTimeslot) {
      console.log('Received timeslot_cancelled event:', updatedTimeslot);

      // Find the booking in our local array
      const index = this.bookings.findIndex(b => b.id === updatedTimeslot.id);

      if (index !== -1) {
        // Create a new booking object with the updated data
        const updatedBooking = {
          ...this.bookings[index],
          ...updatedTimeslot,
          cancelled: true, // Explicitly set cancelled to true
          cancelledAt: updatedTimeslot.cancelledAt || new Date().toISOString()
        };

        // Replace the old booking with the updated one
        this.bookings.splice(index, 1, updatedBooking);
        console.log('Updated booking via socket event:', updatedBooking);

        // Force a refresh of the computed properties
        this.$forceUpdate();

        // If the booking was just cancelled by this user, show a confirmation
        if (updatedTimeslot.cancelledById === this.user.id) {
          console.log('Booking was cancelled by current user');
        } else {
          // If cancelled by another user, show a notification
          const cancelledByGroupMember = updatedTimeslot.bookedByGroupId &&
            this.user.groups &&
            this.user.groups.some(g => Number(g.id) === Number(updatedTimeslot.bookedByGroupId));

          if (cancelledByGroupMember) {
            // Show a notification that a group member cancelled the booking
            alert('A member of your group has cancelled a booking.');
          }
        }
      } else {
        console.log('Booking not found in local array, fetching fresh data');
        // If we can't find the booking, refresh all data
        this.fetchData();
      }
    }
  }
};
</script>

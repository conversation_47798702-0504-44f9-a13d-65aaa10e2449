<template>
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-dark text-white">
          <h3 class="mb-0">Admin Login</h3>
        </div>
        <div class="card-body">
          <form @submit.prevent="login">
            <div class="mb-3">
              <label for="username" class="form-label">Username</label>
              <input
                id="username"
                v-model="username"
                type="text"
                class="form-control"
                placeholder="Enter admin username"
                required
              />
            </div>
            <div class="mb-3">
              <label for="password" class="form-label">Password</label>
              <input
                id="password"
                v-model="password"
                type="password"
                class="form-control"
                placeholder="Enter admin password"
                required
              />
            </div>
            <div v-if="error" class="alert alert-danger mt-3">
              {{ error }}
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <button
                type="submit"
                class="btn btn-dark"
                :disabled="loading"
              >
                <span v-if="loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Login
              </button>
              <small class="text-muted">Default: admin1 / admin123</small>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminLoginView',
  data() {
    return {
      username: '',
      password: '',
      loading: false,
      error: null
    };
  },
  methods: {
    async login() {
      if (!this.username || !this.password) {
        this.error = 'Please enter both username and password';
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        console.log('Attempting admin login with username:', this.username);

        const response = await fetch('/api/admin/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            username: this.username,
            password: this.password
          })
        });

        console.log('Admin login response status:', response.status);

        // Check if the response is empty
        const text = await response.text();
        console.log('Admin login response text:', text);

        if (!text) {
          throw new Error('Empty response from server');
        }

        // Parse the JSON response
        const data = JSON.parse(text);

        if (!response.ok) {
          throw new Error(data.error || 'Login failed');
        }

        console.log('Admin login successful:', data);

        // Update store with admin authentication status
        this.$store.commit('setAdminAuthenticated', true);
        this.$store.commit('setAdminUsername', this.username);

        // Redirect to admin dashboard
        this.$router.push('/admin');
      } catch (error) {
        console.error('Admin login error:', error);
        this.error = error.message || 'Login failed. Please try again.';
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

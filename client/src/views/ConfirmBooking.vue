<template>
  <div>
    <h1 class="mb-4">Confirm Booking</h1>
    
    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">Loading timeslot details...</p>
    </div>
    
    <div v-else-if="error" class="alert alert-danger">
      {{ error }}
    </div>
    
    <div v-else class="row">
      <div class="col-md-8 mx-auto">
        <div class="card">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Confirm Your Booking</h5>
          </div>
          <div class="card-body">
            <div class="alert alert-warning">
              <strong>Time remaining:</strong> {{ timeRemaining }} seconds
            </div>
            
            <h5>Timeslot Details</h5>
            <div class="mb-4">
              <p><strong>Date:</strong> {{ formatDate(timeslot.date) }}</p>
              <p><strong>Time:</strong> {{ timeslot.time }}</p>
              <p><strong>Location:</strong> {{ locationName }}</p>
            </div>
            
            <form @submit.prevent="confirmBooking">
              <div class="mb-3">
                <label class="form-label">Book as:</label>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    id="bookAsIndividual"
                    v-model="bookingType"
                    value="individual"
                  />
                  <label class="form-check-label" for="bookAsIndividual">
                    Individual
                  </label>
                </div>
                <div class="form-check" v-if="groups.length > 0">
                  <input
                    class="form-check-input"
                    type="radio"
                    id="bookAsGroup"
                    v-model="bookingType"
                    value="group"
                  />
                  <label class="form-check-label" for="bookAsGroup">
                    Group
                  </label>
                </div>
              </div>
              
              <div class="mb-3" v-if="bookingType === 'group' && groups.length > 0">
                <label for="groupSelect" class="form-label">Select Group:</label>
                <select
                  id="groupSelect"
                  class="form-select"
                  v-model="selectedGroupId"
                  required
                >
                  <option value="" disabled>Select a group</option>
                  <option v-for="group in groups" :key="group.id" :value="group.id">
                    {{ group.name }} ({{ group.members.length }} members)
                  </option>
                </select>
              </div>
              
              <div v-if="bookingType === 'group' && groups.length === 0" class="alert alert-info">
                You don't have any groups. <router-link to="/groups">Create a group</router-link> first to book as a group.
              </div>
              
              <div class="d-flex justify-content-between mt-4">
                <button
                  type="button"
                  class="btn btn-secondary"
                  @click="cancelReservation"
                  :disabled="confirming"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  class="btn btn-success"
                  :disabled="confirming || (bookingType === 'group' && !selectedGroupId)"
                >
                  <span v-if="confirming" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Confirm Booking
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'ConfirmBookingView',
  inject: ['socket'],
  data() {
    return {
      loading: true,
      error: null,
      timeslot: null,
      locationName: '',
      timeRemaining: 10,
      countdownInterval: null,
      bookingType: 'individual',
      selectedGroupId: '',
      confirming: false
    };
  },
  computed: {
    ...mapGetters(['getSelectedTimeslot', 'getUser', 'getGroups']),
    user() {
      return this.getUser;
    },
    groups() {
      return this.getGroups;
    }
  },
  async created() {
    try {
      // Get the timeslot ID from the route
      const timeslotId = this.$route.params.id;
      
      // Check if we have the timeslot in the store
      const storedTimeslot = this.getSelectedTimeslot;
      
      if (storedTimeslot && storedTimeslot.id === Number(timeslotId)) {
        this.timeslot = storedTimeslot;
      } else {
        // Fetch the timeslot details
        const response = await fetch(`/api/timeslots/${timeslotId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch timeslot details');
        }
        this.timeslot = await response.json();
      }
      
      // Fetch location details
      if (this.timeslot.locationId) {
        const locationsResponse = await fetch('/api/locations');
        const locationsData = await locationsResponse.json();
        const location = locationsData.locations.find(loc => loc.id === this.timeslot.locationId);
        this.locationName = location ? location.name : 'Unknown';
      } else {
        this.locationName = 'Not specified';
      }
      
      // Fetch groups
      await this.$store.dispatch('fetchGroups');
      
      // Start the countdown
      this.startCountdown();
      
      this.loading = false;
    } catch (error) {
      console.error('Error loading data:', error);
      this.error = 'Failed to load timeslot details. Please try again.';
      this.loading = false;
    }
  },
  beforeUnmount() {
    // Clear the countdown interval when the component is destroyed
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
  },
  methods: {
    startCountdown() {
      // Calculate the remaining time based on the reservation expiration
      if (this.timeslot && this.timeslot.reservedUntil) {
        const now = Date.now();
        const reservedUntil = this.timeslot.reservedUntil;
        this.timeRemaining = Math.max(0, Math.floor((reservedUntil - now) / 1000));
      }
      
      // Start the countdown
      this.countdownInterval = setInterval(() => {
        if (this.timeRemaining > 0) {
          this.timeRemaining--;
        } else {
          // Time's up, redirect to booking list
          clearInterval(this.countdownInterval);
          alert('Reservation time expired');
          this.$router.push(`/booking-lists/${this.timeslot.bookingListId}`);
        }
      }, 1000);
    },
    async confirmBooking() {
      try {
        this.confirming = true;
        
        const timeslotId = this.timeslot.id;
        const requestBody = {};
        
        if (this.bookingType === 'group' && this.selectedGroupId) {
          requestBody.groupId = this.selectedGroupId;
        }
        
        const response = await fetch(`/api/timeslots/${timeslotId}/book`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });
        
        if (response.ok) {
          // Clear the countdown interval
          if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
          }
          
          // Show success message and redirect
          alert('Booking confirmed successfully!');
          this.$router.push('/my-bookings');
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to confirm booking');
        }
      } catch (error) {
        console.error('Error confirming booking:', error);
        alert(error.message || 'Failed to confirm booking. Please try again.');
      } finally {
        this.confirming = false;
      }
    },
    async cancelReservation() {
      try {
        // Clear the countdown interval
        if (this.countdownInterval) {
          clearInterval(this.countdownInterval);
        }
        
        // Redirect back to the booking list
        this.$router.push(`/booking-lists/${this.timeslot.bookingListId}`);
      } catch (error) {
        console.error('Error cancelling reservation:', error);
      }
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString();
    }
  }
};
</script>

<template>
  <div>
    <h1 class="mb-4">Manage Booking Lists</h1>

    <div class="mb-4">
      <button class="btn btn-primary" @click="showCreateModal = true">
        Create New Booking List
      </button>
    </div>

    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">Loading booking lists...</p>
    </div>

    <div v-else-if="error" class="alert alert-danger">
      {{ error }}
    </div>

    <div v-else-if="bookingLists.length === 0" class="alert alert-info">
      No booking lists found. Create a new booking list to get started.
    </div>

    <div v-else class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
      <div v-for="list in bookingLists" :key="list.id" class="col">
        <div class="card h-100">
          <div class="card-header">
            <h5 class="card-title mb-0">{{ list.title }}</h5>
          </div>
          <div class="card-body">
            <p class="card-text" v-if="list.description">{{ list.description }}</p>
            <p class="card-text" v-else>No description available.</p>

            <div class="mb-3">
              <strong>Examination Type:</strong>
              <span v-if="getExaminationTypeName(list.examinationTypeId)">
                {{ getExaminationTypeName(list.examinationTypeId) }}
              </span>
              <span v-else>Not specified</span>
            </div>

            <div class="mb-3">
              <strong>Default Location:</strong>
              <span v-if="getLocationName(list.locationId)">
                {{ getLocationName(list.locationId) }}
              </span>
              <span v-else>Not specified</span>
            </div>

            <div class="mb-3">
              <strong>Booking Period:</strong>
              <span v-if="list.bookingStartDate && list.bookingEndDate">
                {{ formatDate(list.bookingStartDate) }} to {{ formatDate(list.bookingEndDate) }}
              </span>
              <span v-else-if="list.bookingStartDate">
                From {{ formatDate(list.bookingStartDate) }}
              </span>
              <span v-else-if="list.bookingEndDate">
                Until {{ formatDate(list.bookingEndDate) }}
              </span>
              <span v-else>No restrictions</span>
            </div>

            <div class="mb-3">
              <strong>Visibility Period:</strong>
              <span v-if="list.visibilityStartDate && list.visibilityEndDate">
                {{ formatDate(list.visibilityStartDate) }} to {{ formatDate(list.visibilityEndDate) }}
              </span>
              <span v-else-if="list.visibilityStartDate">
                From {{ formatDate(list.visibilityStartDate) }}
              </span>
              <span v-else-if="list.visibilityEndDate">
                Until {{ formatDate(list.visibilityEndDate) }}
              </span>
              <span v-else>Always visible</span>
            </div>

            <div class="mb-3">
              <strong>Cancellation Deadline:</strong>
              <span v-if="list.cancellationDeadline">
                {{ formatDate(list.cancellationDeadline) }}
              </span>
              <span v-else>No deadline</span>
            </div>

            <div class="mb-3">
              <strong>Max Bookings:</strong>
              {{ list.maxBookingsPerStudent }} per student/group
            </div>
          </div>
          <div class="card-footer">
            <div class="d-flex justify-content-between mb-2">
              <router-link :to="`/admin/booking-lists/${list.id}`" class="btn btn-primary flex-grow-1 me-2">
                Manage Timeslots
              </router-link>
              <button
                class="btn btn-danger"
                @click="deleteBookingList(list.id)"
                :disabled="deletingBookingList === list.id"
              >
                <span v-if="deletingBookingList === list.id" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <i v-else class="bi bi-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Booking List Modal -->
    <div v-if="showCreateModal" class="modal d-block" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header bg-primary text-white">
            <h5 class="modal-title">Create New Booking List</h5>
            <button type="button" class="btn-close" @click="showCreateModal = false"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="createBookingList">
              <div class="mb-3">
                <label for="title" class="form-label">Title *</label>
                <input
                  id="title"
                  v-model="newBookingList.title"
                  type="text"
                  class="form-control"
                  required
                />
              </div>

              <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea
                  id="description"
                  v-model="newBookingList.description"
                  class="form-control"
                  rows="3"
                ></textarea>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="locationType" class="form-label">Default Location</label>
                  <select
                    id="locationType"
                    v-model="newBookingList.locationId"
                    class="form-select"
                  >
                    <option value="">None</option>
                    <option v-for="location in locations" :key="location.id" :value="location.id">
                      {{ location.name }}
                    </option>
                  </select>
                </div>

                <div class="col-md-6">
                  <label for="examinationType" class="form-label">Examination Type</label>
                  <select
                    id="examinationType"
                    v-model="newBookingList.examinationTypeId"
                    class="form-select"
                  >
                    <option value="">None</option>
                    <option v-for="type in examinationTypes" :key="type.id" :value="type.id">
                      {{ type.name }}
                    </option>
                  </select>
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="bookingStartDate" class="form-label">Booking Start Date</label>
                  <input
                    id="bookingStartDate"
                    v-model="newBookingList.bookingStartDate"
                    type="date"
                    class="form-control"
                  />
                </div>

                <div class="col-md-6">
                  <label for="bookingEndDate" class="form-label">Booking End Date</label>
                  <input
                    id="bookingEndDate"
                    v-model="newBookingList.bookingEndDate"
                    type="date"
                    class="form-control"
                  />
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="visibilityStartDate" class="form-label">Visibility Start Date</label>
                  <input
                    id="visibilityStartDate"
                    v-model="newBookingList.visibilityStartDate"
                    type="date"
                    class="form-control"
                  />
                </div>

                <div class="col-md-6">
                  <label for="visibilityEndDate" class="form-label">Visibility End Date</label>
                  <input
                    id="visibilityEndDate"
                    v-model="newBookingList.visibilityEndDate"
                    type="date"
                    class="form-control"
                  />
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="cancellationDeadline" class="form-label">Cancellation Deadline</label>
                  <input
                    id="cancellationDeadline"
                    v-model="newBookingList.cancellationDeadline"
                    type="date"
                    class="form-control"
                  />
                </div>

                <div class="col-md-6">
                  <label for="maxBookings" class="form-label">Max Bookings per Student/Group</label>
                  <input
                    id="maxBookings"
                    v-model.number="newBookingList.maxBookingsPerStudent"
                    type="number"
                    min="1"
                    class="form-control"
                    required
                  />
                </div>
              </div>

              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @click="showCreateModal = false">Cancel</button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  :disabled="creating"
                >
                  <span v-if="creating" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Create
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal backdrop -->
    <div v-if="showCreateModal" class="modal-backdrop fade show"></div>
  </div>
</template>

<script>
export default {
  name: 'AdminBookingListsView',
  inject: ['socket'],
  data() {
    return {
      loading: true,
      error: null,
      bookingLists: [],
      locations: [],
      examinationTypes: [],
      showCreateModal: false,
      creating: false,
      deletingBookingList: null,
      newBookingList: {
        title: '',
        description: '',
        locationId: '',
        examinationTypeId: '',
        bookingStartDate: '',
        bookingEndDate: '',
        visibilityStartDate: '',
        visibilityEndDate: '',
        cancellationDeadline: '',
        maxBookingsPerStudent: 1
      }
    };
  },
  created() {
    this.fetchData();

    // Set up socket.io event listeners
    this.socket.on('booking_list_updated', this.handleBookingListUpdated);
    this.socket.on('booking_list_deleted', this.handleBookingListDeleted);
  },
  beforeUnmount() {
    // Clean up socket.io event listeners
    this.socket.off('booking_list_updated', this.handleBookingListUpdated);
    this.socket.off('booking_list_deleted', this.handleBookingListDeleted);
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true;

        // Fetch booking lists
        const bookingListsResponse = await fetch('/api/admin/booking-lists');
        const bookingListsData = await bookingListsResponse.json();
        this.bookingLists = bookingListsData.bookingLists;

        // Fetch locations
        const locationsResponse = await fetch('/api/admin/locations');
        const locationsData = await locationsResponse.json();
        this.locations = locationsData.locations;

        // Fetch examination types
        const examinationTypesResponse = await fetch('/api/admin/examination-types');
        const examinationTypesData = await examinationTypesResponse.json();
        this.examinationTypes = examinationTypesData.examinationTypes;

        this.loading = false;
      } catch (error) {
        console.error('Error fetching data:', error);
        this.error = 'Failed to load data. Please try again later.';
        this.loading = false;
      }
    },
    async createBookingList() {
      try {
        this.creating = true;

        // Convert empty strings to null
        const bookingList = { ...this.newBookingList };
        for (const key in bookingList) {
          if (bookingList[key] === '') {
            bookingList[key] = null;
          }
        }

        const response = await fetch('/api/admin/booking-lists', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(bookingList)
        });

        if (response.ok) {
          const newBookingList = await response.json();
          this.bookingLists.push(newBookingList);
          this.showCreateModal = false;
          this.resetNewBookingList();
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create booking list');
        }
      } catch (error) {
        console.error('Error creating booking list:', error);
        alert(error.message || 'Failed to create booking list');
      } finally {
        this.creating = false;
      }
    },
    resetNewBookingList() {
      this.newBookingList = {
        title: '',
        description: '',
        locationId: '',
        examinationTypeId: '',
        bookingStartDate: '',
        bookingEndDate: '',
        visibilityStartDate: '',
        visibilityEndDate: '',
        cancellationDeadline: '',
        maxBookingsPerStudent: 1
      };
    },
    getExaminationTypeName(id) {
      if (!id) return null;
      const type = this.examinationTypes.find(t => t.id === id);
      return type ? type.name : null;
    },
    getLocationName(id) {
      if (!id) return null;
      const location = this.locations.find(l => l.id === id);
      return location ? location.name : null;
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString();
    },

    async deleteBookingList(bookingListId) {
      if (!confirm('Are you sure you want to delete this booking list? This will also delete all associated timeslots and cannot be undone.')) {
        return;
      }

      try {
        this.deletingBookingList = bookingListId;

        const response = await fetch(`/api/admin/booking-lists/${bookingListId}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          // Remove the booking list from the array
          this.bookingLists = this.bookingLists.filter(list => list.id !== bookingListId);
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete booking list');
        }
      } catch (error) {
        console.error('Error deleting booking list:', error);
        alert(error.message || 'Failed to delete booking list');
      } finally {
        this.deletingBookingList = null;
      }
    },

    handleBookingListUpdated(updatedBookingList) {
      // Find and update the booking list in the array
      const index = this.bookingLists.findIndex(list => list.id === updatedBookingList.id);
      if (index !== -1) {
        this.bookingLists.splice(index, 1, updatedBookingList);
      }
    },

    handleBookingListDeleted(bookingListId) {
      // Remove the booking list from the array
      this.bookingLists = this.bookingLists.filter(list => list.id !== bookingListId);
    }
  }
};
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}
</style>

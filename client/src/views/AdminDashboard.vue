<template>
  <div>
    <h1 class="mb-4">Admin Dashboard</h1>
    
    <div class="row">
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Booking Lists</h5>
          </div>
          <div class="card-body">
            <p>Manage booking lists, timeslots, and booking constraints.</p>
            <router-link to="/admin/booking-lists" class="btn btn-primary">Manage Booking Lists</router-link>
          </div>
        </div>
      </div>
      
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-header bg-success text-white">
            <h5 class="mb-0">Locations</h5>
          </div>
          <div class="card-body">
            <p>Manage locations for bookings.</p>
            <button class="btn btn-success" @click="showLocationsModal = true">Manage Locations</button>
          </div>
        </div>
      </div>
      
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-header bg-info text-white">
            <h5 class="mb-0">Examination Types</h5>
          </div>
          <div class="card-body">
            <p>Manage examination types for booking lists.</p>
            <button class="btn btn-info" @click="showExamTypesModal = true">Manage Examination Types</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Locations Modal -->
    <div v-if="showLocationsModal" class="modal d-block" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header bg-success text-white">
            <h5 class="modal-title">Manage Locations</h5>
            <button type="button" class="btn-close" @click="showLocationsModal = false"></button>
          </div>
          <div class="modal-body">
            <div v-if="loadingLocations" class="text-center my-3">
              <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            
            <div v-else>
              <h6>Add New Location</h6>
              <form @submit.prevent="addLocation" class="mb-4">
                <div class="row g-3">
                  <div class="col-md-6">
                    <input
                      type="text"
                      class="form-control"
                      v-model="newLocation.name"
                      placeholder="Location name"
                      required
                    />
                  </div>
                  <div class="col-md-6">
                    <div class="input-group">
                      <input
                        type="text"
                        class="form-control"
                        v-model="newLocation.description"
                        placeholder="Description (optional)"
                      />
                      <button
                        type="submit"
                        class="btn btn-success"
                        :disabled="addingLocation"
                      >
                        <span v-if="addingLocation" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        Add
                      </button>
                    </div>
                  </div>
                </div>
              </form>
              
              <h6>Existing Locations</h6>
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="location in locations" :key="location.id">
                      <td>{{ location.id }}</td>
                      <td>{{ location.name }}</td>
                      <td>{{ location.description || 'N/A' }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="showLocationsModal = false">Close</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Examination Types Modal -->
    <div v-if="showExamTypesModal" class="modal d-block" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header bg-info text-white">
            <h5 class="modal-title">Manage Examination Types</h5>
            <button type="button" class="btn-close" @click="showExamTypesModal = false"></button>
          </div>
          <div class="modal-body">
            <div v-if="loadingExamTypes" class="text-center my-3">
              <div class="spinner-border text-info" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            
            <div v-else>
              <h6>Add New Examination Type</h6>
              <form @submit.prevent="addExamType" class="mb-4">
                <div class="row g-3">
                  <div class="col-md-6">
                    <input
                      type="text"
                      class="form-control"
                      v-model="newExamType.name"
                      placeholder="Type name"
                      required
                    />
                  </div>
                  <div class="col-md-6">
                    <div class="input-group">
                      <input
                        type="text"
                        class="form-control"
                        v-model="newExamType.description"
                        placeholder="Description (optional)"
                      />
                      <button
                        type="submit"
                        class="btn btn-info"
                        :disabled="addingExamType"
                      >
                        <span v-if="addingExamType" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        Add
                      </button>
                    </div>
                  </div>
                </div>
              </form>
              
              <h6>Existing Examination Types</h6>
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="type in examinationTypes" :key="type.id">
                      <td>{{ type.id }}</td>
                      <td>{{ type.name }}</td>
                      <td>{{ type.description || 'N/A' }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="showExamTypesModal = false">Close</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal backdrop -->
    <div v-if="showLocationsModal || showExamTypesModal" class="modal-backdrop fade show"></div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'AdminDashboardView',
  data() {
    return {
      showLocationsModal: false,
      showExamTypesModal: false,
      locations: [],
      examinationTypes: [],
      loadingLocations: false,
      loadingExamTypes: false,
      newLocation: {
        name: '',
        description: ''
      },
      newExamType: {
        name: '',
        description: ''
      },
      addingLocation: false,
      addingExamType: false
    };
  },
  computed: {
    ...mapGetters(['getAdminUsername'])
  },
  watch: {
    showLocationsModal(val) {
      if (val) {
        this.fetchLocations();
      }
    },
    showExamTypesModal(val) {
      if (val) {
        this.fetchExaminationTypes();
      }
    }
  },
  methods: {
    async fetchLocations() {
      try {
        this.loadingLocations = true;
        const response = await fetch('/api/admin/locations');
        const data = await response.json();
        this.locations = data.locations;
      } catch (error) {
        console.error('Error fetching locations:', error);
        alert('Failed to load locations');
      } finally {
        this.loadingLocations = false;
      }
    },
    async fetchExaminationTypes() {
      try {
        this.loadingExamTypes = true;
        const response = await fetch('/api/admin/examination-types');
        const data = await response.json();
        this.examinationTypes = data.examinationTypes;
      } catch (error) {
        console.error('Error fetching examination types:', error);
        alert('Failed to load examination types');
      } finally {
        this.loadingExamTypes = false;
      }
    },
    async addLocation() {
      try {
        this.addingLocation = true;
        const response = await fetch('/api/admin/locations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.newLocation)
        });
        
        if (response.ok) {
          const newLocation = await response.json();
          this.locations.push(newLocation);
          this.newLocation = { name: '', description: '' };
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to add location');
        }
      } catch (error) {
        console.error('Error adding location:', error);
        alert(error.message || 'Failed to add location');
      } finally {
        this.addingLocation = false;
      }
    },
    async addExamType() {
      try {
        this.addingExamType = true;
        const response = await fetch('/api/admin/examination-types', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.newExamType)
        });
        
        if (response.ok) {
          const newType = await response.json();
          this.examinationTypes.push(newType);
          this.newExamType = { name: '', description: '' };
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to add examination type');
        }
      } catch (error) {
        console.error('Error adding examination type:', error);
        alert(error.message || 'Failed to add examination type');
      } finally {
        this.addingExamType = false;
      }
    }
  }
};
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}
</style>

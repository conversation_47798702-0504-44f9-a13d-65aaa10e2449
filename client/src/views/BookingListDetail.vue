<template>
  <div>
    <h1 class="mb-4">Booking List Details</h1>

    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">Loading timeslots...</p>
    </div>

    <div v-else-if="error" class="alert alert-danger">
      {{ error }}
    </div>

    <div v-else>
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">{{ bookingList ? bookingList.title : 'Booking List' }}</h5>
        </div>
        <div class="card-body">
          <p v-if="bookingList && bookingList.description">{{ bookingList.description }}</p>

          <div class="alert alert-info" v-if="!bookingList.bookingAllowed">
            Booking is not currently allowed for this list.
          </div>

          <h5 class="mt-4">Available Timeslots</h5>

          <div v-if="timeslots.length === 0" class="alert alert-info">
            No timeslots available for this booking list.
          </div>

          <div v-else class="table-responsive">
            <table class="table table-striped table-hover">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Time</th>
                  <th>Location</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="timeslot in timeslots" :key="timeslot.id" :class="{ 'table-secondary': (timeslot.booked && !timeslot.cancelled) || timeslot.reserved }">
                  <td>{{ formatDate(timeslot.date) }}</td>
                  <td>{{ timeslot.time }}</td>
                  <td>{{ getLocationName(timeslot.locationId) }}</td>
                  <td>
                    <span v-if="isBookedByUserOrGroup(timeslot)" class="badge bg-info">Your Booking</span>
                    <span v-else-if="timeslot.booked && !timeslot.cancelled" class="badge bg-danger">Booked</span>
                    <span v-else-if="timeslot.reserved" class="badge bg-warning">Reserved</span>
                    <span v-else-if="!timeslot.booked || timeslot.cancelled" class="badge bg-success">Available</span>
                  </td>
                  <td>
                    <button
                      v-if="(!timeslot.booked || timeslot.cancelled) && !timeslot.reserved"
                      class="btn btn-sm btn-primary"
                      @click="reserveTimeslot(timeslot.id)"
                      :disabled="loading || reservingTimeslot"
                    >
                      <span v-if="reservingTimeslot && reservingTimeslotId === timeslot.id" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                      Reserve
                    </button>
                    <button
                      v-else-if="isBookedByUserOrGroup(timeslot)"
                      class="btn btn-sm btn-danger"
                      @click="cancelBooking(timeslot.id)"
                      :disabled="loading || cancellingBooking || !canCancelBooking(timeslot)"
                    >
                      <span v-if="cancellingBooking && cancellingBookingId === timeslot.id" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                      Cancel
                    </button>
                    <span v-else-if="timeslot.booked && !timeslot.cancelled">Booked</span>
                    <span v-else-if="timeslot.reserved">Reserved</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'BookingListDetailView',
  inject: ['socket'],
  data() {
    return {
      loading: true,
      error: null,
      bookingList: null,
      timeslots: [],
      locations: [],
      reservingTimeslot: false,
      reservingTimeslotId: null,
      cancellingBooking: false,
      cancellingBookingId: null
    };
  },
  computed: {
    ...mapGetters(['getUser']),
    user() {
      return this.getUser;
    }
  },
  created() {
    this.fetchData();

    // Set up socket.io event listeners
    this.socket.on('timeslot_reserved', this.handleTimeslotReserved);
    this.socket.on('timeslot_reservation_cancelled', this.handleReservationCancelled);
    this.socket.on('timeslot_booked', this.handleTimeslotBooked);
    this.socket.on('timeslot_cancelled', this.handleTimeslotCancelled);
  },
  beforeUnmount() {
    // Clean up socket.io event listeners
    this.socket.off('timeslot_reserved', this.handleTimeslotReserved);
    this.socket.off('timeslot_reservation_cancelled', this.handleReservationCancelled);
    this.socket.off('timeslot_booked', this.handleTimeslotBooked);
    this.socket.off('timeslot_cancelled', this.handleTimeslotCancelled);
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true;
        const bookingListId = this.$route.params.id;

        // Fetch booking list details
        const bookingListsResponse = await fetch('/api/booking-lists');
        const bookingListsData = await bookingListsResponse.json();
        this.bookingList = bookingListsData.bookingLists.find(list => list.id === Number(bookingListId));

        if (!this.bookingList) {
          this.error = 'Booking list not found';
          this.loading = false;
          return;
        }

        // Fetch timeslots for this booking list
        const timeslotsResponse = await fetch(`/api/booking-lists/${bookingListId}/timeslots`);
        const timeslotsData = await timeslotsResponse.json();
        this.timeslots = timeslotsData.timeslots;

        // Fetch locations
        const locationsResponse = await fetch('/api/locations');
        const locationsData = await locationsResponse.json();
        this.locations = locationsData.locations;

        this.loading = false;
      } catch (error) {
        console.error('Error fetching data:', error);
        this.error = 'Failed to load data. Please try again later.';
        this.loading = false;
      }
    },
    async reserveTimeslot(timeslotId) {
      try {
        this.reservingTimeslot = true;
        this.reservingTimeslotId = timeslotId;

        const response = await fetch(`/api/timeslots/${timeslotId}/reserve`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const timeslot = await response.json();
          this.$store.commit('setSelectedTimeslot', timeslot);
          this.$router.push(`/confirm-booking/${timeslotId}`);
        } else {
          const errorData = await response.json();
          alert(errorData.error || 'Failed to reserve timeslot');
        }
      } catch (error) {
        console.error('Error reserving timeslot:', error);
        alert('Failed to reserve timeslot. Please try again.');
      } finally {
        this.reservingTimeslot = false;
        this.reservingTimeslotId = null;
      }
    },
    getLocationName(locationId) {
      if (!locationId) return 'Not specified';
      const location = this.locations.find(loc => loc.id === locationId);
      return location ? location.name : 'Unknown';
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString();
    },
    handleTimeslotReserved(updatedTimeslot) {
      const index = this.timeslots.findIndex(t => t.id === updatedTimeslot.id);
      if (index !== -1) {
        this.timeslots.splice(index, 1, updatedTimeslot);
      }
    },
    handleReservationCancelled(updatedTimeslot) {
      const index = this.timeslots.findIndex(t => t.id === updatedTimeslot.id);
      if (index !== -1) {
        this.timeslots.splice(index, 1, updatedTimeslot);
      }
    },
    handleTimeslotBooked(updatedTimeslot) {
      const index = this.timeslots.findIndex(t => t.id === updatedTimeslot.id);
      if (index !== -1) {
        this.timeslots.splice(index, 1, updatedTimeslot);
      }
    },
    handleTimeslotCancelled(updatedTimeslot) {
      console.log('Received timeslot_cancelled event:', updatedTimeslot);
      const index = this.timeslots.findIndex(t => t.id === updatedTimeslot.id);
      if (index !== -1) {
        // Create a new timeslot object with the updated data
        const updatedTimeslotWithCancelled = {
          ...this.timeslots[index], // Keep existing properties
          ...updatedTimeslot,       // Apply updates
          cancelled: true,          // Explicitly set cancelled to true
          booked: true              // Keep booked flag true (for history)
        };

        // Replace the old timeslot with the updated one
        this.timeslots.splice(index, 1, updatedTimeslotWithCancelled);
        console.log('Updated timeslot via socket event:', updatedTimeslotWithCancelled);

        // Force a refresh of the view
        this.$forceUpdate();

        // If cancelled by another user in the same group, show a notification
        if (updatedTimeslot.cancelledById !== this.user.id &&
            updatedTimeslot.bookedByGroupId &&
            this.user.groups &&
            this.user.groups.some(g => Number(g.id) === Number(updatedTimeslot.bookedByGroupId))) {
          // Show a notification that a group member cancelled the booking
          alert('A member of your group has cancelled a booking.');
        }
      } else {
        console.log('Timeslot not found in local array, fetching fresh data');
        // If we can't find the timeslot, refresh all data
        this.fetchData();
      }
    },

    /**
     * Check if a timeslot is booked by the current user or a group they belong to
     * @param {Object} timeslot - The timeslot to check
     * @returns {boolean} - Whether the timeslot is booked by the user or their group
     */
    isBookedByUserOrGroup(timeslot) {
      // First check if the booking is cancelled
      if (timeslot.cancelled) {
        return false;
      }

      // Check if booked directly by this user
      if (timeslot.bookedByStudentId === this.user.id) {
        return true;
      }

      // Check if booked by a group this user belongs to
      if (timeslot.bookedByGroupId) {
        // First try to check using the groups from the Vuex store if available
        if (this.$store.getters.getGroups && this.$store.getters.getGroups.length > 0) {
          return this.$store.getters.getGroups.some(group => Number(group.id) === Number(timeslot.bookedByGroupId));
        }

        // Fallback to user.groups if available
        if (this.user.groups && this.user.groups.length > 0) {
          // Convert both to numbers to ensure consistent comparison
          return this.user.groups.some(group => Number(group.id) === Number(timeslot.bookedByGroupId));
        }
      }

      return false;
    },

    /**
     * Check if the current user can cancel a specific booking
     * @param {Object} timeslot - The timeslot to check
     * @returns {boolean} - Whether the user can cancel this booking
     */
    canCancelBooking(timeslot) {
      // Check if the booking list allows cancellation
      const bookingList = this.bookingList;
      if (!bookingList || !bookingList.cancellationAllowed) {
        return false;
      }

      // First check if the booking is already cancelled
      if (timeslot.cancelled) {
        return false;
      }

      // If booked directly by this user, they can cancel it
      if (timeslot.bookedByStudentId === this.user.id) {
        return true;
      }

      // If booked by a group, check if the user is a member of that group
      if (timeslot.bookedByGroupId) {
        // First try to check using the groups from the Vuex store if available
        if (this.$store.getters.getGroups && this.$store.getters.getGroups.length > 0) {
          return this.$store.getters.getGroups.some(group => Number(group.id) === Number(timeslot.bookedByGroupId));
        }

        // Fallback to user.groups if available
        if (this.user.groups && this.user.groups.length > 0) {
          // Convert both to numbers to ensure consistent comparison
          return this.user.groups.some(group => Number(group.id) === Number(timeslot.bookedByGroupId));
        }
      }

      return false;
    },

    /**
     * Cancel a booking
     * @param {number} timeslotId - The ID of the timeslot to cancel
     */
    async cancelBooking(timeslotId) {
      if (!confirm('Are you sure you want to cancel this booking?')) {
        return;
      }

      try {
        this.cancellingBooking = true;
        this.cancellingBookingId = timeslotId;

        const response = await fetch(`/api/timeslots/${timeslotId}/cancel`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          // The timeslot will be updated via socket event
          alert('Booking cancelled successfully');
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to cancel booking');
        }
      } catch (error) {
        console.error('Error cancelling booking:', error);
        alert(error.message || 'Failed to cancel booking. Please try again.');
      } finally {
        this.cancellingBooking = false;
        this.cancellingBookingId = null;
      }
    }
  }
};
</script>

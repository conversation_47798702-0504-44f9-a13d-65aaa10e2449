<template>
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">Register</h3>
        </div>
        <div class="card-body">
          <form @submit.prevent="register">
            <div class="mb-3">
              <label for="username" class="form-label">Username</label>
              <input
                id="username"
                v-model="username"
                type="text"
                class="form-control"
                placeholder="Choose a username"
                required
              />
              <small class="form-text text-muted">Username must be at least 3 characters long.</small>
            </div>
            <div class="mb-3">
              <label for="password" class="form-label">Password</label>
              <input
                id="password"
                v-model="password"
                type="password"
                class="form-control"
                placeholder="Choose a password"
                required
              />
              <small class="form-text text-muted">Password must be at least 6 characters long.</small>
            </div>
            <div class="mb-3">
              <label for="confirmPassword" class="form-label">Confirm Password</label>
              <input
                id="confirmPassword"
                v-model="confirmPassword"
                type="password"
                class="form-control"
                placeholder="Confirm your password"
                required
              />
            </div>
            <div class="mb-3">
              <label for="email" class="form-label">Email (optional)</label>
              <input
                id="email"
                v-model="email"
                type="email"
                class="form-control"
                placeholder="Enter your email"
              />
            </div>
            <div v-if="error" class="alert alert-danger mt-3">
              {{ error }}
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="loading"
              >
                <span v-if="loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Register
              </button>
              <router-link to="/login" class="text-decoration-none">Already have an account? Login</router-link>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RegisterView',
  data() {
    return {
      username: '',
      password: '',
      confirmPassword: '',
      email: '',
      loading: false,
      error: null
    };
  },
  methods: {
    async register() {
      // Validate form
      if (this.username.length < 3) {
        this.error = 'Username must be at least 3 characters long';
        return;
      }

      if (this.password.length < 6) {
        this.error = 'Password must be at least 6 characters long';
        return;
      }

      if (this.password !== this.confirmPassword) {
        this.error = 'Passwords do not match';
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        const response = await fetch('/api/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            username: this.username,
            password: this.password,
            email: this.email || null
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Registration failed');
        }

        // Update store with authentication status and user data
        this.$store.commit('setAuthenticated', true);
        this.$store.commit('setUser', data.user);

        // Redirect to booking lists page
        this.$router.push('/booking-lists');
      } catch (error) {
        console.error('Registration error:', error);
        this.error = error.message || 'Registration failed. Please try again.';
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

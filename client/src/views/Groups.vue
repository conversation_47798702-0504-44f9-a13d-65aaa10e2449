<template>
  <div>
    <h1 class="mb-4">My Groups</h1>
    
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Create New Group</h5>
          </div>
          <div class="card-body">
            <form @submit.prevent="createGroup">
              <div class="mb-3">
                <label for="groupName" class="form-label">Group Name</label>
                <input
                  id="groupName"
                  v-model="newGroupName"
                  type="text"
                  class="form-control"
                  placeholder="Enter group name"
                  required
                />
              </div>
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="creatingGroup"
              >
                <span v-if="creatingGroup" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Create Group
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">Loading your groups...</p>
    </div>
    
    <div v-else-if="error" class="alert alert-danger">
      {{ error }}
    </div>
    
    <div v-else-if="groups.length === 0" class="alert alert-info">
      You don't have any groups yet. Create a new group to get started.
    </div>
    
    <div v-else>
      <div v-for="group in groups" :key="group.id" class="card mb-4">
        <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
          <h5 class="mb-0">{{ group.name }}</h5>
        </div>
        <div class="card-body">
          <h6>Members:</h6>
          <ul class="list-group mb-3">
            <li v-for="member in group.members" :key="member.id" class="list-group-item d-flex justify-content-between align-items-center">
              {{ member.username }}
              <button
                class="btn btn-sm btn-danger"
                @click="removeMember(group.id, member.id)"
                :disabled="removingMember"
              >
                Remove
              </button>
            </li>
          </ul>
          
          <div class="mt-4">
            <h6>Add Member:</h6>
            <form @submit.prevent="addMember(group.id)">
              <div class="input-group">
                <input
                  type="text"
                  class="form-control"
                  placeholder="Enter username"
                  v-model="newMemberUsernames[group.id]"
                  required
                />
                <button
                  type="submit"
                  class="btn btn-primary"
                  :disabled="addingMember"
                >
                  <span v-if="addingMember && addingToGroupId === group.id" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Add
                </button>
              </div>
              <div v-if="memberErrors[group.id]" class="text-danger mt-2">
                {{ memberErrors[group.id] }}
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'GroupsView',
  data() {
    return {
      loading: true,
      error: null,
      newGroupName: '',
      creatingGroup: false,
      newMemberUsernames: {},
      addingMember: false,
      addingToGroupId: null,
      removingMember: false,
      memberErrors: {}
    };
  },
  computed: {
    ...mapGetters(['getGroups']),
    groups() {
      return this.getGroups;
    }
  },
  async created() {
    try {
      await this.$store.dispatch('fetchGroups');
      this.loading = false;
    } catch (error) {
      console.error('Error loading groups:', error);
      this.error = 'Failed to load groups. Please try again later.';
      this.loading = false;
    }
  },
  methods: {
    async createGroup() {
      if (!this.newGroupName) return;
      
      this.creatingGroup = true;
      
      try {
        await this.$store.dispatch('createGroup', this.newGroupName);
        this.newGroupName = '';
      } catch (error) {
        console.error('Error creating group:', error);
        this.error = 'Failed to create group. Please try again.';
      } finally {
        this.creatingGroup = false;
      }
    },
    async addMember(groupId) {
      const username = this.newMemberUsernames[groupId];
      if (!username) return;
      
      this.addingMember = true;
      this.addingToGroupId = groupId;
      this.memberErrors[groupId] = null;
      
      try {
        await this.$store.dispatch('addGroupMember', { groupId, username });
        this.newMemberUsernames[groupId] = '';
      } catch (error) {
        console.error('Error adding member:', error);
        this.memberErrors[groupId] = 'Failed to add member. Please check the username and try again.';
      } finally {
        this.addingMember = false;
        this.addingToGroupId = null;
      }
    },
    async removeMember(groupId, memberId) {
      this.removingMember = true;
      
      try {
        await this.$store.dispatch('removeGroupMember', { groupId, memberId });
      } catch (error) {
        console.error('Error removing member:', error);
        this.error = 'Failed to remove member. Please try again.';
      } finally {
        this.removingMember = false;
      }
    }
  }
};
</script>

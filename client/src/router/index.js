import { createRouter, createWebHistory } from 'vue-router';
import store from '../store';

// Import views
import Login from '../views/Login.vue';
import Register from '../views/Register.vue';
import BookingLists from '../views/BookingLists.vue';
import BookingListDetail from '../views/BookingListDetail.vue';
import ConfirmBooking from '../views/ConfirmBooking.vue';
import MyBookings from '../views/MyBookings.vue';
import Groups from '../views/Groups.vue';
import AdminLogin from '../views/AdminLogin.vue';
import AdminDashboard from '../views/AdminDashboard.vue';
import AdminBookingLists from '../views/AdminBookingLists.vue';
import AdminBookingListDetail from '../views/AdminBookingListDetail.vue';

const routes = [
  {
    path: '/',
    redirect: '/booking-lists'
  },
  {
    path: '/login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    component: Register,
    meta: { requiresGuest: true }
  },
  {
    path: '/booking-lists',
    component: BookingLists,
    meta: { requiresAuth: true }
  },
  {
    path: '/booking-lists/:id',
    component: BookingListDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/confirm-booking/:id',
    component: ConfirmBooking,
    meta: { requiresAuth: true }
  },
  {
    path: '/my-bookings',
    component: MyBookings,
    meta: { requiresAuth: true }
  },
  {
    path: '/groups',
    component: Groups,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/login',
    component: AdminLogin,
    meta: { requiresGuest: true }
  },
  {
    path: '/admin',
    component: AdminDashboard,
    meta: { requiresAdmin: true }
  },
  {
    path: '/admin/booking-lists',
    component: AdminBookingLists,
    meta: { requiresAdmin: true }
  },
  {
    path: '/admin/booking-lists/:id',
    component: AdminBookingListDetail,
    meta: { requiresAdmin: true }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// Navigation guards
router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters.isAuthenticated;
  const isAdminAuthenticated = store.getters.isAdminAuthenticated;

  // Check if the route requires authentication
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login');
  } 
  // Check if the route requires admin authentication
  else if (to.meta.requiresAdmin && !isAdminAuthenticated) {
    next('/admin/login');
  }
  // Check if the route requires guest (not authenticated)
  else if (to.meta.requiresGuest && isAuthenticated) {
    next('/booking-lists');
  }
  // Otherwise, proceed to the route
  else {
    next();
  }
});

export default router;

<template>
  <nav class="navbar navbar-expand-md navbar-dark bg-dark">
    <button
      class="navbar-toggler mx-2 mb-2"
      type="button"
      data-bs-toggle="collapse"
      data-bs-target="#navbarNav"
    >
      <span class="navbar-toggler-icon"></span>
    </button>
    <div id="navbarNav" class="collapse navbar-collapse mx-2">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" href="#" @click="redirect('/booking-lists')">Booking Lists</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#" @click="redirect('/groups')">My Groups</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#" @click="redirect('/my-bookings')">My Bookings</a>
        </li>
        <li class="nav-item" v-if="!isAuthenticated">
          <a class="nav-link" href="#" @click="redirect('/login')">Login</a>
        </li>
        <li class="nav-item" v-if="!isAuthenticated">
          <a class="nav-link" href="#" @click="redirect('/register')">Register</a>
        </li>
        <li class="nav-item" v-if="isAuthenticated">
          <a class="nav-link" href="#" @click="logout">Logout</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#" @click="redirect('/admin/login')">Admin</a>
        </li>
      </ul>
    </div>
  </nav>
  <section class="container-fluid py-4">
    <router-view />
  </section>
</template>

<script>
import { io } from 'socket.io-client';
import { mapGetters } from 'vuex';

export default {
  name: 'App',
  data() {
    return {
      socket: io({
        withCredentials: true,
        transports: ['websocket', 'polling']
      }),
      activityTimeout: null,
      inactivityLimit: 60 * 60 * 1000 // 60 minutes
    };
  },
  computed: {
    ...mapGetters(['isAuthenticated'])
  },
  provide() {
    return {
      socket: this.socket
    };
  },
  created() {
    const { commit } = this.$store;

    // Make socket available globally
    this.$root.socket = this.socket;

    // Set up socket error handling
    this.socket.on('connect_error', (error) => {
      console.error('Socket.io connection error:', error);
    });

    // Check authentication status
    fetch('/api/users/me', {
      credentials: 'include'
    })
      .then((res) => res.json())
      .then(({ authenticated, user }) => {
        commit('setAuthenticated', authenticated);
        if (authenticated && user) {
          commit('setUser', user);
          this.initActivityTracker();
        }
      })
      .catch(error => {
        console.error('Error checking authentication status:', error);
      });
  },
  methods: {
    redirect(path) {
      this.$router.push(path);
    },
    logout() {
      fetch('/api/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Logout failed');
          }
          return response.json();
        })
        .then(() => {
          this.$store.commit('setAuthenticated', false);
          this.$store.commit('setUser', null);
          this.$router.push('/login');
        })
        .catch(error => {
          console.error('Error during logout:', error);
        });
    },
    initActivityTracker() {
      // Reset the activity timeout whenever there's user activity
      const resetActivityTimeout = () => {
        if (this.activityTimeout) {
          clearTimeout(this.activityTimeout);
        }
        this.activityTimeout = setTimeout(this.handleInactivityLogout, this.inactivityLimit);
      };

      // Set up event listeners for user activity
      ['mousedown', 'keydown', 'touchstart', 'scroll'].forEach(eventName => {
        window.addEventListener(eventName, resetActivityTimeout);
      });

      // Initial timeout
      resetActivityTimeout();
    },
    handleInactivityLogout() {
      console.log('Logging out due to inactivity');

      // Call the logout API
      fetch('/api/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })
        .then(() => {
          // Update authentication state
          this.$store.commit('setAuthenticated', false);
          this.$store.commit('setUser', null);

          // Redirect to login page
          this.$router.push('/login');

          // Show alert to user
          alert('You have been logged out due to inactivity.');
        })
        .catch((error) => {
          console.error('Error during inactivity logout:', error);
        });
    }
  }
};
</script>

<style>
@import url('bootstrap/dist/css/bootstrap.css');

html,
body {
  background-color: #f5f5f5;
}

.card {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: #3498db;
  border-color: #3498db;
}

.btn-primary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
}

.btn-success {
  background-color: #2ecc71;
  border-color: #2ecc71;
}

.btn-success:hover {
  background-color: #27ae60;
  border-color: #27ae60;
}

.btn-danger {
  background-color: #e74c3c;
  border-color: #e74c3c;
}

.btn-danger:hover {
  background-color: #c0392b;
  border-color: #c0392b;
}
</style>

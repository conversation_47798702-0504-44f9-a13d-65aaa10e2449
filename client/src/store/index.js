import { createStore } from 'vuex';

export default createStore({
  state: {
    authenticated: false,
    user: null,
    selectedTimeslot: null,
    adminAuthenticated: false,
    adminUsername: null,
    bookingLists: [],
    groups: []
  },
  getters: {
    isAuthenticated(state) {
      return state.authenticated;
    },
    getUser(state) {
      return state.user;
    },
    getSelectedTimeslot(state) {
      return state.selectedTimeslot;
    },
    isAdminAuthenticated(state) {
      return state.adminAuthenticated;
    },
    getAdminUsername(state) {
      return state.adminUsername;
    },
    getBookingLists(state) {
      return state.bookingLists;
    },
    getGroups(state) {
      return state.groups;
    }
  },
  mutations: {
    setAuthenticated(state, authenticated) {
      state.authenticated = authenticated;
    },
    setUser(state, user) {
      state.user = user;
    },
    setSelectedTimeslot(state, timeslot) {
      state.selectedTimeslot = timeslot;
    },
    setAdminAuthenticated(state, authenticated) {
      state.adminAuthenticated = authenticated;
    },
    setAdminUsername(state, username) {
      state.adminUsername = username;
    },
    setBookingLists(state, bookingLists) {
      state.bookingLists = bookingLists;
    },
    setGroups(state, groups) {
      state.groups = groups;
    },
    addGroup(state, group) {
      state.groups.push(group);
    },
    updateGroup(state, updatedGroup) {
      const index = state.groups.findIndex(g => g.id === updatedGroup.id);
      if (index !== -1) {
        state.groups.splice(index, 1, updatedGroup);
      }
    },
    removeGroup(state, groupId) {
      state.groups = state.groups.filter(g => g.id !== groupId);
    }
  },
  actions: {
    async fetchBookingLists({ commit }) {
      try {
        const response = await fetch('/api/booking-lists');
        const data = await response.json();
        commit('setBookingLists', data.bookingLists);
        return data.bookingLists;
      } catch (error) {
        console.error('Error fetching booking lists:', error);
        throw error;
      }
    },
    async fetchGroups({ commit }) {
      try {
        const response = await fetch('/api/groups');
        const data = await response.json();
        commit('setGroups', data.groups);
        return data.groups;
      } catch (error) {
        console.error('Error fetching groups:', error);
        throw error;
      }
    },
    async createGroup({ commit }, groupName) {
      try {
        const response = await fetch('/api/groups', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ name: groupName })
        });
        const group = await response.json();
        commit('addGroup', group);
        return group;
      } catch (error) {
        console.error('Error creating group:', error);
        throw error;
      }
    },
    async addGroupMember({ commit }, { groupId, username }) {
      try {
        const response = await fetch(`/api/groups/${groupId}/members`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ username })
        });
        const updatedGroup = await response.json();
        commit('updateGroup', updatedGroup);
        return updatedGroup;
      } catch (error) {
        console.error('Error adding group member:', error);
        throw error;
      }
    },
    async removeGroupMember({ commit }, { groupId, memberId }) {
      try {
        const response = await fetch(`/api/groups/${groupId}/members/${memberId}`, {
          method: 'DELETE'
        });
        const result = await response.json();
        
        if (result.message && result.message.includes('empty group deleted')) {
          commit('removeGroup', groupId);
        } else {
          commit('updateGroup', result);
        }
        
        return result;
      } catch (error) {
        console.error('Error removing group member:', error);
        throw error;
      }
    }
  }
});

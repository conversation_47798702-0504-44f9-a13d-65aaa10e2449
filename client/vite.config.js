import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8989',
        changeOrigin: true,
        secure: false,
        withCredentials: true
      },
      '/socket.io': {
        target: 'http://localhost:8989',
        changeOrigin: true,
        secure: false,
        ws: true,
        withCredentials: true
      }
    }
  }
});

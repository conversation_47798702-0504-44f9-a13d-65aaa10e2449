import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    https: false, // Set to true if you want to serve client over HTTPS too
    proxy: {
      '/api': {
        target: process.env.HTTPS_ENABLED === 'true' ? 'https://localhost:8990' : 'http://localhost:8989',
        changeOrigin: true,
        secure: false, // Allow self-signed certificates
        withCredentials: true
      },
      '/socket.io': {
        target: process.env.HTTPS_ENABLED === 'true' ? 'https://localhost:8990' : 'http://localhost:8989',
        changeOrigin: true,
        secure: false, // Allow self-signed certificates
        ws: true,
        withCredentials: true
      }
    }
  }
});

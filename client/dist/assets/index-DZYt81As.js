(function(){const e=document.createElement('link').relList;if(e&&e.supports&&e.supports('modulepreload'))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))n(o);new MutationObserver(o => {for(const i of o)if(i.type==='childList')for(const r of i.addedNodes)r.tagName==='LINK'&&r.rel==='modulepreload'&&n(r);}).observe(document,{childList:!0,subtree:!0});function s(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==='use-credentials'?i.credentials='include':o.crossOrigin==='anonymous'?i.credentials='omit':i.credentials='same-origin',i;}function n(o){if(o.ep)return;o.ep=!0;const i=s(o);fetch(o.href,i);}})();/**
* @vue/shared v3.5.14
* (c) 2018-present <PERSON><PERSON> (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function lo(t){const e=Object.create(null);for(const s of t.split(','))e[s]=1;return s => s in e;}const ie={},Kt=[],et=() => {},Al=() => !1,ln=t => t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&(t.charCodeAt(2)>122||t.charCodeAt(2)<97),ao=t => t.startsWith('onUpdate:'),ke=Object.assign,co=(t,e) => {const s=t.indexOf(e);s>-1&&t.splice(s,1);},Bl=Object.prototype.hasOwnProperty,ee=(t,e) => Bl.call(t,e),F=Array.isArray,Wt=t => Rs(t)==='[object Map]',an=t => Rs(t)==='[object Set]',Uo=t => Rs(t)==='[object Date]',G=t => typeof t=='function',he=t => typeof t=='string',st=t => typeof t=='symbol',ce=t => t!==null&&typeof t=='object',ji=t => (ce(t)||G(t))&&G(t.then)&&G(t.catch),Fi=Object.prototype.toString,Rs=t => Fi.call(t),Rl=t => Rs(t).slice(8,-1),Vi=t => Rs(t)==='[object Object]',uo=t => he(t)&&t!=='NaN'&&t[0]!=='-'&&''+parseInt(t,10)===t,gs=lo(',key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted'),cn=t => {const e=Object.create(null);return s => e[s]||(e[s]=t(s));},Dl=/-(\w)/g,Me=cn(t => t.replace(Dl,(e,s) => s?s.toUpperCase():'')),Ol=/\B([A-Z])/g,Ot=cn(t => t.replace(Ol,'-$1').toLowerCase()),un=cn(t => t.charAt(0).toUpperCase()+t.slice(1)),wn=cn(t => t?`on${un(t)}`:''),xt=(t,e) => !Object.is(t,e),Vs=(t,...e) => {for(let s=0;s<t.length;s++)t[s](...e);},Gi=(t,e,s,n=!1) => {Object.defineProperty(t,e,{configurable:!0,enumerable:!1,writable:n,value:s});},Js=t => {const e=parseFloat(t);return isNaN(e)?t:e;};let $o;const dn=() => $o||($o=typeof globalThis<'u'?globalThis:typeof self<'u'?self:typeof window<'u'?window:typeof global<'u'?global:{});function fo(t){if(F(t)){const e={};for(let s=0;s<t.length;s++){const n=t[s],o=he(n)?Ml(n):fo(n);if(o)for(const i in o)e[i]=o[i];}return e;}else if(he(t)||ce(t))return t;}const Pl=/;(?![^(]*\))/g,Nl=/:([^]+)/,Il=/\/\*[^]*?\*\//g;function Ml(t){const e={};return t.replace(Il,'').split(Pl).forEach(s => {if(s){const n=s.split(Nl);n.length>1&&(e[n[0].trim()]=n[1].trim());}}),e;}function Ds(t){let e='';if(he(t))e=t;else if(F(t))for(let s=0;s<t.length;s++){const n=Ds(t[s]);n&&(e+=n+' ');}else if(ce(t))for(const s in t)t[s]&&(e+=s+' ');return e.trim();}const Ul='itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly',$l=lo(Ul);function qi(t){return!!t||t==='';}function jl(t,e){if(t.length!==e.length)return!1;let s=!0;for(let n=0;s&&n<t.length;n++)s=es(t[n],e[n]);return s;}function es(t,e){if(t===e)return!0;let s=Uo(t),n=Uo(e);if(s||n)return s&&n?t.getTime()===e.getTime():!1;if(s=st(t),n=st(e),s||n)return t===e;if(s=F(t),n=F(e),s||n)return s&&n?jl(t,e):!1;if(s=ce(t),n=ce(e),s||n){if(!s||!n)return!1;const o=Object.keys(t).length,i=Object.keys(e).length;if(o!==i)return!1;for(const r in t){const l=t.hasOwnProperty(r),c=e.hasOwnProperty(r);if(l&&!c||!l&&c||!es(t[r],e[r]))return!1;}}return String(t)===String(e);}function Fl(t,e){return t.findIndex(s => es(s,e));}const Hi=t => !!(t&&t.__v_isRef===!0),T=t => he(t)?t:t==null?'':F(t)||ce(t)&&(t.toString===Fi||!G(t.toString))?Hi(t)?T(t.value):JSON.stringify(t,Ki,2):String(t),Ki=(t,e) => Hi(e)?Ki(t,e.value):Wt(e)?{[`Map(${e.size})`]:[...e.entries()].reduce((s,[n,o],i) => (s[En(n,i)+' =>']=o,s),{})}:an(e)?{[`Set(${e.size})`]:[...e.values()].map(s => En(s))}:st(e)?En(e):ce(e)&&!F(e)&&!Vi(e)?String(e):e,En=(t,e='') => {let s;return st(t)?`Symbol(${(s=t.description)!=null?s:e})`:t;};/**
* @vue/reactivity v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Se;class Wi{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Se,!e&&Se&&(this.index=(Se.scopes||(Se.scopes=[])).push(this)-1);}get active(){return this._active;}pause(){if(this._active){this._isPaused=!0;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].pause();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].pause();}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].resume();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].resume();}}run(e){if(this._active){const s=Se;try{return Se=this,e();}finally{Se=s;}}}on(){++this._on===1&&(this.prevScope=Se,Se=this);}off(){this._on>0&&--this._on===0&&(Se=this.prevScope,this.prevScope=void 0);}stop(e){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0;}if(!this.detached&&this.parent&&!e){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index);}this.parent=void 0;}}}function Vl(t){return new Wi(t);}function Gl(){return Se;}let le;const xn=new WeakSet;class zi{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Se&&Se.active&&Se.effects.push(this);}pause(){this.flags|=64;}resume(){this.flags&64&&(this.flags&=-65,xn.has(this)&&(xn.delete(this),this.trigger()));}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ji(this);}run(){if(!(this.flags&1))return this.fn();this.flags|=2,jo(this),Qi(this);const e=le,s=je;le=this,je=!0;try{return this.fn();}finally{Xi(this),le=e,je=s,this.flags&=-3;}}stop(){if(this.flags&1){for(let e=this.deps;e;e=e.nextDep)go(e);this.deps=this.depsTail=void 0,jo(this),this.onStop&&this.onStop(),this.flags&=-2;}}trigger(){this.flags&64?xn.add(this):this.scheduler?this.scheduler():this.runIfDirty();}runIfDirty(){$n(this)&&this.run();}get dirty(){return $n(this);}}let Yi=0,ms,ys;function Ji(t,e=!1){if(t.flags|=8,e){t.next=ys,ys=t;return;}t.next=ms,ms=t;}function ho(){Yi++;}function po(){if(--Yi>0)return;if(ys){let e=ys;for(ys=void 0;e;){const s=e.next;e.next=void 0,e.flags&=-9,e=s;}}let t;for(;ms;){let e=ms;for(ms=void 0;e;){const s=e.next;if(e.next=void 0,e.flags&=-9,e.flags&1)try{e.trigger();}catch(n){t||(t=n);}e=s;}}if(t)throw t;}function Qi(t){for(let e=t.deps;e;e=e.nextDep)e.version=-1,e.prevActiveLink=e.dep.activeLink,e.dep.activeLink=e;}function Xi(t){let e,s=t.depsTail,n=s;for(;n;){const o=n.prevDep;n.version===-1?(n===s&&(s=o),go(n),ql(n)):e=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=o;}t.deps=e,t.depsTail=s;}function $n(t){for(let e=t.deps;e;e=e.nextDep)if(e.dep.version!==e.version||e.dep.computed&&(Zi(e.dep.computed)||e.dep.version!==e.version))return!0;return!!t._dirty;}function Zi(t){if(t.flags&4&&!(t.flags&16)||(t.flags&=-17,t.globalVersion===Es)||(t.globalVersion=Es,!t.isSSR&&t.flags&128&&(!t.deps&&!t._dirty||!$n(t))))return;t.flags|=2;const e=t.dep,s=le,n=je;le=t,je=!0;try{Qi(t);const o=t.fn(t._value);(e.version===0||xt(o,t._value))&&(t.flags|=128,t._value=o,e.version++);}catch(o){throw e.version++,o;}finally{le=s,je=n,Xi(t),t.flags&=-3;}}function go(t,e=!1){const{dep:s,prevSub:n,nextSub:o}=t;if(n&&(n.nextSub=o,t.prevSub=void 0),o&&(o.prevSub=n,t.nextSub=void 0),s.subs===t&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let i=s.computed.deps;i;i=i.nextDep)go(i,!0);}!e&&!--s.sc&&s.map&&s.map.delete(s.key);}function ql(t){const{prevDep:e,nextDep:s}=t;e&&(e.nextDep=s,t.prevDep=void 0),s&&(s.prevDep=e,t.nextDep=void 0);}let je=!0;const er=[];function ht(){er.push(je),je=!1;}function pt(){const t=er.pop();je=t===void 0?!0:t;}function jo(t){const{cleanup:e}=t;if(t.cleanup=void 0,e){const s=le;le=void 0;try{e();}finally{le=s;}}}let Es=0;class Hl{constructor(e,s){this.sub=e,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0;}}class mo{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0;}track(e){if(!le||!je||le===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==le)s=this.activeLink=new Hl(le,this),le.deps?(s.prevDep=le.depsTail,le.depsTail.nextDep=s,le.depsTail=s):le.deps=le.depsTail=s,tr(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=le.depsTail,s.nextDep=void 0,le.depsTail.nextDep=s,le.depsTail=s,le.deps===s&&(le.deps=n);}return s;}trigger(e){this.version++,Es++,this.notify(e);}notify(e){ho();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify();}finally{po();}}}function tr(t){if(t.dep.sc++,t.sub.flags&4){const e=t.dep.computed;if(e&&!t.dep.subs){e.flags|=20;for(let n=e.deps;n;n=n.nextDep)tr(n);}const s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t;}}const jn=new WeakMap,Dt=Symbol(''),Fn=Symbol(''),xs=Symbol('');function ye(t,e,s){if(je&&le){let n=jn.get(t);n||jn.set(t,n=new Map);let o=n.get(s);o||(n.set(s,o=new mo),o.map=n,o.key=s),o.track();}}function ct(t,e,s,n,o,i){const r=jn.get(t);if(!r){Es++;return;}const l=c => {c&&c.trigger();};if(ho(),e==='clear')r.forEach(l);else{const c=F(t),h=c&&uo(s);if(c&&s==='length'){const u=Number(n);r.forEach((f,y) => {(y==='length'||y===xs||!st(y)&&y>=u)&&l(f);});}else switch((s!==void 0||r.has(void 0))&&l(r.get(s)),h&&l(r.get(xs)),e){case'add':c?h&&l(r.get('length')):(l(r.get(Dt)),Wt(t)&&l(r.get(Fn)));break;case'delete':c||(l(r.get(Dt)),Wt(t)&&l(r.get(Fn)));break;case'set':Wt(t)&&l(r.get(Dt));break;}}po();}function Ft(t){const e=Z(t);return e===t?e:(ye(e,'iterate',xs),Ie(t)?e:e.map(me));}function fn(t){return ye(t=Z(t),'iterate',xs),t;}const Kl={__proto__:null,[Symbol.iterator](){return Tn(this,Symbol.iterator,me);},concat(...t){return Ft(this).concat(...t.map(e => F(e)?Ft(e):e));},entries(){return Tn(this,'entries',t => (t[1]=me(t[1]),t));},every(t,e){return rt(this,'every',t,e,void 0,arguments);},filter(t,e){return rt(this,'filter',t,e,s => s.map(me),arguments);},find(t,e){return rt(this,'find',t,e,me,arguments);},findIndex(t,e){return rt(this,'findIndex',t,e,void 0,arguments);},findLast(t,e){return rt(this,'findLast',t,e,me,arguments);},findLastIndex(t,e){return rt(this,'findLastIndex',t,e,void 0,arguments);},forEach(t,e){return rt(this,'forEach',t,e,void 0,arguments);},includes(...t){return Sn(this,'includes',t);},indexOf(...t){return Sn(this,'indexOf',t);},join(t){return Ft(this).join(t);},lastIndexOf(...t){return Sn(this,'lastIndexOf',t);},map(t,e){return rt(this,'map',t,e,void 0,arguments);},pop(){return cs(this,'pop');},push(...t){return cs(this,'push',t);},reduce(t,...e){return Fo(this,'reduce',t,e);},reduceRight(t,...e){return Fo(this,'reduceRight',t,e);},shift(){return cs(this,'shift');},some(t,e){return rt(this,'some',t,e,void 0,arguments);},splice(...t){return cs(this,'splice',t);},toReversed(){return Ft(this).toReversed();},toSorted(t){return Ft(this).toSorted(t);},toSpliced(...t){return Ft(this).toSpliced(...t);},unshift(...t){return cs(this,'unshift',t);},values(){return Tn(this,'values',me);}};function Tn(t,e,s){const n=fn(t),o=n[e]();return n!==t&&!Ie(t)&&(o._next=o.next,o.next=() => {const i=o._next();return i.value&&(i.value=s(i.value)),i;}),o;}const Wl=Array.prototype;function rt(t,e,s,n,o,i){const r=fn(t),l=r!==t&&!Ie(t),c=r[e];if(c!==Wl[e]){const f=c.apply(t,i);return l?me(f):f;}let h=s;r!==t&&(l?h=function(f,y){return s.call(this,me(f),y,t);}:s.length>2&&(h=function(f,y){return s.call(this,f,y,t);}));const u=c.call(r,h,n);return l&&o?o(u):u;}function Fo(t,e,s,n){const o=fn(t);let i=s;return o!==t&&(Ie(t)?s.length>3&&(i=function(r,l,c){return s.call(this,r,l,c,t);}):i=function(r,l,c){return s.call(this,r,me(l),c,t);}),o[e](i,...n);}function Sn(t,e,s){const n=Z(t);ye(n,'iterate',xs);const o=n[e](...s);return(o===-1||o===!1)&&_o(s[0])?(s[0]=Z(s[0]),n[e](...s)):o;}function cs(t,e,s=[]){ht(),ho();const n=Z(t)[e].apply(t,s);return po(),pt(),n;}const zl=lo('__proto__,__v_isRef,__isVue'),sr=new Set(Object.getOwnPropertyNames(Symbol).filter(t => t!=='arguments'&&t!=='caller').map(t => Symbol[t]).filter(st));function Yl(t){st(t)||(t=String(t));const e=Z(this);return ye(e,'has',t),e.hasOwnProperty(t);}class nr{constructor(e=!1,s=!1){this._isReadonly=e,this._isShallow=s;}get(e,s,n){if(s==='__v_skip')return e.__v_skip;const o=this._isReadonly,i=this._isShallow;if(s==='__v_isReactive')return!o;if(s==='__v_isReadonly')return o;if(s==='__v_isShallow')return i;if(s==='__v_raw')return n===(o?i?ia:lr:i?rr:ir).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const r=F(e);if(!o){let c;if(r&&(c=Kl[s]))return c;if(s==='hasOwnProperty')return Yl;}const l=Reflect.get(e,s,ve(e)?e:n);return(st(s)?sr.has(s):zl(s))||(o||ye(e,'get',s),i)?l:ve(l)?r&&uo(s)?l:l.value:ce(l)?o?cr(l):Os(l):l;}}class or extends nr{constructor(e=!1){super(!1,e);}set(e,s,n,o){let i=e[s];if(!this._isShallow){const c=St(i);if(!Ie(n)&&!St(n)&&(i=Z(i),n=Z(n)),!F(e)&&ve(i)&&!ve(n))return c?!1:(i.value=n,!0);}const r=F(e)&&uo(s)?Number(s)<e.length:ee(e,s),l=Reflect.set(e,s,n,ve(e)?e:o);return e===Z(o)&&(r?xt(n,i)&&ct(e,'set',s,n):ct(e,'add',s,n)),l;}deleteProperty(e,s){const n=ee(e,s);e[s];const o=Reflect.deleteProperty(e,s);return o&&n&&ct(e,'delete',s,void 0),o;}has(e,s){const n=Reflect.has(e,s);return(!st(s)||!sr.has(s))&&ye(e,'has',s),n;}ownKeys(e){return ye(e,'iterate',F(e)?'length':Dt),Reflect.ownKeys(e);}}class Jl extends nr{constructor(e=!1){super(!0,e);}set(e,s){return!0;}deleteProperty(e,s){return!0;}}const Ql=new or,Xl=new Jl,Zl=new or(!0);const Vn=t => t,Ms=t => Reflect.getPrototypeOf(t);function ea(t,e,s){return function(...n){const o=this.__v_raw,i=Z(o),r=Wt(i),l=t==='entries'||t===Symbol.iterator&&r,c=t==='keys'&&r,h=o[t](...n),u=s?Vn:e?Qs:me;return!e&&ye(i,'iterate',c?Fn:Dt),{next(){const{value:f,done:y}=h.next();return y?{value:f,done:y}:{value:l?[u(f[0]),u(f[1])]:u(f),done:y};},[Symbol.iterator](){return this;}};};}function Us(t){return function(...e){return t==='delete'?!1:t==='clear'?void 0:this;};}function ta(t,e){const s={get(o){const i=this.__v_raw,r=Z(i),l=Z(o);t||(xt(o,l)&&ye(r,'get',o),ye(r,'get',l));const{has:c}=Ms(r),h=e?Vn:t?Qs:me;if(c.call(r,o))return h(i.get(o));if(c.call(r,l))return h(i.get(l));i!==r&&i.get(o);},get size(){const o=this.__v_raw;return!t&&ye(Z(o),'iterate',Dt),Reflect.get(o,'size',o);},has(o){const i=this.__v_raw,r=Z(i),l=Z(o);return t||(xt(o,l)&&ye(r,'has',o),ye(r,'has',l)),o===l?i.has(o):i.has(o)||i.has(l);},forEach(o,i){const r=this,l=r.__v_raw,c=Z(l),h=e?Vn:t?Qs:me;return!t&&ye(c,'iterate',Dt),l.forEach((u,f) => o.call(i,h(u),h(f),r));}};return ke(s,t?{add:Us('add'),set:Us('set'),delete:Us('delete'),clear:Us('clear')}:{add(o){!e&&!Ie(o)&&!St(o)&&(o=Z(o));const i=Z(this);return Ms(i).has.call(i,o)||(i.add(o),ct(i,'add',o,o)),this;},set(o,i){!e&&!Ie(i)&&!St(i)&&(i=Z(i));const r=Z(this),{has:l,get:c}=Ms(r);let h=l.call(r,o);h||(o=Z(o),h=l.call(r,o));const u=c.call(r,o);return r.set(o,i),h?xt(i,u)&&ct(r,'set',o,i):ct(r,'add',o,i),this;},delete(o){const i=Z(this),{has:r,get:l}=Ms(i);let c=r.call(i,o);c||(o=Z(o),c=r.call(i,o)),l&&l.call(i,o);const h=i.delete(o);return c&&ct(i,'delete',o,void 0),h;},clear(){const o=Z(this),i=o.size!==0,r=o.clear();return i&&ct(o,'clear',void 0,void 0),r;}}),['keys','values','entries',Symbol.iterator].forEach(o => {s[o]=ea(o,t,e);}),s;}function yo(t,e){const s=ta(t,e);return(n,o,i) => o==='__v_isReactive'?!t:o==='__v_isReadonly'?t:o==='__v_raw'?n:Reflect.get(ee(s,o)&&o in n?s:n,o,i);}const sa={get:yo(!1,!1)},na={get:yo(!1,!0)},oa={get:yo(!0,!1)};const ir=new WeakMap,rr=new WeakMap,lr=new WeakMap,ia=new WeakMap;function ra(t){switch(t){case'Object':case'Array':return 1;case'Map':case'Set':case'WeakMap':case'WeakSet':return 2;default:return 0;}}function la(t){return t.__v_skip||!Object.isExtensible(t)?0:ra(Rl(t));}function Os(t){return St(t)?t:bo(t,!1,Ql,sa,ir);}function ar(t){return bo(t,!1,Zl,na,rr);}function cr(t){return bo(t,!0,Xl,oa,lr);}function bo(t,e,s,n,o){if(!ce(t)||t.__v_raw&&!(e&&t.__v_isReactive))return t;const i=la(t);if(i===0)return t;const r=o.get(t);if(r)return r;const l=new Proxy(t,i===2?n:s);return o.set(t,l),l;}function zt(t){return St(t)?zt(t.__v_raw):!!(t&&t.__v_isReactive);}function St(t){return!!(t&&t.__v_isReadonly);}function Ie(t){return!!(t&&t.__v_isShallow);}function _o(t){return t?!!t.__v_raw:!1;}function Z(t){const e=t&&t.__v_raw;return e?Z(e):t;}function aa(t){return!ee(t,'__v_skip')&&Object.isExtensible(t)&&Gi(t,'__v_skip',!0),t;}const me=t => ce(t)?Os(t):t,Qs=t => ce(t)?cr(t):t;function ve(t){return t?t.__v_isRef===!0:!1;}function ca(t){return ur(t,!1);}function ua(t){return ur(t,!0);}function ur(t,e){return ve(t)?t:new da(t,e);}class da{constructor(e,s){this.dep=new mo,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?e:Z(e),this._value=s?e:me(e),this.__v_isShallow=s;}get value(){return this.dep.track(),this._value;}set value(e){const s=this._rawValue,n=this.__v_isShallow||Ie(e)||St(e);e=n?e:Z(e),xt(e,s)&&(this._rawValue=e,this._value=n?e:me(e),this.dep.trigger());}}function Yt(t){return ve(t)?t.value:t;}const fa={get:(t,e,s) => e==='__v_raw'?t:Yt(Reflect.get(t,e,s)),set:(t,e,s,n) => {const o=t[e];return ve(o)&&!ve(s)?(o.value=s,!0):Reflect.set(t,e,s,n);}};function dr(t){return zt(t)?t:new Proxy(t,fa);}class ha{constructor(e,s,n){this.fn=e,this.setter=s,this._value=void 0,this.dep=new mo(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Es-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n;}notify(){if(this.flags|=16,!(this.flags&8)&&le!==this)return Ji(this,!0),!0;}get value(){const e=this.dep.track();return Zi(this),e&&(e.version=this.dep.version),this._value;}set value(e){this.setter&&this.setter(e);}}function pa(t,e,s=!1){let n,o;return G(t)?n=t:(n=t.get,o=t.set),new ha(n,o,s);}const $s={},Xs=new WeakMap;let Rt;function ga(t,e=!1,s=Rt){if(s){let n=Xs.get(s);n||Xs.set(s,n=[]),n.push(t);}}function ma(t,e,s=ie){const{immediate:n,deep:o,once:i,scheduler:r,augmentJob:l,call:c}=s,h=P => o?P:Ie(P)||o===!1||o===0?ut(P,1):ut(P);let u,f,y,_,D=!1,O=!1;if(ve(t)?(f=() => t.value,D=Ie(t)):zt(t)?(f=() => h(t),D=!0):F(t)?(O=!0,D=t.some(P => zt(P)||Ie(P)),f=() => t.map(P => {if(ve(P))return P.value;if(zt(P))return h(P);if(G(P))return c?c(P,2):P();})):G(t)?e?f=c?() => c(t,2):t:f=() => {if(y){ht();try{y();}finally{pt();}}const P=Rt;Rt=u;try{return c?c(t,3,[_]):t(_);}finally{Rt=P;}}:f=et,e&&o){const P=f,ne=o===!0?1/0:o;f=() => ut(P(),ne);}const q=Gl(),$=() => {u.stop(),q&&q.active&&co(q.effects,u);};if(i&&e){const P=e;e=(...ne) => {P(...ne),$();};}let I=O?new Array(t.length).fill($s):$s;const j=P => {if(!(!(u.flags&1)||!u.dirty&&!P))if(e){const ne=u.run();if(o||D||(O?ne.some((ge,ue) => xt(ge,I[ue])):xt(ne,I))){y&&y();const ge=Rt;Rt=u;try{const ue=[ne,I===$s?void 0:O&&I[0]===$s?[]:I,_];c?c(e,3,ue):e(...ue),I=ne;}finally{Rt=ge;}}}else u.run();};return l&&l(j),u=new zi(f),u.scheduler=r?() => r(j,!1):j,_=P => ga(P,!1,u),y=u.onStop=() => {const P=Xs.get(u);if(P){if(c)c(P,4);else for(const ne of P)ne();Xs.delete(u);}},e?n?j(!0):I=u.run():r?r(j.bind(null,!0),!0):u.run(),$.pause=u.pause.bind(u),$.resume=u.resume.bind(u),$.stop=$,$;}function ut(t,e=1/0,s){if(e<=0||!ce(t)||t.__v_skip||(s=s||new Set,s.has(t)))return t;if(s.add(t),e--,ve(t))ut(t.value,e,s);else if(F(t))for(let n=0;n<t.length;n++)ut(t[n],e,s);else if(an(t)||Wt(t))t.forEach(n => {ut(n,e,s);});else if(Vi(t)){for(const n in t)ut(t[n],e,s);for(const n of Object.getOwnPropertySymbols(t))Object.prototype.propertyIsEnumerable.call(t,n)&&ut(t[n],e,s);}return t;}/**
* @vue/runtime-core v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ps(t,e,s,n){try{return n?t(...n):t();}catch(o){hn(o,e,s);}}function nt(t,e,s,n){if(G(t)){const o=Ps(t,e,s,n);return o&&ji(o)&&o.catch(i => {hn(i,e,s);}),o;}if(F(t)){const o=[];for(let i=0;i<t.length;i++)o.push(nt(t[i],e,s,n));return o;}}function hn(t,e,s,n=!0){const o=e?e.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:r}=e&&e.appContext.config||ie;if(e){let l=e.parent;const c=e.proxy,h=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const u=l.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](t,c,h)===!1)return;}l=l.parent;}if(i){ht(),Ps(i,null,10,[t,c,h]),pt();return;}}ya(t,s,o,n,r);}function ya(t,e,s,n=!0,o=!1){if(o)throw t;console.error(t);}const Ee=[];let Xe=-1;const Jt=[];let vt=null,qt=0;const fr=Promise.resolve();let Zs=null;function vo(t){const e=Zs||fr;return t?e.then(this?t.bind(this):t):e;}function ba(t){let e=Xe+1,s=Ee.length;for(;e<s;){const n=e+s>>>1,o=Ee[n],i=Ts(o);i<t||i===t&&o.flags&2?e=n+1:s=n;}return e;}function ko(t){if(!(t.flags&1)){const e=Ts(t),s=Ee[Ee.length-1];!s||!(t.flags&2)&&e>=Ts(s)?Ee.push(t):Ee.splice(ba(e),0,t),t.flags|=1,hr();}}function hr(){Zs||(Zs=fr.then(gr));}function _a(t){F(t)?Jt.push(...t):vt&&t.id===-1?vt.splice(qt+1,0,t):t.flags&1||(Jt.push(t),t.flags|=1),hr();}function Vo(t,e,s=Xe+1){for(;s<Ee.length;s++){const n=Ee[s];if(n&&n.flags&2){if(t&&n.id!==t.uid)continue;Ee.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2);}}}function pr(t){if(Jt.length){const e=[...new Set(Jt)].sort((s,n) => Ts(s)-Ts(n));if(Jt.length=0,vt){vt.push(...e);return;}for(vt=e,qt=0;qt<vt.length;qt++){const s=vt[qt];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2;}vt=null,qt=0;}}const Ts=t => t.id==null?t.flags&2?-1:1/0:t.id;function gr(t){try{for(Xe=0;Xe<Ee.length;Xe++){const e=Ee[Xe];e&&!(e.flags&8)&&(e.flags&4&&(e.flags&=-2),Ps(e,e.i,e.i?15:14),e.flags&4||(e.flags&=-2));}}finally{for(;Xe<Ee.length;Xe++){const e=Ee[Xe];e&&(e.flags&=-2);}Xe=-1,Ee.length=0,pr(),Zs=null,(Ee.length||Jt.length)&&gr();}}let Re=null,mr=null;function en(t){const e=Re;return Re=t,mr=t&&t.type.__scopeId||null,e;}function Pt(t,e=Re,s){if(!e||t._n)return t;const n=(...o) => {n._d&&Xo(-1);const i=en(e);let r;try{r=t(...o);}finally{en(i),n._d&&Xo(1);}return r;};return n._n=!0,n._c=!0,n._d=!0,n;}function H(t,e){if(Re===null)return t;const s=yn(Re),n=t.dirs||(t.dirs=[]);for(let o=0;o<e.length;o++){let[i,r,l,c=ie]=e[o];i&&(G(i)&&(i={mounted:i,updated:i}),i.deep&&ut(r),n.push({dir:i,instance:s,value:r,oldValue:void 0,arg:l,modifiers:c}));}return t;}function At(t,e,s,n){const o=t.dirs,i=e&&e.dirs;for(let r=0;r<o.length;r++){const l=o[r];i&&(l.oldValue=i[r].value);const c=l.dir[n];c&&(ht(),nt(c,s,8,[t.el,l,t,e]),pt());}}const va=Symbol('_vte'),ka=t => t.__isTeleport;function wo(t,e){t.shapeFlag&6&&t.component?(t.transition=e,wo(t.component.subTree,e)):t.shapeFlag&128?(t.ssContent.transition=e.clone(t.ssContent),t.ssFallback.transition=e.clone(t.ssFallback)):t.transition=e;}/*! #__NO_SIDE_EFFECTS__ */function yr(t,e){return G(t)?ke({name:t.name},e,{setup:t}):t;}function br(t){t.ids=[t.ids[0]+t.ids[2]+++'-',0,0];}function tn(t,e,s,n,o=!1){if(F(t)){t.forEach((D,O) => tn(D,e&&(F(e)?e[O]:e),s,n,o));return;}if(bs(n)&&!o){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&tn(t,e,s,n.component.subTree);return;}const i=n.shapeFlag&4?yn(n.component):n.el,r=o?null:i,{i:l,r:c}=t,h=e&&e.r,u=l.refs===ie?l.refs={}:l.refs,f=l.setupState,y=Z(f),_=f===ie?() => !1:D => ee(y,D);if(h!=null&&h!==c&&(he(h)?(u[h]=null,_(h)&&(f[h]=null)):ve(h)&&(h.value=null)),G(c))Ps(c,l,12,[r,u]);else{const D=he(c),O=ve(c);if(D||O){const q=() => {if(t.f){const $=D?_(c)?f[c]:u[c]:c.value;o?F($)&&co($,i):F($)?$.includes(i)||$.push(i):D?(u[c]=[i],_(c)&&(f[c]=u[c])):(c.value=[i],t.k&&(u[t.k]=c.value));}else D?(u[c]=r,_(c)&&(f[c]=r)):O&&(c.value=r,t.k&&(u[t.k]=r));};r?(q.id=-1,Be(q,s)):q();}}}dn().requestIdleCallback;dn().cancelIdleCallback;const bs=t => !!t.type.__asyncLoader,_r=t => t.type.__isKeepAlive;function wa(t,e){vr(t,'a',e);}function Ea(t,e){vr(t,'da',e);}function vr(t,e,s=be){const n=t.__wdc||(t.__wdc=() => {let o=s;for(;o;){if(o.isDeactivated)return;o=o.parent;}return t();});if(pn(e,n,s),s){let o=s.parent;for(;o&&o.parent;)_r(o.parent.vnode)&&xa(n,e,s,o),o=o.parent;}}function xa(t,e,s,n){const o=pn(e,t,n,!0);kr(() => {co(n[e],o);},s);}function pn(t,e,s=be,n=!1){if(s){const o=s[t]||(s[t]=[]),i=e.__weh||(e.__weh=(...r) => {ht();const l=Ns(s),c=nt(e,s,t,r);return l(),pt(),c;});return n?o.unshift(i):o.push(i),i;}}const gt=t => (e,s=be) => {(!Ls||t==='sp')&&pn(t,(...n) => e(...n),s);},Ta=gt('bm'),Sa=gt('m'),La=gt('bu'),Ca=gt('u'),Aa=gt('bum'),kr=gt('um'),Ba=gt('sp'),Ra=gt('rtg'),Da=gt('rtc');function Oa(t,e=be){pn('ec',t,e);}const Pa='components';function Nt(t,e){return Ia(Pa,t,!0,e)||t;}const Na=Symbol.for('v-ndc');function Ia(t,e,s=!0,n=!1){const o=Re||be;if(o){const i=o.type;{const l=Ec(i,!1);if(l&&(l===e||l===Me(e)||l===un(Me(e))))return i;}const r=Go(o[t]||i[t],e)||Go(o.appContext[t],e);return!r&&n?i:r;}}function Go(t,e){return t&&(t[e]||t[Me(e)]||t[un(Me(e))]);}function _e(t,e,s,n){let o;const i=s,r=F(t);if(r||he(t)){const l=r&&zt(t);let c=!1,h=!1;l&&(c=!Ie(t),h=St(t),t=fn(t)),o=new Array(t.length);for(let u=0,f=t.length;u<f;u++)o[u]=e(c?h?Qs(me(t[u])):me(t[u]):t[u],u,void 0,i);}else if(typeof t=='number'){o=new Array(t);for(let l=0;l<t;l++)o[l]=e(l+1,l,void 0,i);}else if(ce(t))if(t[Symbol.iterator])o=Array.from(t,(l,c) => e(l,c,void 0,i));else{const l=Object.keys(t);o=new Array(l.length);for(let c=0,h=l.length;c<h;c++){const u=l[c];o[c]=e(t[u],u,c,i);}}else o=[];return o;}const Gn=t => t?Fr(t)?yn(t):Gn(t.parent):null,_s=ke(Object.create(null),{$:t => t,$el:t => t.vnode.el,$data:t => t.data,$props:t => t.props,$attrs:t => t.attrs,$slots:t => t.slots,$refs:t => t.refs,$parent:t => Gn(t.parent),$root:t => Gn(t.root),$host:t => t.ce,$emit:t => t.emit,$options:t => Er(t),$forceUpdate:t => t.f||(t.f=() => {ko(t.update);}),$nextTick:t => t.n||(t.n=vo.bind(t.proxy)),$watch:t => nc.bind(t)}),Ln=(t,e) => t!==ie&&!t.__isScriptSetup&&ee(t,e),Ma={get({_:t},e){if(e==='__v_skip')return!0;const{ctx:s,setupState:n,data:o,props:i,accessCache:r,type:l,appContext:c}=t;let h;if(e[0]!=='$'){const _=r[e];if(_!==void 0)switch(_){case 1:return n[e];case 2:return o[e];case 4:return s[e];case 3:return i[e];}else{if(Ln(n,e))return r[e]=1,n[e];if(o!==ie&&ee(o,e))return r[e]=2,o[e];if((h=t.propsOptions[0])&&ee(h,e))return r[e]=3,i[e];if(s!==ie&&ee(s,e))return r[e]=4,s[e];qn&&(r[e]=0);}}const u=_s[e];let f,y;if(u)return e==='$attrs'&&ye(t.attrs,'get',''),u(t);if((f=l.__cssModules)&&(f=f[e]))return f;if(s!==ie&&ee(s,e))return r[e]=4,s[e];if(y=c.config.globalProperties,ee(y,e))return y[e];},set({_:t},e,s){const{data:n,setupState:o,ctx:i}=t;return Ln(o,e)?(o[e]=s,!0):n!==ie&&ee(n,e)?(n[e]=s,!0):ee(t.props,e)||e[0]==='$'&&e.slice(1)in t?!1:(i[e]=s,!0);},has({_:{data:t,setupState:e,accessCache:s,ctx:n,appContext:o,propsOptions:i}},r){let l;return!!s[r]||t!==ie&&ee(t,r)||Ln(e,r)||(l=i[0])&&ee(l,r)||ee(n,r)||ee(_s,r)||ee(o.config.globalProperties,r);},defineProperty(t,e,s){return s.get!=null?t._.accessCache[e]=0:ee(s,'value')&&this.set(t,e,s.value,null),Reflect.defineProperty(t,e,s);}};function qo(t){return F(t)?t.reduce((e,s) => (e[s]=null,e),{}):t;}let qn=!0;function Ua(t){const e=Er(t),s=t.proxy,n=t.ctx;qn=!1,e.beforeCreate&&Ho(e.beforeCreate,t,'bc');const{data:o,computed:i,methods:r,watch:l,provide:c,inject:h,created:u,beforeMount:f,mounted:y,beforeUpdate:_,updated:D,activated:O,deactivated:q,beforeDestroy:$,beforeUnmount:I,destroyed:j,unmounted:P,render:ne,renderTracked:ge,renderTriggered:ue,errorCaptured:qe,serverPrefetch:mt,expose:He,inheritAttrs:yt,components:Ct,directives:Ke,filters:ls}=e;if(h&&$a(h,n,null),r)for(const se in r){const Q=r[se];G(Q)&&(n[se]=Q.bind(s));}if(o){const se=o.call(s,s);ce(se)&&(t.data=Os(se));}if(qn=!0,i)for(const se in i){const Q=i[se],it=G(Q)?Q.bind(s,s):G(Q.get)?Q.get.bind(s,s):et,bt=!G(Q)&&G(Q.set)?Q.set.bind(s):et,We=Ne({get:it,set:bt});Object.defineProperty(n,se,{enumerable:!0,configurable:!0,get:() => We.value,set:xe => We.value=xe});}if(l)for(const se in l)wr(l[se],n,s,se);if(c){const se=G(c)?c.call(s):c;Reflect.ownKeys(se).forEach(Q => {Gs(Q,se[Q]);});}u&&Ho(u,t,'c');function pe(se,Q){F(Q)?Q.forEach(it => se(it.bind(s))):Q&&se(Q.bind(s));}if(pe(Ta,f),pe(Sa,y),pe(La,_),pe(Ca,D),pe(wa,O),pe(Ea,q),pe(Oa,qe),pe(Da,ge),pe(Ra,ue),pe(Aa,I),pe(kr,P),pe(Ba,mt),F(He))if(He.length){const se=t.exposed||(t.exposed={});He.forEach(Q => {Object.defineProperty(se,Q,{get:() => s[Q],set:it => s[Q]=it});});}else t.exposed||(t.exposed={});ne&&t.render===et&&(t.render=ne),yt!=null&&(t.inheritAttrs=yt),Ct&&(t.components=Ct),Ke&&(t.directives=Ke),mt&&br(t);}function $a(t,e,s=et){F(t)&&(t=Hn(t));for(const n in t){const o=t[n];let i;ce(o)?'default'in o?i=dt(o.from||n,o.default,!0):i=dt(o.from||n):i=dt(o),ve(i)?Object.defineProperty(e,n,{enumerable:!0,configurable:!0,get:() => i.value,set:r => i.value=r}):e[n]=i;}}function Ho(t,e,s){nt(F(t)?t.map(n => n.bind(e.proxy)):t.bind(e.proxy),e,s);}function wr(t,e,s,n){const o=n.includes('.')?Ir(s,n):() => s[n];if(he(t)){const i=e[t];G(i)&&Xt(o,i);}else if(G(t))Xt(o,t.bind(s));else if(ce(t))if(F(t))t.forEach(i => wr(i,e,s,n));else{const i=G(t.handler)?t.handler.bind(s):e[t.handler];G(i)&&Xt(o,i,t);}}function Er(t){const e=t.type,{mixins:s,extends:n}=e,{mixins:o,optionsCache:i,config:{optionMergeStrategies:r}}=t.appContext,l=i.get(e);let c;return l?c=l:!o.length&&!s&&!n?c=e:(c={},o.length&&o.forEach(h => sn(c,h,r,!0)),sn(c,e,r)),ce(e)&&i.set(e,c),c;}function sn(t,e,s,n=!1){const{mixins:o,extends:i}=e;i&&sn(t,i,s,!0),o&&o.forEach(r => sn(t,r,s,!0));for(const r in e)if(!(n&&r==='expose')){const l=ja[r]||s&&s[r];t[r]=l?l(t[r],e[r]):e[r];}return t;}const ja={data:Ko,props:Wo,emits:Wo,methods:hs,computed:hs,beforeCreate:we,created:we,beforeMount:we,mounted:we,beforeUpdate:we,updated:we,beforeDestroy:we,beforeUnmount:we,destroyed:we,unmounted:we,activated:we,deactivated:we,errorCaptured:we,serverPrefetch:we,components:hs,directives:hs,watch:Va,provide:Ko,inject:Fa};function Ko(t,e){return e?t?function(){return ke(G(t)?t.call(this,this):t,G(e)?e.call(this,this):e);}:e:t;}function Fa(t,e){return hs(Hn(t),Hn(e));}function Hn(t){if(F(t)){const e={};for(let s=0;s<t.length;s++)e[t[s]]=t[s];return e;}return t;}function we(t,e){return t?[...new Set([].concat(t,e))]:e;}function hs(t,e){return t?ke(Object.create(null),t,e):e;}function Wo(t,e){return t?F(t)&&F(e)?[...new Set([...t,...e])]:ke(Object.create(null),qo(t),qo(e??{})):e;}function Va(t,e){if(!t)return e;if(!e)return t;const s=ke(Object.create(null),t);for(const n in e)s[n]=we(t[n],e[n]);return s;}function xr(){return{app:null,config:{isNativeTag:Al,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap};}let Ga=0;function qa(t,e){return function(n,o=null){G(n)||(n=ke({},n)),o!=null&&!ce(o)&&(o=null);const i=xr(),r=new WeakSet,l=[];let c=!1;const h=i.app={_uid:Ga++,_component:n,_props:o,_container:null,_context:i,_instance:null,version:Tc,get config(){return i.config;},set config(u){},use(u,...f){return r.has(u)||(u&&G(u.install)?(r.add(u),u.install(h,...f)):G(u)&&(r.add(u),u(h,...f))),h;},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),h;},component(u,f){return f?(i.components[u]=f,h):i.components[u];},directive(u,f){return f?(i.directives[u]=f,h):i.directives[u];},mount(u,f,y){if(!c){const _=h._ceVNode||de(n,o);return _.appContext=i,y===!0?y='svg':y===!1&&(y=void 0),t(_,u,y),c=!0,h._container=u,u.__vue_app__=h,yn(_.component);}},onUnmount(u){l.push(u);},unmount(){c&&(nt(l,h._instance,16),t(null,h._container),delete h._container.__vue_app__);},provide(u,f){return i.provides[u]=f,h;},runWithContext(u){const f=Qt;Qt=h;try{return u();}finally{Qt=f;}}};return h;};}let Qt=null;function Gs(t,e){if(be){let s=be.provides;const n=be.parent&&be.parent.provides;n===s&&(s=be.provides=Object.create(n)),s[t]=e;}}function dt(t,e,s=!1){const n=be||Re;if(n||Qt){const o=Qt?Qt._context.provides:n?n.parent==null?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(o&&t in o)return o[t];if(arguments.length>1)return s&&G(e)?e.call(n&&n.proxy):e;}}const Tr={},Sr=() => Object.create(Tr),Lr=t => Object.getPrototypeOf(t)===Tr;function Ha(t,e,s,n=!1){const o={},i=Sr();t.propsDefaults=Object.create(null),Cr(t,e,o,i);for(const r in t.propsOptions[0])r in o||(o[r]=void 0);s?t.props=n?o:ar(o):t.type.props?t.props=o:t.props=i,t.attrs=i;}function Ka(t,e,s,n){const{props:o,attrs:i,vnode:{patchFlag:r}}=t,l=Z(o),[c]=t.propsOptions;let h=!1;if((n||r>0)&&!(r&16)){if(r&8){const u=t.vnode.dynamicProps;for(let f=0;f<u.length;f++){const y=u[f];if(gn(t.emitsOptions,y))continue;const _=e[y];if(c)if(ee(i,y))_!==i[y]&&(i[y]=_,h=!0);else{const D=Me(y);o[D]=Kn(c,l,D,_,t,!1);}else _!==i[y]&&(i[y]=_,h=!0);}}}else{Cr(t,e,o,i)&&(h=!0);let u;for(const f in l)(!e||!ee(e,f)&&((u=Ot(f))===f||!ee(e,u)))&&(c?s&&(s[f]!==void 0||s[u]!==void 0)&&(o[f]=Kn(c,l,f,void 0,t,!0)):delete o[f]);if(i!==l)for(const f in i)(!e||!ee(e,f))&&(delete i[f],h=!0);}h&&ct(t.attrs,'set','');}function Cr(t,e,s,n){const[o,i]=t.propsOptions;let r=!1,l;if(e)for(const c in e){if(gs(c))continue;const h=e[c];let u;o&&ee(o,u=Me(c))?!i||!i.includes(u)?s[u]=h:(l||(l={}))[u]=h:gn(t.emitsOptions,c)||(!(c in n)||h!==n[c])&&(n[c]=h,r=!0);}if(i){const c=Z(s),h=l||ie;for(let u=0;u<i.length;u++){const f=i[u];s[f]=Kn(o,c,f,h[f],t,!ee(h,f));}}return r;}function Kn(t,e,s,n,o,i){const r=t[s];if(r!=null){const l=ee(r,'default');if(l&&n===void 0){const c=r.default;if(r.type!==Function&&!r.skipFactory&&G(c)){const{propsDefaults:h}=o;if(s in h)n=h[s];else{const u=Ns(o);n=h[s]=c.call(null,e),u();}}else n=c;o.ce&&o.ce._setProp(s,n);}r[0]&&(i&&!l?n=!1:r[1]&&(n===''||n===Ot(s))&&(n=!0));}return n;}const Wa=new WeakMap;function Ar(t,e,s=!1){const n=s?Wa:e.propsCache,o=n.get(t);if(o)return o;const i=t.props,r={},l=[];let c=!1;if(!G(t)){const u=f => {c=!0;const[y,_]=Ar(f,e,!0);ke(r,y),_&&l.push(..._);};!s&&e.mixins.length&&e.mixins.forEach(u),t.extends&&u(t.extends),t.mixins&&t.mixins.forEach(u);}if(!i&&!c)return ce(t)&&n.set(t,Kt),Kt;if(F(i))for(let u=0;u<i.length;u++){const f=Me(i[u]);zo(f)&&(r[f]=ie);}else if(i)for(const u in i){const f=Me(u);if(zo(f)){const y=i[u],_=r[f]=F(y)||G(y)?{type:y}:ke({},y),D=_.type;let O=!1,q=!0;if(F(D))for(let $=0;$<D.length;++$){const I=D[$],j=G(I)&&I.name;if(j==='Boolean'){O=!0;break;}else j==='String'&&(q=!1);}else O=G(D)&&D.name==='Boolean';_[0]=O,_[1]=q,(O||ee(_,'default'))&&l.push(f);}}const h=[r,l];return ce(t)&&n.set(t,h),h;}function zo(t){return t[0]!=='$'&&!gs(t);}const Eo=t => t[0]==='_'||t==='$stable',xo=t => F(t)?t.map(Ze):[Ze(t)],za=(t,e,s) => {if(e._n)return e;const n=Pt((...o) => xo(e(...o)),s);return n._c=!1,n;},Br=(t,e,s) => {const n=t._ctx;for(const o in t){if(Eo(o))continue;const i=t[o];if(G(i))e[o]=za(o,i,n);else if(i!=null){const r=xo(i);e[o]=() => r;}}},Rr=(t,e) => {const s=xo(e);t.slots.default=() => s;},Dr=(t,e,s) => {for(const n in e)(s||!Eo(n))&&(t[n]=e[n]);},Ya=(t,e,s) => {const n=t.slots=Sr();if(t.vnode.shapeFlag&32){const o=e._;o?(Dr(n,e,s),s&&Gi(n,'_',o,!0)):Br(e,n);}else e&&Rr(t,e);},Ja=(t,e,s) => {const{vnode:n,slots:o}=t;let i=!0,r=ie;if(n.shapeFlag&32){const l=e._;l?s&&l===1?i=!1:Dr(o,e,s):(i=!e.$stable,Br(e,o)),r=e;}else e&&(Rr(t,e),r={default:1});if(i)for(const l in o)!Eo(l)&&r[l]==null&&delete o[l];},Be=uc;function Qa(t){return Xa(t);}function Xa(t,e){const s=dn();s.__VUE__=!0;const{insert:n,remove:o,patchProp:i,createElement:r,createText:l,createComment:c,setText:h,setElementText:u,parentNode:f,nextSibling:y,setScopeId:_=et,insertStaticContent:D}=t,O=(d,p,b,v=null,E=null,w=null,C=void 0,L=null,S=!!p.dynamicChildren) => {if(d===p)return;d&&!us(d,p)&&(v=k(d),xe(d,E,w,!0),d=null),p.patchFlag===-2&&(S=!1,p.dynamicChildren=null);const{type:x,ref:U,shapeFlag:B}=p;switch(x){case mn:q(d,p,b,v);break;case Lt:$(d,p,b,v);break;case An:d==null&&I(p,b,v,C);break;case ae:Ct(d,p,b,v,E,w,C,L,S);break;default:B&1?ne(d,p,b,v,E,w,C,L,S):B&6?Ke(d,p,b,v,E,w,C,L,S):(B&64||B&128)&&x.process(d,p,b,v,E,w,C,L,S,N);}U!=null&&E&&tn(U,d&&d.ref,w,p||d,!p);},q=(d,p,b,v) => {if(d==null)n(p.el=l(p.children),b,v);else{const E=p.el=d.el;p.children!==d.children&&h(E,p.children);}},$=(d,p,b,v) => {d==null?n(p.el=c(p.children||''),b,v):p.el=d.el;},I=(d,p,b,v) => {[d.el,d.anchor]=D(d.children,p,b,v,d.el,d.anchor);},j=({el:d,anchor:p},b,v) => {let E;for(;d&&d!==p;)E=y(d),n(d,b,v),d=E;n(p,b,v);},P=({el:d,anchor:p}) => {let b;for(;d&&d!==p;)b=y(d),o(d),d=b;o(p);},ne=(d,p,b,v,E,w,C,L,S) => {p.type==='svg'?C='svg':p.type==='math'&&(C='mathml'),d==null?ge(p,b,v,E,w,C,L,S):mt(d,p,E,w,C,L,S);},ge=(d,p,b,v,E,w,C,L) => {let S,x;const{props:U,shapeFlag:B,transition:M,dirs:V}=d;if(S=d.el=r(d.type,w,U&&U.is,U),B&8?u(S,d.children):B&16&&qe(d.children,S,null,v,E,Cn(d,w),C,L),V&&At(d,null,v,'created'),ue(S,d,d.scopeId,C,v),U){for(const re in U)re!=='value'&&!gs(re)&&i(S,re,null,U[re],w,v);'value'in U&&i(S,'value',null,U.value,w),(x=U.onVnodeBeforeMount)&&Qe(x,v,d);}V&&At(d,null,v,'beforeMount');const Y=Za(E,M);Y&&M.beforeEnter(S),n(S,p,b),((x=U&&U.onVnodeMounted)||Y||V)&&Be(() => {x&&Qe(x,v,d),Y&&M.enter(S),V&&At(d,null,v,'mounted');},E);},ue=(d,p,b,v,E) => {if(b&&_(d,b),v)for(let w=0;w<v.length;w++)_(d,v[w]);if(E){const w=E.subTree;if(p===w||Ur(w.type)&&(w.ssContent===p||w.ssFallback===p)){const C=E.vnode;ue(d,C,C.scopeId,C.slotScopeIds,E.parent);}}},qe=(d,p,b,v,E,w,C,L,S=0) => {for(let x=S;x<d.length;x++){const U=d[x]=L?kt(d[x]):Ze(d[x]);O(null,U,p,b,v,E,w,C,L);}},mt=(d,p,b,v,E,w,C) => {const L=p.el=d.el;let{patchFlag:S,dynamicChildren:x,dirs:U}=p;S|=d.patchFlag&16;const B=d.props||ie,M=p.props||ie;let V;if(b&&Bt(b,!1),(V=M.onVnodeBeforeUpdate)&&Qe(V,b,p,d),U&&At(p,d,b,'beforeUpdate'),b&&Bt(b,!0),(B.innerHTML&&M.innerHTML==null||B.textContent&&M.textContent==null)&&u(L,''),x?He(d.dynamicChildren,x,L,b,v,Cn(p,E),w):C||Q(d,p,L,null,b,v,Cn(p,E),w,!1),S>0){if(S&16)yt(L,B,M,b,E);else if(S&2&&B.class!==M.class&&i(L,'class',null,M.class,E),S&4&&i(L,'style',B.style,M.style,E),S&8){const Y=p.dynamicProps;for(let re=0;re<Y.length;re++){const te=Y[re],Ce=B[te],Te=M[te];(Te!==Ce||te==='value')&&i(L,te,Ce,Te,E,b);}}S&1&&d.children!==p.children&&u(L,p.children);}else!C&&x==null&&yt(L,B,M,b,E);((V=M.onVnodeUpdated)||U)&&Be(() => {V&&Qe(V,b,p,d),U&&At(p,d,b,'updated');},v);},He=(d,p,b,v,E,w,C) => {for(let L=0;L<p.length;L++){const S=d[L],x=p[L],U=S.el&&(S.type===ae||!us(S,x)||S.shapeFlag&70)?f(S.el):b;O(S,x,U,null,v,E,w,C,!0);}},yt=(d,p,b,v,E) => {if(p!==b){if(p!==ie)for(const w in p)!gs(w)&&!(w in b)&&i(d,w,p[w],null,E,v);for(const w in b){if(gs(w))continue;const C=b[w],L=p[w];C!==L&&w!=='value'&&i(d,w,L,C,E,v);}'value'in b&&i(d,'value',p.value,b.value,E);}},Ct=(d,p,b,v,E,w,C,L,S) => {const x=p.el=d?d.el:l(''),U=p.anchor=d?d.anchor:l('');const{patchFlag:B,dynamicChildren:M,slotScopeIds:V}=p;V&&(L=L?L.concat(V):V),d==null?(n(x,b,v),n(U,b,v),qe(p.children||[],b,U,E,w,C,L,S)):B>0&&B&64&&M&&d.dynamicChildren?(He(d.dynamicChildren,M,b,E,w,C,L),(p.key!=null||E&&p===E.subTree)&&Or(d,p,!0)):Q(d,p,b,U,E,w,C,L,S);},Ke=(d,p,b,v,E,w,C,L,S) => {p.slotScopeIds=L,d==null?p.shapeFlag&512?E.ctx.activate(p,b,v,C,S):ls(p,b,v,E,w,C,S):Ut(d,p,S);},ls=(d,p,b,v,E,w,C) => {const L=d.component=bc(d,v,E);if(_r(d)&&(L.ctx.renderer=N),_c(L,!1,C),L.asyncDep){if(E&&E.registerDep(L,pe,C),!d.el){const S=L.subTree=de(Lt);$(null,S,p,b);}}else pe(L,d,p,b,E,w,C);},Ut=(d,p,b) => {const v=p.component=d.component;if(ac(d,p,b))if(v.asyncDep&&!v.asyncResolved){se(v,p,b);return;}else v.next=p,v.update();else p.el=d.el,v.vnode=p;},pe=(d,p,b,v,E,w,C) => {const L=() => {if(d.isMounted){let{next:B,bu:M,u:V,parent:Y,vnode:re}=d;{const Ye=Pr(d);if(Ye){B&&(B.el=re.el,se(d,B,C)),Ye.asyncDep.then(() => {d.isUnmounted||L();});return;}}let te=B,Ce;Bt(d,!1),B?(B.el=re.el,se(d,B,C)):B=re,M&&Vs(M),(Ce=B.props&&B.props.onVnodeBeforeUpdate)&&Qe(Ce,Y,B,re),Bt(d,!0);const Te=Jo(d),ze=d.subTree;d.subTree=Te,O(ze,Te,f(ze.el),k(ze),d,E,w),B.el=Te.el,te===null&&cc(d,Te.el),V&&Be(V,E),(Ce=B.props&&B.props.onVnodeUpdated)&&Be(() => Qe(Ce,Y,B,re),E);}else{let B;const{el:M,props:V}=p,{bm:Y,m:re,parent:te,root:Ce,type:Te}=d,ze=bs(p);Bt(d,!1),Y&&Vs(Y),!ze&&(B=V&&V.onVnodeBeforeMount)&&Qe(B,te,p),Bt(d,!0);{Ce.ce&&Ce.ce._injectChildStyle(Te);const Ye=d.subTree=Jo(d);O(null,Ye,b,v,d,E,w),p.el=Ye.el;}if(re&&Be(re,E),!ze&&(B=V&&V.onVnodeMounted)){const Ye=p;Be(() => Qe(B,te,Ye),E);}(p.shapeFlag&256||te&&bs(te.vnode)&&te.vnode.shapeFlag&256)&&d.a&&Be(d.a,E),d.isMounted=!0,p=b=v=null;}};d.scope.on();const S=d.effect=new zi(L);d.scope.off();const x=d.update=S.run.bind(S),U=d.job=S.runIfDirty.bind(S);U.i=d,U.id=d.uid,S.scheduler=() => ko(U),Bt(d,!0),x();},se=(d,p,b) => {p.component=d;const v=d.vnode.props;d.vnode=p,d.next=null,Ka(d,p.props,v,b),Ja(d,p.children,b),ht(),Vo(d),pt();},Q=(d,p,b,v,E,w,C,L,S=!1) => {const x=d&&d.children,U=d?d.shapeFlag:0,B=p.children,{patchFlag:M,shapeFlag:V}=p;if(M>0){if(M&128){bt(x,B,b,v,E,w,C,L,S);return;}else if(M&256){it(x,B,b,v,E,w,C,L,S);return;}}V&8?(U&16&&Oe(x,E,w),B!==x&&u(b,B)):U&16?V&16?bt(x,B,b,v,E,w,C,L,S):Oe(x,E,w,!0):(U&8&&u(b,''),V&16&&qe(B,b,v,E,w,C,L,S));},it=(d,p,b,v,E,w,C,L,S) => {d=d||Kt,p=p||Kt;const x=d.length,U=p.length,B=Math.min(x,U);let M;for(M=0;M<B;M++){const V=p[M]=S?kt(p[M]):Ze(p[M]);O(d[M],V,b,null,E,w,C,L,S);}x>U?Oe(d,E,w,!0,!1,B):qe(p,b,v,E,w,C,L,S,B);},bt=(d,p,b,v,E,w,C,L,S) => {let x=0;const U=p.length;let B=d.length-1,M=U-1;for(;x<=B&&x<=M;){const V=d[x],Y=p[x]=S?kt(p[x]):Ze(p[x]);if(us(V,Y))O(V,Y,b,null,E,w,C,L,S);else break;x++;}for(;x<=B&&x<=M;){const V=d[B],Y=p[M]=S?kt(p[M]):Ze(p[M]);if(us(V,Y))O(V,Y,b,null,E,w,C,L,S);else break;B--,M--;}if(x>B){if(x<=M){const V=M+1,Y=V<U?p[V].el:v;for(;x<=M;)O(null,p[x]=S?kt(p[x]):Ze(p[x]),b,Y,E,w,C,L,S),x++;}}else if(x>M)for(;x<=B;)xe(d[x],E,w,!0),x++;else{const V=x,Y=x,re=new Map;for(x=Y;x<=M;x++){const Ae=p[x]=S?kt(p[x]):Ze(p[x]);Ae.key!=null&&re.set(Ae.key,x);}let te,Ce=0;const Te=M-Y+1;let ze=!1,Ye=0;const as=new Array(Te);for(x=0;x<Te;x++)as[x]=0;for(x=V;x<=B;x++){const Ae=d[x];if(Ce>=Te){xe(Ae,E,w,!0);continue;}let Je;if(Ae.key!=null)Je=re.get(Ae.key);else for(te=Y;te<=M;te++)if(as[te-Y]===0&&us(Ae,p[te])){Je=te;break;}Je===void 0?xe(Ae,E,w,!0):(as[Je-Y]=x+1,Je>=Ye?Ye=Je:ze=!0,O(Ae,p[Je],b,null,E,w,C,L,S),Ce++);}const Io=ze?ec(as):Kt;for(te=Io.length-1,x=Te-1;x>=0;x--){const Ae=Y+x,Je=p[Ae],Mo=Ae+1<U?p[Ae+1].el:v;as[x]===0?O(null,Je,b,Mo,E,w,C,L,S):ze&&(te<0||x!==Io[te]?We(Je,b,Mo,2):te--);}}},We=(d,p,b,v,E=null) => {const{el:w,type:C,transition:L,children:S,shapeFlag:x}=d;if(x&6){We(d.component.subTree,p,b,v);return;}if(x&128){d.suspense.move(p,b,v);return;}if(x&64){C.move(d,p,b,N);return;}if(C===ae){n(w,p,b);for(let B=0;B<S.length;B++)We(S[B],p,b,v);n(d.anchor,p,b);return;}if(C===An){j(d,p,b);return;}if(v!==2&&x&1&&L)if(v===0)L.beforeEnter(w),n(w,p,b),Be(() => L.enter(w),E);else{const{leave:B,delayLeave:M,afterLeave:V}=L,Y=() => {d.ctx.isUnmounted?o(w):n(w,p,b);},re=() => {B(w,() => {Y(),V&&V();});};M?M(w,Y,re):re();}else n(w,p,b);},xe=(d,p,b,v=!1,E=!1) => {const{type:w,props:C,ref:L,children:S,dynamicChildren:x,shapeFlag:U,patchFlag:B,dirs:M,cacheIndex:V}=d;if(B===-2&&(E=!1),L!=null&&(ht(),tn(L,null,b,d,!0),pt()),V!=null&&(p.renderCache[V]=void 0),U&256){p.ctx.deactivate(d);return;}const Y=U&1&&M,re=!bs(d);let te;if(re&&(te=C&&C.onVnodeBeforeUnmount)&&Qe(te,p,d),U&6)Is(d.component,b,v);else{if(U&128){d.suspense.unmount(b,v);return;}Y&&At(d,null,p,'beforeUnmount'),U&64?d.type.remove(d,p,b,N,v):x&&!x.hasOnce&&(w!==ae||B>0&&B&64)?Oe(x,p,b,!1,!0):(w===ae&&B&384||!E&&U&16)&&Oe(S,p,b),v&&$t(d);}(re&&(te=C&&C.onVnodeUnmounted)||Y)&&Be(() => {te&&Qe(te,p,d),Y&&At(d,null,p,'unmounted');},b);},$t=d => {const{type:p,el:b,anchor:v,transition:E}=d;if(p===ae){jt(b,v);return;}if(p===An){P(d);return;}const w=() => {o(b),E&&!E.persisted&&E.afterLeave&&E.afterLeave();};if(d.shapeFlag&1&&E&&!E.persisted){const{leave:C,delayLeave:L}=E,S=() => C(b,w);L?L(d.el,w,S):S();}else w();},jt=(d,p) => {let b;for(;d!==p;)b=y(d),o(d),d=b;o(p);},Is=(d,p,b) => {const{bum:v,scope:E,job:w,subTree:C,um:L,m:S,a:x,parent:U,slots:{__:B}}=d;Yo(S),Yo(x),v&&Vs(v),U&&F(B)&&B.forEach(M => {U.renderCache[M]=void 0;}),E.stop(),w&&(w.flags|=8,xe(C,d,p,b)),L&&Be(L,p),Be(() => {d.isUnmounted=!0;},p),p&&p.pendingBranch&&!p.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve());},Oe=(d,p,b,v=!1,E=!1,w=0) => {for(let C=w;C<d.length;C++)xe(d[C],p,b,v,E);},k=d => {if(d.shapeFlag&6)return k(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const p=y(d.anchor||d.el),b=p&&p[va];return b?y(b):p;};let R=!1;const A=(d,p,b) => {d==null?p._vnode&&xe(p._vnode,null,null,!0):O(p._vnode||null,d,p,null,null,null,b),p._vnode=d,R||(R=!0,Vo(),pr(),R=!1);},N={p:O,um:xe,m:We,r:$t,mt:ls,mc:qe,pc:Q,pbc:He,n:k,o:t};return{render:A,hydrate:void 0,createApp:qa(A)};}function Cn({type:t,props:e},s){return s==='svg'&&t==='foreignObject'||s==='mathml'&&t==='annotation-xml'&&e&&e.encoding&&e.encoding.includes('html')?void 0:s;}function Bt({effect:t,job:e},s){s?(t.flags|=32,e.flags|=4):(t.flags&=-33,e.flags&=-5);}function Za(t,e){return(!t||t&&!t.pendingBranch)&&e&&!e.persisted;}function Or(t,e,s=!1){const n=t.children,o=e.children;if(F(n)&&F(o))for(let i=0;i<n.length;i++){const r=n[i];let l=o[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=o[i]=kt(o[i]),l.el=r.el),!s&&l.patchFlag!==-2&&Or(r,l)),l.type===mn&&(l.el=r.el),l.type===Lt&&!l.el&&(l.el=r.el);}}function ec(t){const e=t.slice(),s=[0];let n,o,i,r,l;const c=t.length;for(n=0;n<c;n++){const h=t[n];if(h!==0){if(o=s[s.length-1],t[o]<h){e[n]=o,s.push(n);continue;}for(i=0,r=s.length-1;i<r;)l=i+r>>1,t[s[l]]<h?i=l+1:r=l;h<t[s[i]]&&(i>0&&(e[n]=s[i-1]),s[i]=n);}}for(i=s.length,r=s[i-1];i-- >0;)s[i]=r,r=e[r];return s;}function Pr(t){const e=t.subTree.component;if(e)return e.asyncDep&&!e.asyncResolved?e:Pr(e);}function Yo(t){if(t)for(let e=0;e<t.length;e++)t[e].flags|=8;}const tc=Symbol.for('v-scx'),sc=() => dt(tc);function Xt(t,e,s){return Nr(t,e,s);}function Nr(t,e,s=ie){const{immediate:n,deep:o,flush:i,once:r}=s,l=ke({},s),c=e&&n||!e&&i!=='post';let h;if(Ls){if(i==='sync'){const _=sc();h=_.__watcherHandles||(_.__watcherHandles=[]);}else if(!c){const _=() => {};return _.stop=et,_.resume=et,_.pause=et,_;}}const u=be;l.call=(_,D,O) => nt(_,u,D,O);let f=!1;i==='post'?l.scheduler=_ => {Be(_,u&&u.suspense);}:i!=='sync'&&(f=!0,l.scheduler=(_,D) => {D?_():ko(_);}),l.augmentJob=_ => {e&&(_.flags|=4),f&&(_.flags|=2,u&&(_.id=u.uid,_.i=u));};const y=ma(t,e,l);return Ls&&(h?h.push(y):c&&y()),y;}function nc(t,e,s){const n=this.proxy,o=he(t)?t.includes('.')?Ir(n,t):() => n[t]:t.bind(n,n);let i;G(e)?i=e:(i=e.handler,s=e);const r=Ns(this),l=Nr(o,i.bind(n),s);return r(),l;}function Ir(t,e){const s=e.split('.');return() => {let n=t;for(let o=0;o<s.length&&n;o++)n=n[s[o]];return n;};}const oc=(t,e) => e==='modelValue'||e==='model-value'?t.modelModifiers:t[`${e}Modifiers`]||t[`${Me(e)}Modifiers`]||t[`${Ot(e)}Modifiers`];function ic(t,e,...s){if(t.isUnmounted)return;const n=t.vnode.props||ie;let o=s;const i=e.startsWith('update:'),r=i&&oc(n,e.slice(7));r&&(r.trim&&(o=s.map(u => he(u)?u.trim():u)),r.number&&(o=s.map(Js)));let l,c=n[l=wn(e)]||n[l=wn(Me(e))];!c&&i&&(c=n[l=wn(Ot(e))]),c&&nt(c,t,6,o);const h=n[l+'Once'];if(h){if(!t.emitted)t.emitted={};else if(t.emitted[l])return;t.emitted[l]=!0,nt(h,t,6,o);}}function Mr(t,e,s=!1){const n=e.emitsCache,o=n.get(t);if(o!==void 0)return o;const i=t.emits;let r={},l=!1;if(!G(t)){const c=h => {const u=Mr(h,e,!0);u&&(l=!0,ke(r,u));};!s&&e.mixins.length&&e.mixins.forEach(c),t.extends&&c(t.extends),t.mixins&&t.mixins.forEach(c);}return!i&&!l?(ce(t)&&n.set(t,null),null):(F(i)?i.forEach(c => r[c]=null):ke(r,i),ce(t)&&n.set(t,r),r);}function gn(t,e){return!t||!ln(e)?!1:(e=e.slice(2).replace(/Once$/,''),ee(t,e[0].toLowerCase()+e.slice(1))||ee(t,Ot(e))||ee(t,e));}function Jo(t){const{type:e,vnode:s,proxy:n,withProxy:o,propsOptions:[i],slots:r,attrs:l,emit:c,render:h,renderCache:u,props:f,data:y,setupState:_,ctx:D,inheritAttrs:O}=t,q=en(t);let $,I;try{if(s.shapeFlag&4){const P=o||n,ne=P;$=Ze(h.call(ne,P,u,f,_,y,D)),I=l;}else{const P=e;$=Ze(P.length>1?P(f,{attrs:l,slots:r,emit:c}):P(f,null)),I=e.props?l:rc(l);}}catch(P){vs.length=0,hn(P,t,1),$=de(Lt);}let j=$;if(I&&O!==!1){const P=Object.keys(I),{shapeFlag:ne}=j;P.length&&ne&7&&(i&&P.some(ao)&&(I=lc(I,i)),j=ts(j,I,!1,!0));}return s.dirs&&(j=ts(j,null,!1,!0),j.dirs=j.dirs?j.dirs.concat(s.dirs):s.dirs),s.transition&&wo(j,s.transition),$=j,en(q),$;}const rc=t => {let e;for(const s in t)(s==='class'||s==='style'||ln(s))&&((e||(e={}))[s]=t[s]);return e;},lc=(t,e) => {const s={};for(const n in t)(!ao(n)||!(n.slice(9)in e))&&(s[n]=t[n]);return s;};function ac(t,e,s){const{props:n,children:o,component:i}=t,{props:r,children:l,patchFlag:c}=e,h=i.emitsOptions;if(e.dirs||e.transition)return!0;if(s&&c>=0){if(c&1024)return!0;if(c&16)return n?Qo(n,r,h):!!r;if(c&8){const u=e.dynamicProps;for(let f=0;f<u.length;f++){const y=u[f];if(r[y]!==n[y]&&!gn(h,y))return!0;}}}else return(o||l)&&(!l||!l.$stable)?!0:n===r?!1:n?r?Qo(n,r,h):!0:!!r;return!1;}function Qo(t,e,s){const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!0;for(let o=0;o<n.length;o++){const i=n[o];if(e[i]!==t[i]&&!gn(s,i))return!0;}return!1;}function cc({vnode:t,parent:e},s){for(;e;){const n=e.subTree;if(n.suspense&&n.suspense.activeBranch===t&&(n.el=t.el),n===t)(t=e.vnode).el=s,e=e.parent;else break;}}const Ur=t => t.__isSuspense;function uc(t,e){e&&e.pendingBranch?F(t)?e.effects.push(...t):e.effects.push(t):_a(t);}const ae=Symbol.for('v-fgt'),mn=Symbol.for('v-txt'),Lt=Symbol.for('v-cmt'),An=Symbol.for('v-stc'),vs=[];let De=null;function g(t=!1){vs.push(De=t?null:[]);}function dc(){vs.pop(),De=vs[vs.length-1]||null;}let Ss=1;function Xo(t,e=!1){Ss+=t,t<0&&De&&e&&(De.hasOnce=!0);}function $r(t){return t.dynamicChildren=Ss>0?De||Kt:null,dc(),Ss>0&&De&&De.push(t),t;}function m(t,e,s,n,o,i){return $r(a(t,e,s,n,o,i,!0));}function fc(t,e,s,n,o){return $r(de(t,e,s,n,o,!0));}function nn(t){return t?t.__v_isVNode===!0:!1;}function us(t,e){return t.type===e.type&&t.key===e.key;}const jr=({key:t}) => t??null,qs=({ref:t,ref_key:e,ref_for:s}) => (typeof t=='number'&&(t=''+t),t!=null?he(t)||ve(t)||G(t)?{i:Re,r:t,k:e,f:!!s}:t:null);function a(t,e=null,s=null,n=0,o=null,i=t===ae?0:1,r=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:t,props:e,key:e&&jr(e),ref:e&&qs(e),scopeId:mr,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:n,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Re};return l?(To(c,s),i&128&&t.normalize(c)):s&&(c.shapeFlag|=he(s)?8:16),Ss>0&&!r&&De&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&De.push(c),c;}const de=hc;function hc(t,e=null,s=null,n=0,o=null,i=!1){if((!t||t===Na)&&(t=Lt),nn(t)){const l=ts(t,e,!0);return s&&To(l,s),Ss>0&&!i&&De&&(l.shapeFlag&6?De[De.indexOf(t)]=l:De.push(l)),l.patchFlag=-2,l;}if(xc(t)&&(t=t.__vccOpts),e){e=pc(e);let{class:l,style:c}=e;l&&!he(l)&&(e.class=Ds(l)),ce(c)&&(_o(c)&&!F(c)&&(c=ke({},c)),e.style=fo(c));}const r=he(t)?1:Ur(t)?128:ka(t)?64:ce(t)?4:G(t)?2:0;return a(t,e,s,n,o,r,i,!0);}function pc(t){return t?_o(t)||Lr(t)?ke({},t):t:null;}function ts(t,e,s=!1,n=!1){const{props:o,ref:i,patchFlag:r,children:l,transition:c}=t,h=e?gc(o||{},e):o,u={__v_isVNode:!0,__v_skip:!0,type:t.type,props:h,key:h&&jr(h),ref:e&&e.ref?s&&i?F(i)?i.concat(qs(e)):[i,qs(e)]:qs(e):i,scopeId:t.scopeId,slotScopeIds:t.slotScopeIds,children:l,target:t.target,targetStart:t.targetStart,targetAnchor:t.targetAnchor,staticCount:t.staticCount,shapeFlag:t.shapeFlag,patchFlag:e&&t.type!==ae?r===-1?16:r|16:r,dynamicProps:t.dynamicProps,dynamicChildren:t.dynamicChildren,appContext:t.appContext,dirs:t.dirs,transition:c,component:t.component,suspense:t.suspense,ssContent:t.ssContent&&ts(t.ssContent),ssFallback:t.ssFallback&&ts(t.ssFallback),el:t.el,anchor:t.anchor,ctx:t.ctx,ce:t.ce};return c&&n&&wo(u,c.clone(u)),u;}function z(t=' ',e=0){return de(mn,null,t,e);}function K(t='',e=!1){return e?(g(),fc(Lt,null,t)):de(Lt,null,t);}function Ze(t){return t==null||typeof t=='boolean'?de(Lt):F(t)?de(ae,null,t.slice()):nn(t)?kt(t):de(mn,null,String(t));}function kt(t){return t.el===null&&t.patchFlag!==-1||t.memo?t:ts(t);}function To(t,e){let s=0;const{shapeFlag:n}=t;if(e==null)e=null;else if(F(e))s=16;else if(typeof e=='object')if(n&65){const o=e.default;o&&(o._c&&(o._d=!1),To(t,o()),o._c&&(o._d=!0));return;}else{s=32;const o=e._;!o&&!Lr(e)?e._ctx=Re:o===3&&Re&&(Re.slots._===1?e._=1:(e._=2,t.patchFlag|=1024));}else G(e)?(e={default:e,_ctx:Re},s=32):(e=String(e),n&64?(s=16,e=[z(e)]):s=8);t.children=e,t.shapeFlag|=s;}function gc(...t){const e={};for(let s=0;s<t.length;s++){const n=t[s];for(const o in n)if(o==='class')e.class!==n.class&&(e.class=Ds([e.class,n.class]));else if(o==='style')e.style=fo([e.style,n.style]);else if(ln(o)){const i=e[o],r=n[o];r&&i!==r&&!(F(i)&&i.includes(r))&&(e[o]=i?[].concat(i,r):r);}else o!==''&&(e[o]=n[o]);}return e;}function Qe(t,e,s,n=null){nt(t,e,7,[s,n]);}const mc=xr();let yc=0;function bc(t,e,s){const n=t.type,o=(e?e.appContext:t.appContext)||mc,i={uid:yc++,vnode:t,type:n,parent:e,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Wi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:e?e.provides:Object.create(o.provides),ids:e?e.ids:['',0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ar(n,o),emitsOptions:Mr(n,o),emit:null,emitted:null,propsDefaults:ie,inheritAttrs:n.inheritAttrs,ctx:ie,data:ie,props:ie,attrs:ie,slots:ie,refs:ie,setupState:ie,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=e?e.root:i,i.emit=ic.bind(null,i),t.ce&&t.ce(i),i;}let be=null,on,Wn;{const t=dn(),e=(s,n) => {let o;return(o=t[s])||(o=t[s]=[]),o.push(n),i => {o.length>1?o.forEach(r => r(i)):o[0](i);};};on=e('__VUE_INSTANCE_SETTERS__',s => be=s),Wn=e('__VUE_SSR_SETTERS__',s => Ls=s);}const Ns=t => {const e=be;return on(t),t.scope.on(),() => {t.scope.off(),on(e);};},Zo=() => {be&&be.scope.off(),on(null);};function Fr(t){return t.vnode.shapeFlag&4;}let Ls=!1;function _c(t,e=!1,s=!1){e&&Wn(e);const{props:n,children:o}=t.vnode,i=Fr(t);Ha(t,n,i,e),Ya(t,o,s||e);const r=i?vc(t,e):void 0;return e&&Wn(!1),r;}function vc(t,e){const s=t.type;t.accessCache=Object.create(null),t.proxy=new Proxy(t.ctx,Ma);const{setup:n}=s;if(n){ht();const o=t.setupContext=n.length>1?wc(t):null,i=Ns(t),r=Ps(n,t,0,[t.props,o]),l=ji(r);if(pt(),i(),(l||t.sp)&&!bs(t)&&br(t),l){if(r.then(Zo,Zo),e)return r.then(c => {ei(t,c);}).catch(c => {hn(c,t,0);});t.asyncDep=r;}else ei(t,r);}else Vr(t);}function ei(t,e,s){G(e)?t.type.__ssrInlineRender?t.ssrRender=e:t.render=e:ce(e)&&(t.setupState=dr(e)),Vr(t);}function Vr(t,e,s){const n=t.type;t.render||(t.render=n.render||et);{const o=Ns(t);ht();try{Ua(t);}finally{pt(),o();}}}const kc={get(t,e){return ye(t,'get',''),t[e];}};function wc(t){const e=s => {t.exposed=s||{};};return{attrs:new Proxy(t.attrs,kc),slots:t.slots,emit:t.emit,expose:e};}function yn(t){return t.exposed?t.exposeProxy||(t.exposeProxy=new Proxy(dr(aa(t.exposed)),{get(e,s){if(s in e)return e[s];if(s in _s)return _s[s](t);},has(e,s){return s in e||s in _s;}})):t.proxy;}function Ec(t,e=!0){return G(t)?t.displayName||t.name:t.name||e&&t.__name;}function xc(t){return G(t)&&'__vccOpts'in t;}const Ne=(t,e) => pa(t,e,Ls);function Gr(t,e,s){const n=arguments.length;return n===2?ce(e)&&!F(e)?nn(e)?de(t,null,[e]):de(t,e):de(t,null,e):(n>3?s=Array.prototype.slice.call(arguments,2):n===3&&nn(s)&&(s=[s]),de(t,e,s));}const Tc='3.5.14';/**
* @vue/runtime-dom v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let zn;const ti=typeof window<'u'&&window.trustedTypes;if(ti)try{zn=ti.createPolicy('vue',{createHTML:t => t});}catch{}const qr=zn?t => zn.createHTML(t):t => t,Sc='http://www.w3.org/2000/svg',Lc='http://www.w3.org/1998/Math/MathML',at=typeof document<'u'?document:null,si=at&&at.createElement('template'),Cc={insert:(t,e,s) => {e.insertBefore(t,s||null);},remove:t => {const e=t.parentNode;e&&e.removeChild(t);},createElement:(t,e,s,n) => {const o=e==='svg'?at.createElementNS(Sc,t):e==='mathml'?at.createElementNS(Lc,t):s?at.createElement(t,{is:s}):at.createElement(t);return t==='select'&&n&&n.multiple!=null&&o.setAttribute('multiple',n.multiple),o;},createText:t => at.createTextNode(t),createComment:t => at.createComment(t),setText:(t,e) => {t.nodeValue=e;},setElementText:(t,e) => {t.textContent=e;},parentNode:t => t.parentNode,nextSibling:t => t.nextSibling,querySelector:t => at.querySelector(t),setScopeId(t,e){t.setAttribute(e,'');},insertStaticContent(t,e,s,n,o,i){const r=s?s.previousSibling:e.lastChild;if(o&&(o===i||o.nextSibling))for(;e.insertBefore(o.cloneNode(!0),s),!(o===i||!(o=o.nextSibling)););else{si.innerHTML=qr(n==='svg'?`<svg>${t}</svg>`:n==='mathml'?`<math>${t}</math>`:t);const l=si.content;if(n==='svg'||n==='mathml'){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c);}e.insertBefore(l,s);}return[r?r.nextSibling:e.firstChild,s?s.previousSibling:e.lastChild];}},Ac=Symbol('_vtc');function Bc(t,e,s){const n=t[Ac];n&&(e=(e?[e,...n]:[...n]).join(' ')),e==null?t.removeAttribute('class'):s?t.setAttribute('class',e):t.className=e;}const ni=Symbol('_vod'),Rc=Symbol('_vsh'),Dc=Symbol(''),Oc=/(^|;)\s*display\s*:/;function Pc(t,e,s){const n=t.style,o=he(s);let i=!1;if(s&&!o){if(e)if(he(e))for(const r of e.split(';')){const l=r.slice(0,r.indexOf(':')).trim();s[l]==null&&Hs(n,l,'');}else for(const r in e)s[r]==null&&Hs(n,r,'');for(const r in s)r==='display'&&(i=!0),Hs(n,r,s[r]);}else if(o){if(e!==s){const r=n[Dc];r&&(s+=';'+r),n.cssText=s,i=Oc.test(s);}}else e&&t.removeAttribute('style');ni in t&&(t[ni]=i?n.display:'',t[Rc]&&(n.display='none'));}const oi=/\s*!important$/;function Hs(t,e,s){if(F(s))s.forEach(n => Hs(t,e,n));else if(s==null&&(s=''),e.startsWith('--'))t.setProperty(e,s);else{const n=Nc(t,e);oi.test(s)?t.setProperty(Ot(n),s.replace(oi,''),'important'):t[n]=s;}}const ii=['Webkit','Moz','ms'],Bn={};function Nc(t,e){const s=Bn[e];if(s)return s;let n=Me(e);if(n!=='filter'&&n in t)return Bn[e]=n;n=un(n);for(let o=0;o<ii.length;o++){const i=ii[o]+n;if(i in t)return Bn[e]=i;}return e;}const ri='http://www.w3.org/1999/xlink';function li(t,e,s,n,o,i=$l(e)){n&&e.startsWith('xlink:')?s==null?t.removeAttributeNS(ri,e.slice(6,e.length)):t.setAttributeNS(ri,e,s):s==null||i&&!qi(s)?t.removeAttribute(e):t.setAttribute(e,i?'':st(s)?String(s):s);}function ai(t,e,s,n,o){if(e==='innerHTML'||e==='textContent'){s!=null&&(t[e]=e==='innerHTML'?qr(s):s);return;}const i=t.tagName;if(e==='value'&&i!=='PROGRESS'&&!i.includes('-')){const l=i==='OPTION'?t.getAttribute('value')||'':t.value,c=s==null?t.type==='checkbox'?'on':'':String(s);(l!==c||!('_value'in t))&&(t.value=c),s==null&&t.removeAttribute(e),t._value=s;return;}let r=!1;if(s===''||s==null){const l=typeof t[e];l==='boolean'?s=qi(s):s==null&&l==='string'?(s='',r=!0):l==='number'&&(s=0,r=!0);}try{t[e]=s;}catch{}r&&t.removeAttribute(o||e);}function Et(t,e,s,n){t.addEventListener(e,s,n);}function Ic(t,e,s,n){t.removeEventListener(e,s,n);}const ci=Symbol('_vei');function Mc(t,e,s,n,o=null){const i=t[ci]||(t[ci]={}),r=i[e];if(n&&r)r.value=n;else{const[l,c]=Uc(e);if(n){const h=i[e]=Fc(n,o);Et(t,l,h,c);}else r&&(Ic(t,l,r,c),i[e]=void 0);}}const ui=/(?:Once|Passive|Capture)$/;function Uc(t){let e;if(ui.test(t)){e={};let n;for(;n=t.match(ui);)t=t.slice(0,t.length-n[0].length),e[n[0].toLowerCase()]=!0;}return[t[2]===':'?t.slice(3):Ot(t.slice(2)),e];}let Rn=0;const $c=Promise.resolve(),jc=() => Rn||($c.then(() => Rn=0),Rn=Date.now());function Fc(t,e){const s=n => {if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;nt(Vc(n,s.value),e,5,[n]);};return s.value=t,s.attached=jc(),s;}function Vc(t,e){if(F(e)){const s=t.stopImmediatePropagation;return t.stopImmediatePropagation=() => {s.call(t),t._stopped=!0;},e.map(n => o => !o._stopped&&n&&n(o));}else return e;}const di=t => t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&t.charCodeAt(2)>96&&t.charCodeAt(2)<123,Gc=(t,e,s,n,o,i) => {const r=o==='svg';e==='class'?Bc(t,n,r):e==='style'?Pc(t,s,n):ln(e)?ao(e)||Mc(t,e,s,n,i):(e[0]==='.'?(e=e.slice(1),!0):e[0]==='^'?(e=e.slice(1),!1):qc(t,e,n,r))?(ai(t,e,n),!t.tagName.includes('-')&&(e==='value'||e==='checked'||e==='selected')&&li(t,e,n,r,i,e!=='value')):t._isVueCE&&(/[A-Z]/.test(e)||!he(n))?ai(t,Me(e),n,i,e):(e==='true-value'?t._trueValue=n:e==='false-value'&&(t._falseValue=n),li(t,e,n,r));};function qc(t,e,s,n){if(n)return!!(e==='innerHTML'||e==='textContent'||e in t&&di(e)&&G(s));if(e==='spellcheck'||e==='draggable'||e==='translate'||e==='autocorrect'||e==='form'||e==='list'&&t.tagName==='INPUT'||e==='type'&&t.tagName==='TEXTAREA')return!1;if(e==='width'||e==='height'){const o=t.tagName;if(o==='IMG'||o==='VIDEO'||o==='CANVAS'||o==='SOURCE')return!1;}return di(e)&&he(s)?!1:e in t;}const ss=t => {const e=t.props['onUpdate:modelValue']||!1;return F(e)?s => Vs(e,s):e;};function Hc(t){t.target.composing=!0;}function fi(t){const e=t.target;e.composing&&(e.composing=!1,e.dispatchEvent(new Event('input')));}const ft=Symbol('_assign'),J={created(t,{modifiers:{lazy:e,trim:s,number:n}},o){t[ft]=ss(o);const i=n||o.props&&o.props.type==='number';Et(t,e?'change':'input',r => {if(r.target.composing)return;let l=t.value;s&&(l=l.trim()),i&&(l=Js(l)),t[ft](l);}),s&&Et(t,'change',() => {t.value=t.value.trim();}),e||(Et(t,'compositionstart',Hc),Et(t,'compositionend',fi),Et(t,'change',fi));},mounted(t,{value:e}){t.value=e??'';},beforeUpdate(t,{value:e,oldValue:s,modifiers:{lazy:n,trim:o,number:i}},r){if(t[ft]=ss(r),t.composing)return;const l=(i||t.type==='number')&&!/^0\d/.test(t.value)?Js(t.value):t.value,c=e??'';l!==c&&(document.activeElement===t&&t.type!=='range'&&(n&&e===s||o&&t.value.trim()===c)||(t.value=c));}},hi={created(t,{value:e},s){t.checked=es(e,s.props.value),t[ft]=ss(s),Et(t,'change',() => {t[ft](Cs(t));});},beforeUpdate(t,{value:e,oldValue:s},n){t[ft]=ss(n),e!==s&&(t.checked=es(e,n.props.value));}},Zt={deep:!0,created(t,{value:e,modifiers:{number:s}},n){const o=an(e);Et(t,'change',() => {const i=Array.prototype.filter.call(t.options,r => r.selected).map(r => s?Js(Cs(r)):Cs(r));t[ft](t.multiple?o?new Set(i):i:i[0]),t._assigning=!0,vo(() => {t._assigning=!1;});}),t[ft]=ss(n);},mounted(t,{value:e}){pi(t,e);},beforeUpdate(t,e,s){t[ft]=ss(s);},updated(t,{value:e}){t._assigning||pi(t,e);}};function pi(t,e){const s=t.multiple,n=F(e);if(!(s&&!n&&!an(e))){for(let o=0,i=t.options.length;o<i;o++){const r=t.options[o],l=Cs(r);if(s)if(n){const c=typeof l;c==='string'||c==='number'?r.selected=e.some(h => String(h)===String(l)):r.selected=Fl(e,l)>-1;}else r.selected=e.has(l);else if(es(Cs(r),e)){t.selectedIndex!==o&&(t.selectedIndex=o);return;}}!s&&t.selectedIndex!==-1&&(t.selectedIndex=-1);}}function Cs(t){return'_value'in t?t._value:t.value;}const Kc=['ctrl','shift','alt','meta'],Wc={stop:t => t.stopPropagation(),prevent:t => t.preventDefault(),self:t => t.target!==t.currentTarget,ctrl:t => !t.ctrlKey,shift:t => !t.shiftKey,alt:t => !t.altKey,meta:t => !t.metaKey,left:t => 'button'in t&&t.button!==0,middle:t => 'button'in t&&t.button!==1,right:t => 'button'in t&&t.button!==2,exact:(t,e) => Kc.some(s => t[`${s}Key`]&&!e.includes(s))},Fe=(t,e) => {const s=t._withMods||(t._withMods={}),n=e.join('.');return s[n]||(s[n]=(o,...i) => {for(let r=0;r<e.length;r++){const l=Wc[e[r]];if(l&&l(o,e))return;}return t(o,...i);});},zc=ke({patchProp:Gc},Cc);let gi;function Yc(){return gi||(gi=Qa(zc));}const Jc=(...t) => {const e=Yc().createApp(...t),{mount:s}=e;return e.mount=n => {const o=Xc(n);if(!o)return;const i=e._component;!G(i)&&!i.render&&!i.template&&(i.template=o.innerHTML),o.nodeType===1&&(o.textContent='');const r=s(o,!1,Qc(o));return o instanceof Element&&(o.removeAttribute('v-cloak'),o.setAttribute('data-v-app','')),r;},e;};function Qc(t){if(t instanceof SVGElement)return'svg';if(typeof MathMLElement=='function'&&t instanceof MathMLElement)return'mathml';}function Xc(t){return he(t)?document.querySelector(t):t;}const ot=Object.create(null);ot.open='0';ot.close='1';ot.ping='2';ot.pong='3';ot.message='4';ot.upgrade='5';ot.noop='6';const Ks=Object.create(null);Object.keys(ot).forEach(t => {Ks[ot[t]]=t;});const Yn={type:'error',data:'parser error'},Hr=typeof Blob=='function'||typeof Blob<'u'&&Object.prototype.toString.call(Blob)==='[object BlobConstructor]',Kr=typeof ArrayBuffer=='function',Wr=t => typeof ArrayBuffer.isView=='function'?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer,So=({type:t,data:e},s,n) => Hr&&e instanceof Blob?s?n(e):mi(e,n):Kr&&(e instanceof ArrayBuffer||Wr(e))?s?n(e):mi(new Blob([e]),n):n(ot[t]+(e||'')),mi=(t,e) => {const s=new FileReader;return s.onload=function(){const n=s.result.split(',')[1];e('b'+(n||''));},s.readAsDataURL(t);};function yi(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength);}let Dn;function Zc(t,e){if(Hr&&t.data instanceof Blob)return t.data.arrayBuffer().then(yi).then(e);if(Kr&&(t.data instanceof ArrayBuffer||Wr(t.data)))return e(yi(t.data));So(t,!1,s => {Dn||(Dn=new TextEncoder),e(Dn.encode(s));});}const bi='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',ps=typeof Uint8Array>'u'?[]:new Uint8Array(256);for(let t=0;t<bi.length;t++)ps[bi.charCodeAt(t)]=t;const eu=t => {let e=t.length*.75,s=t.length,n,o=0,i,r,l,c;t[t.length-1]==='='&&(e--,t[t.length-2]==='='&&e--);const h=new ArrayBuffer(e),u=new Uint8Array(h);for(n=0;n<s;n+=4)i=ps[t.charCodeAt(n)],r=ps[t.charCodeAt(n+1)],l=ps[t.charCodeAt(n+2)],c=ps[t.charCodeAt(n+3)],u[o++]=i<<2|r>>4,u[o++]=(r&15)<<4|l>>2,u[o++]=(l&3)<<6|c&63;return h;},tu=typeof ArrayBuffer=='function',Lo=(t,e) => {if(typeof t!='string')return{type:'message',data:zr(t,e)};const s=t.charAt(0);return s==='b'?{type:'message',data:su(t.substring(1),e)}:Ks[s]?t.length>1?{type:Ks[s],data:t.substring(1)}:{type:Ks[s]}:Yn;},su=(t,e) => {if(tu){const s=eu(t);return zr(s,e);}else return{base64:!0,data:t};},zr=(t,e) => {switch(e){case'blob':return t instanceof Blob?t:new Blob([t]);case'arraybuffer':default:return t instanceof ArrayBuffer?t:t.buffer;}},Yr='',nu=(t,e) => {const s=t.length,n=new Array(s);let o=0;t.forEach((i,r) => {So(i,!1,l => {n[r]=l,++o===s&&e(n.join(Yr));});});},ou=(t,e) => {const s=t.split(Yr),n=[];for(let o=0;o<s.length;o++){const i=Lo(s[o],e);if(n.push(i),i.type==='error')break;}return n;};function iu(){return new TransformStream({transform(t,e){Zc(t,s => {const n=s.length;let o;if(n<126)o=new Uint8Array(1),new DataView(o.buffer).setUint8(0,n);else if(n<65536){o=new Uint8Array(3);const i=new DataView(o.buffer);i.setUint8(0,126),i.setUint16(1,n);}else{o=new Uint8Array(9);const i=new DataView(o.buffer);i.setUint8(0,127),i.setBigUint64(1,BigInt(n));}t.data&&typeof t.data!='string'&&(o[0]|=128),e.enqueue(o),e.enqueue(s);});}});}let On;function js(t){return t.reduce((e,s) => e+s.length,0);}function Fs(t,e){if(t[0].length===e)return t.shift();const s=new Uint8Array(e);let n=0;for(let o=0;o<e;o++)s[o]=t[0][n++],n===t[0].length&&(t.shift(),n=0);return t.length&&n<t[0].length&&(t[0]=t[0].slice(n)),s;}function ru(t,e){On||(On=new TextDecoder);const s=[];let n=0,o=-1,i=!1;return new TransformStream({transform(r,l){for(s.push(r);;){if(n===0){if(js(s)<1)break;const c=Fs(s,1);i=(c[0]&128)===128,o=c[0]&127,o<126?n=3:o===126?n=1:n=2;}else if(n===1){if(js(s)<2)break;const c=Fs(s,2);o=new DataView(c.buffer,c.byteOffset,c.length).getUint16(0),n=3;}else if(n===2){if(js(s)<8)break;const c=Fs(s,8),h=new DataView(c.buffer,c.byteOffset,c.length),u=h.getUint32(0);if(u>Math.pow(2,21)-1){l.enqueue(Yn);break;}o=u*Math.pow(2,32)+h.getUint32(4),n=3;}else{if(js(s)<o)break;const c=Fs(s,o);l.enqueue(Lo(i?c:On.decode(c),e)),n=0;}if(o===0||o>t){l.enqueue(Yn);break;}}}});}const Jr=4;function fe(t){if(t)return lu(t);}function lu(t){for(const e in fe.prototype)t[e]=fe.prototype[e];return t;}fe.prototype.on=fe.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks['$'+t]=this._callbacks['$'+t]||[]).push(e),this;};fe.prototype.once=function(t,e){function s(){this.off(t,s),e.apply(this,arguments);}return s.fn=e,this.on(t,s),this;};fe.prototype.off=fe.prototype.removeListener=fe.prototype.removeAllListeners=fe.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;const s=this._callbacks['$'+t];if(!s)return this;if(arguments.length==1)return delete this._callbacks['$'+t],this;for(var n,o=0;o<s.length;o++)if(n=s[o],n===e||n.fn===e){s.splice(o,1);break;}return s.length===0&&delete this._callbacks['$'+t],this;};fe.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),s=this._callbacks['$'+t],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(s){s=s.slice(0);for(var n=0,o=s.length;n<o;++n)s[n].apply(this,e);}return this;};fe.prototype.emitReserved=fe.prototype.emit;fe.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks['$'+t]||[];};fe.prototype.hasListeners=function(t){return!!this.listeners(t).length;};const bn=typeof Promise=='function'&&typeof Promise.resolve=='function'?e => Promise.resolve().then(e):(e,s) => s(e,0),Pe=typeof self<'u'?self:typeof window<'u'?window:Function('return this')(),au='arraybuffer';function Qr(t,...e){return e.reduce((s,n) => (t.hasOwnProperty(n)&&(s[n]=t[n]),s),{});}const cu=Pe.setTimeout,uu=Pe.clearTimeout;function _n(t,e){e.useNativeTimers?(t.setTimeoutFn=cu.bind(Pe),t.clearTimeoutFn=uu.bind(Pe)):(t.setTimeoutFn=Pe.setTimeout.bind(Pe),t.clearTimeoutFn=Pe.clearTimeout.bind(Pe));}const du=1.33;function fu(t){return typeof t=='string'?hu(t):Math.ceil((t.byteLength||t.size)*du);}function hu(t){let e=0,s=0;for(let n=0,o=t.length;n<o;n++)e=t.charCodeAt(n),e<128?s+=1:e<2048?s+=2:e<55296||e>=57344?s+=3:(n++,s+=4);return s;}function Xr(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5);}function pu(t){let e='';for(const s in t)t.hasOwnProperty(s)&&(e.length&&(e+='&'),e+=encodeURIComponent(s)+'='+encodeURIComponent(t[s]));return e;}function gu(t){const e={},s=t.split('&');for(let n=0,o=s.length;n<o;n++){const i=s[n].split('=');e[decodeURIComponent(i[0])]=decodeURIComponent(i[1]);}return e;}class mu extends Error{constructor(e,s,n){super(e),this.description=s,this.context=n,this.type='TransportError';}}class Co extends fe{constructor(e){super(),this.writable=!1,_n(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64;}onError(e,s,n){return super.emitReserved('error',new mu(e,s,n)),this;}open(){return this.readyState='opening',this.doOpen(),this;}close(){return(this.readyState==='opening'||this.readyState==='open')&&(this.doClose(),this.onClose()),this;}send(e){this.readyState==='open'&&this.write(e);}onOpen(){this.readyState='open',this.writable=!0,super.emitReserved('open');}onData(e){const s=Lo(e,this.socket.binaryType);this.onPacket(s);}onPacket(e){super.emitReserved('packet',e);}onClose(e){this.readyState='closed',super.emitReserved('close',e);}pause(e){}createUri(e,s={}){return e+'://'+this._hostname()+this._port()+this.opts.path+this._query(s);}_hostname(){const e=this.opts.hostname;return e.indexOf(':')===-1?e:'['+e+']';}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?':'+this.opts.port:'';}_query(e){const s=pu(e);return s.length?'?'+s:'';}}class yu extends Co{constructor(){super(...arguments),this._polling=!1;}get name(){return'polling';}doOpen(){this._poll();}pause(e){this.readyState='pausing';const s=() => {this.readyState='paused',e();};if(this._polling||!this.writable){let n=0;this._polling&&(n++,this.once('pollComplete',function(){--n||s();})),this.writable||(n++,this.once('drain',function(){--n||s();}));}else s();}_poll(){this._polling=!0,this.doPoll(),this.emitReserved('poll');}onData(e){const s=n => {if(this.readyState==='opening'&&n.type==='open'&&this.onOpen(),n.type==='close')return this.onClose({description:'transport closed by the server'}),!1;this.onPacket(n);};ou(e,this.socket.binaryType).forEach(s),this.readyState!=='closed'&&(this._polling=!1,this.emitReserved('pollComplete'),this.readyState==='open'&&this._poll());}doClose(){const e=() => {this.write([{type:'close'}]);};this.readyState==='open'?e():this.once('open',e);}write(e){this.writable=!1,nu(e,s => {this.doWrite(s,() => {this.writable=!0,this.emitReserved('drain');});});}uri(){const e=this.opts.secure?'https':'http',s=this.query||{};return this.opts.timestampRequests!==!1&&(s[this.opts.timestampParam]=Xr()),!this.supportsBinary&&!s.sid&&(s.b64=1),this.createUri(e,s);}}let Zr=!1;try{Zr=typeof XMLHttpRequest<'u'&&'withCredentials'in new XMLHttpRequest;}catch{}const bu=Zr;function _u(){}class vu extends yu{constructor(e){if(super(e),typeof location<'u'){const s=location.protocol==='https:';let n=location.port;n||(n=s?'443':'80'),this.xd=typeof location<'u'&&e.hostname!==location.hostname||n!==e.port;}}doWrite(e,s){const n=this.request({method:'POST',data:e});n.on('success',s),n.on('error',(o,i) => {this.onError('xhr post error',o,i);});}doPoll(){const e=this.request();e.on('data',this.onData.bind(this)),e.on('error',(s,n) => {this.onError('xhr poll error',s,n);}),this.pollXhr=e;}}class tt extends fe{constructor(e,s,n){super(),this.createRequest=e,_n(this,n),this._opts=n,this._method=n.method||'GET',this._uri=s,this._data=n.data!==void 0?n.data:null,this._create();}_create(){let e;const s=Qr(this._opts,'agent','pfx','key','passphrase','cert','ca','ciphers','rejectUnauthorized','autoUnref');s.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(s);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(const o in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(o)&&n.setRequestHeader(o,this._opts.extraHeaders[o]);}}catch{}if(this._method==='POST')try{n.setRequestHeader('Content-type','text/plain;charset=UTF-8');}catch{}try{n.setRequestHeader('Accept','*/*');}catch{}(e=this._opts.cookieJar)===null||e===void 0||e.addCookies(n),'withCredentials'in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=() => {let o;n.readyState===3&&((o=this._opts.cookieJar)===null||o===void 0||o.parseCookies(n.getResponseHeader('set-cookie'))),n.readyState===4&&(n.status===200||n.status===1223?this._onLoad():this.setTimeoutFn(() => {this._onError(typeof n.status=='number'?n.status:0);},0));},n.send(this._data);}catch(o){this.setTimeoutFn(() => {this._onError(o);},0);return;}typeof document<'u'&&(this._index=tt.requestsCount++,tt.requests[this._index]=this);}_onError(e){this.emitReserved('error',e,this._xhr),this._cleanup(!0);}_cleanup(e){if(!(typeof this._xhr>'u'||this._xhr===null)){if(this._xhr.onreadystatechange=_u,e)try{this._xhr.abort();}catch{}typeof document<'u'&&delete tt.requests[this._index],this._xhr=null;}}_onLoad(){const e=this._xhr.responseText;e!==null&&(this.emitReserved('data',e),this.emitReserved('success'),this._cleanup());}abort(){this._cleanup();}}tt.requestsCount=0;tt.requests={};if(typeof document<'u'){if(typeof attachEvent=='function')attachEvent('onunload',_i);else if(typeof addEventListener=='function'){const t='onpagehide'in Pe?'pagehide':'unload';addEventListener(t,_i,!1);}}function _i(){for(const t in tt.requests)tt.requests.hasOwnProperty(t)&&tt.requests[t].abort();}const ku=function(){const t=el({xdomain:!1});return t&&t.responseType!==null;}();class wu extends vu{constructor(e){super(e);const s=e&&e.forceBase64;this.supportsBinary=ku&&!s;}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new tt(el,this.uri(),e);}}function el(t){const e=t.xdomain;try{if(typeof XMLHttpRequest<'u'&&(!e||bu))return new XMLHttpRequest;}catch{}if(!e)try{return new Pe[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');}catch{}}const tl=typeof navigator<'u'&&typeof navigator.product=='string'&&navigator.product.toLowerCase()==='reactnative';class Eu extends Co{get name(){return'websocket';}doOpen(){const e=this.uri(),s=this.opts.protocols,n=tl?{}:Qr(this.opts,'agent','perMessageDeflate','pfx','key','passphrase','cert','ca','ciphers','rejectUnauthorized','localAddress','protocolVersion','origin','maxPayload','family','checkServerIdentity');this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,s,n);}catch(o){return this.emitReserved('error',o);}this.ws.binaryType=this.socket.binaryType,this.addEventListeners();}addEventListeners(){this.ws.onopen=() => {this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen();},this.ws.onclose=e => this.onClose({description:'websocket connection closed',context:e}),this.ws.onmessage=e => this.onData(e.data),this.ws.onerror=e => this.onError('websocket error',e);}write(e){this.writable=!1;for(let s=0;s<e.length;s++){const n=e[s],o=s===e.length-1;So(n,this.supportsBinary,i => {try{this.doWrite(n,i);}catch{}o&&bn(() => {this.writable=!0,this.emitReserved('drain');},this.setTimeoutFn);});}}doClose(){typeof this.ws<'u'&&(this.ws.onerror=() => {},this.ws.close(),this.ws=null);}uri(){const e=this.opts.secure?'wss':'ws',s=this.query||{};return this.opts.timestampRequests&&(s[this.opts.timestampParam]=Xr()),this.supportsBinary||(s.b64=1),this.createUri(e,s);}}const Pn=Pe.WebSocket||Pe.MozWebSocket;class xu extends Eu{createSocket(e,s,n){return tl?new Pn(e,s,n):s?new Pn(e,s):new Pn(e);}doWrite(e,s){this.ws.send(s);}}class Tu extends Co{get name(){return'webtransport';}doOpen(){try{this._transport=new WebTransport(this.createUri('https'),this.opts.transportOptions[this.name]);}catch(e){return this.emitReserved('error',e);}this._transport.closed.then(() => {this.onClose();}).catch(e => {this.onError('webtransport error',e);}),this._transport.ready.then(() => {this._transport.createBidirectionalStream().then(e => {const s=ru(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(s).getReader(),o=iu();o.readable.pipeTo(e.writable),this._writer=o.writable.getWriter();const i=() => {n.read().then(({done:l,value:c}) => {l||(this.onPacket(c),i());}).catch(l => {});};i();const r={type:'open'};this.query.sid&&(r.data=`{"sid":"${this.query.sid}"}`),this._writer.write(r).then(() => this.onOpen());});});}write(e){this.writable=!1;for(let s=0;s<e.length;s++){const n=e[s],o=s===e.length-1;this._writer.write(n).then(() => {o&&bn(() => {this.writable=!0,this.emitReserved('drain');},this.setTimeoutFn);});}}doClose(){let e;(e=this._transport)===null||e===void 0||e.close();}}const Su={websocket:xu,webtransport:Tu,polling:wu},Lu=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Cu=['source','protocol','authority','userInfo','user','password','host','port','relative','path','directory','file','query','anchor'];function Jn(t){if(t.length>8e3)throw'URI too long';const e=t,s=t.indexOf('['),n=t.indexOf(']');s!=-1&&n!=-1&&(t=t.substring(0,s)+t.substring(s,n).replace(/:/g,';')+t.substring(n,t.length));let o=Lu.exec(t||''),i={},r=14;for(;r--;)i[Cu[r]]=o[r]||'';return s!=-1&&n!=-1&&(i.source=e,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,':'),i.authority=i.authority.replace('[','').replace(']','').replace(/;/g,':'),i.ipv6uri=!0),i.pathNames=Au(i,i.path),i.queryKey=Bu(i,i.query),i;}function Au(t,e){const s=/\/{2,9}/g,n=e.replace(s,'/').split('/');return(e.slice(0,1)=='/'||e.length===0)&&n.splice(0,1),e.slice(-1)=='/'&&n.splice(n.length-1,1),n;}function Bu(t,e){const s={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(n,o,i){o&&(s[o]=i);}),s;}const Qn=typeof addEventListener=='function'&&typeof removeEventListener=='function',Ws=[];Qn&&addEventListener('offline',() => {Ws.forEach(t => t());},!1);class Tt extends fe{constructor(e,s){if(super(),this.binaryType=au,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&typeof e=='object'&&(s=e,e=null),e){const n=Jn(e);s.hostname=n.host,s.secure=n.protocol==='https'||n.protocol==='wss',s.port=n.port,n.query&&(s.query=n.query);}else s.host&&(s.hostname=Jn(s.host).host);_n(this,s),this.secure=s.secure!=null?s.secure:typeof location<'u'&&location.protocol==='https:',s.hostname&&!s.port&&(s.port=this.secure?'443':'80'),this.hostname=s.hostname||(typeof location<'u'?location.hostname:'localhost'),this.port=s.port||(typeof location<'u'&&location.port?location.port:this.secure?'443':'80'),this.transports=[],this._transportsByName={},s.transports.forEach(n => {const o=n.prototype.name;this.transports.push(o),this._transportsByName[o]=n;}),this.opts=Object.assign({path:'/engine.io',agent:!1,withCredentials:!1,upgrade:!0,timestampParam:'t',rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},s),this.opts.path=this.opts.path.replace(/\/$/,'')+(this.opts.addTrailingSlash?'/':''),typeof this.opts.query=='string'&&(this.opts.query=gu(this.opts.query)),Qn&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=() => {this.transport&&(this.transport.removeAllListeners(),this.transport.close());},addEventListener('beforeunload',this._beforeunloadEventListener,!1)),this.hostname!=='localhost'&&(this._offlineEventListener=() => {this._onClose('transport close',{description:'network connection lost'});},Ws.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open();}createTransport(e){const s=Object.assign({},this.opts.query);s.EIO=Jr,s.transport=e,this.id&&(s.sid=this.id);const n=Object.assign({},this.opts,{query:s,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n);}_open(){if(this.transports.length===0){this.setTimeoutFn(() => {this.emitReserved('error','No transports available');},0);return;}const e=this.opts.rememberUpgrade&&Tt.priorWebsocketSuccess&&this.transports.indexOf('websocket')!==-1?'websocket':this.transports[0];this.readyState='opening';const s=this.createTransport(e);s.open(),this.setTransport(s);}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on('drain',this._onDrain.bind(this)).on('packet',this._onPacket.bind(this)).on('error',this._onError.bind(this)).on('close',s => this._onClose('transport close',s));}onOpen(){this.readyState='open',Tt.priorWebsocketSuccess=this.transport.name==='websocket',this.emitReserved('open'),this.flush();}_onPacket(e){if(this.readyState==='opening'||this.readyState==='open'||this.readyState==='closing')switch(this.emitReserved('packet',e),this.emitReserved('heartbeat'),e.type){case'open':this.onHandshake(JSON.parse(e.data));break;case'ping':this._sendPacket('pong'),this.emitReserved('ping'),this.emitReserved('pong'),this._resetPingTimeout();break;case'error':const s=new Error('server error');s.code=e.data,this._onError(s);break;case'message':this.emitReserved('data',e.data),this.emitReserved('message',e.data);break;}}onHandshake(e){this.emitReserved('handshake',e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),this.readyState!=='closed'&&this._resetPingTimeout();}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(() => {this._onClose('ping timeout');},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref();}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved('drain'):this.flush();}flush(){if(this.readyState!=='closed'&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved('flush');}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==='polling'&&this.writeBuffer.length>1))return this.writeBuffer;let s=1;for(let n=0;n<this.writeBuffer.length;n++){const o=this.writeBuffer[n].data;if(o&&(s+=fu(o)),n>0&&s>this._maxPayload)return this.writeBuffer.slice(0,n);s+=2;}return this.writeBuffer;}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,bn(() => {this._onClose('ping timeout');},this.setTimeoutFn)),e;}write(e,s,n){return this._sendPacket('message',e,s,n),this;}send(e,s,n){return this._sendPacket('message',e,s,n),this;}_sendPacket(e,s,n,o){if(typeof s=='function'&&(o=s,s=void 0),typeof n=='function'&&(o=n,n=null),this.readyState==='closing'||this.readyState==='closed')return;n=n||{},n.compress=n.compress!==!1;const i={type:e,data:s,options:n};this.emitReserved('packetCreate',i),this.writeBuffer.push(i),o&&this.once('flush',o),this.flush();}close(){const e=() => {this._onClose('forced close'),this.transport.close();},s=() => {this.off('upgrade',s),this.off('upgradeError',s),e();},n=() => {this.once('upgrade',s),this.once('upgradeError',s);};return(this.readyState==='opening'||this.readyState==='open')&&(this.readyState='closing',this.writeBuffer.length?this.once('drain',() => {this.upgrading?n():e();}):this.upgrading?n():e()),this;}_onError(e){if(Tt.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==='opening')return this.transports.shift(),this._open();this.emitReserved('error',e),this._onClose('transport error',e);}_onClose(e,s){if(this.readyState==='opening'||this.readyState==='open'||this.readyState==='closing'){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners('close'),this.transport.close(),this.transport.removeAllListeners(),Qn&&(this._beforeunloadEventListener&&removeEventListener('beforeunload',this._beforeunloadEventListener,!1),this._offlineEventListener)){const n=Ws.indexOf(this._offlineEventListener);n!==-1&&Ws.splice(n,1);}this.readyState='closed',this.id=null,this.emitReserved('close',e,s),this.writeBuffer=[],this._prevBufferLen=0;}}}Tt.protocol=Jr;class Ru extends Tt{constructor(){super(...arguments),this._upgrades=[];}onOpen(){if(super.onOpen(),this.readyState==='open'&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e]);}_probe(e){let s=this.createTransport(e),n=!1;Tt.priorWebsocketSuccess=!1;const o=() => {n||(s.send([{type:'ping',data:'probe'}]),s.once('packet',f => {if(!n)if(f.type==='pong'&&f.data==='probe'){if(this.upgrading=!0,this.emitReserved('upgrading',s),!s)return;Tt.priorWebsocketSuccess=s.name==='websocket',this.transport.pause(() => {n||this.readyState!=='closed'&&(u(),this.setTransport(s),s.send([{type:'upgrade'}]),this.emitReserved('upgrade',s),s=null,this.upgrading=!1,this.flush());});}else{const y=new Error('probe error');y.transport=s.name,this.emitReserved('upgradeError',y);}}));};function i(){n||(n=!0,u(),s.close(),s=null);}const r=f => {const y=new Error('probe error: '+f);y.transport=s.name,i(),this.emitReserved('upgradeError',y);};function l(){r('transport closed');}function c(){r('socket closed');}function h(f){s&&f.name!==s.name&&i();}const u=() => {s.removeListener('open',o),s.removeListener('error',r),s.removeListener('close',l),this.off('close',c),this.off('upgrading',h);};s.once('open',o),s.once('error',r),s.once('close',l),this.once('close',c),this.once('upgrading',h),this._upgrades.indexOf('webtransport')!==-1&&e!=='webtransport'?this.setTimeoutFn(() => {n||s.open();},200):s.open();}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e);}_filterUpgrades(e){const s=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&s.push(e[n]);return s;}}const Du=class extends Ru{constructor(e,s={}){const n=typeof e=='object'?e:s;(!n.transports||n.transports&&typeof n.transports[0]=='string')&&(n.transports=(n.transports||['polling','websocket','webtransport']).map(o => Su[o]).filter(o => !!o)),super(e,n);}};function Ou(t,e='',s){let n=t;s=s||typeof location<'u'&&location,t==null&&(t=s.protocol+'//'+s.host),typeof t=='string'&&(t.charAt(0)==='/'&&(t.charAt(1)==='/'?t=s.protocol+t:t=s.host+t),/^(https?|wss?):\/\//.test(t)||(typeof s<'u'?t=s.protocol+'//'+t:t='https://'+t),n=Jn(t)),n.port||(/^(http|ws)$/.test(n.protocol)?n.port='80':/^(http|ws)s$/.test(n.protocol)&&(n.port='443')),n.path=n.path||'/';const i=n.host.indexOf(':')!==-1?'['+n.host+']':n.host;return n.id=n.protocol+'://'+i+':'+n.port+e,n.href=n.protocol+'://'+i+(s&&s.port===n.port?'':':'+n.port),n;}const Pu=typeof ArrayBuffer=='function',Nu=t => typeof ArrayBuffer.isView=='function'?ArrayBuffer.isView(t):t.buffer instanceof ArrayBuffer,sl=Object.prototype.toString,Iu=typeof Blob=='function'||typeof Blob<'u'&&sl.call(Blob)==='[object BlobConstructor]',Mu=typeof File=='function'||typeof File<'u'&&sl.call(File)==='[object FileConstructor]';function Ao(t){return Pu&&(t instanceof ArrayBuffer||Nu(t))||Iu&&t instanceof Blob||Mu&&t instanceof File;}function zs(t,e){if(!t||typeof t!='object')return!1;if(Array.isArray(t)){for(let s=0,n=t.length;s<n;s++)if(zs(t[s]))return!0;return!1;}if(Ao(t))return!0;if(t.toJSON&&typeof t.toJSON=='function'&&arguments.length===1)return zs(t.toJSON(),!0);for(const s in t)if(Object.prototype.hasOwnProperty.call(t,s)&&zs(t[s]))return!0;return!1;}function Uu(t){const e=[],s=t.data,n=t;return n.data=Xn(s,e),n.attachments=e.length,{packet:n,buffers:e};}function Xn(t,e){if(!t)return t;if(Ao(t)){const s={_placeholder:!0,num:e.length};return e.push(t),s;}else if(Array.isArray(t)){const s=new Array(t.length);for(let n=0;n<t.length;n++)s[n]=Xn(t[n],e);return s;}else if(typeof t=='object'&&!(t instanceof Date)){const s={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(s[n]=Xn(t[n],e));return s;}return t;}function $u(t,e){return t.data=Zn(t.data,e),delete t.attachments,t;}function Zn(t,e){if(!t)return t;if(t&&t._placeholder===!0){if(typeof t.num=='number'&&t.num>=0&&t.num<e.length)return e[t.num];throw new Error('illegal attachments');}else if(Array.isArray(t))for(let s=0;s<t.length;s++)t[s]=Zn(t[s],e);else if(typeof t=='object')for(const s in t)Object.prototype.hasOwnProperty.call(t,s)&&(t[s]=Zn(t[s],e));return t;}const ju=['connect','connect_error','disconnect','disconnecting','newListener','removeListener'],Fu=5;let W;(function(t){t[t.CONNECT=0]='CONNECT',t[t.DISCONNECT=1]='DISCONNECT',t[t.EVENT=2]='EVENT',t[t.ACK=3]='ACK',t[t.CONNECT_ERROR=4]='CONNECT_ERROR',t[t.BINARY_EVENT=5]='BINARY_EVENT',t[t.BINARY_ACK=6]='BINARY_ACK';})(W||(W={}));class Vu{constructor(e){this.replacer=e;}encode(e){return(e.type===W.EVENT||e.type===W.ACK)&&zs(e)?this.encodeAsBinary({type:e.type===W.EVENT?W.BINARY_EVENT:W.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)];}encodeAsString(e){let s=''+e.type;return(e.type===W.BINARY_EVENT||e.type===W.BINARY_ACK)&&(s+=e.attachments+'-'),e.nsp&&e.nsp!=='/'&&(s+=e.nsp+','),e.id!=null&&(s+=e.id),e.data!=null&&(s+=JSON.stringify(e.data,this.replacer)),s;}encodeAsBinary(e){const s=Uu(e),n=this.encodeAsString(s.packet),o=s.buffers;return o.unshift(n),o;}}function vi(t){return Object.prototype.toString.call(t)==='[object Object]';}class Bo extends fe{constructor(e){super(),this.reviver=e;}add(e){let s;if(typeof e=='string'){if(this.reconstructor)throw new Error('got plaintext data when reconstructing a packet');s=this.decodeString(e);const n=s.type===W.BINARY_EVENT;n||s.type===W.BINARY_ACK?(s.type=n?W.EVENT:W.ACK,this.reconstructor=new Gu(s),s.attachments===0&&super.emitReserved('decoded',s)):super.emitReserved('decoded',s);}else if(Ao(e)||e.base64)if(this.reconstructor)s=this.reconstructor.takeBinaryData(e),s&&(this.reconstructor=null,super.emitReserved('decoded',s));else throw new Error('got binary data when not reconstructing a packet');else throw new Error('Unknown type: '+e);}decodeString(e){let s=0;const n={type:Number(e.charAt(0))};if(W[n.type]===void 0)throw new Error('unknown packet type '+n.type);if(n.type===W.BINARY_EVENT||n.type===W.BINARY_ACK){const i=s+1;for(;e.charAt(++s)!=='-'&&s!=e.length;);const r=e.substring(i,s);if(r!=Number(r)||e.charAt(s)!=='-')throw new Error('Illegal attachments');n.attachments=Number(r);}if(e.charAt(s+1)==='/'){const i=s+1;for(;++s&&!(e.charAt(s)===','||s===e.length););n.nsp=e.substring(i,s);}else n.nsp='/';const o=e.charAt(s+1);if(o!==''&&Number(o)==o){const i=s+1;for(;++s;){const r=e.charAt(s);if(r==null||Number(r)!=r){--s;break;}if(s===e.length)break;}n.id=Number(e.substring(i,s+1));}if(e.charAt(++s)){const i=this.tryParse(e.substr(s));if(Bo.isPayloadValid(n.type,i))n.data=i;else throw new Error('invalid payload');}return n;}tryParse(e){try{return JSON.parse(e,this.reviver);}catch{return!1;}}static isPayloadValid(e,s){switch(e){case W.CONNECT:return vi(s);case W.DISCONNECT:return s===void 0;case W.CONNECT_ERROR:return typeof s=='string'||vi(s);case W.EVENT:case W.BINARY_EVENT:return Array.isArray(s)&&(typeof s[0]=='number'||typeof s[0]=='string'&&ju.indexOf(s[0])===-1);case W.ACK:case W.BINARY_ACK:return Array.isArray(s);}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null);}}class Gu{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e;}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const s=$u(this.reconPack,this.buffers);return this.finishedReconstruction(),s;}return null;}finishedReconstruction(){this.reconPack=null,this.buffers=[];}}const qu=Object.freeze(Object.defineProperty({__proto__:null,Decoder:Bo,Encoder:Vu,get PacketType(){return W;},protocol:Fu},Symbol.toStringTag,{value:'Module'}));function $e(t,e,s){return t.on(e,s),function(){t.off(e,s);};}const Hu=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class nl extends fe{constructor(e,s,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=s,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open();}get disconnected(){return!this.connected;}subEvents(){if(this.subs)return;const e=this.io;this.subs=[$e(e,'open',this.onopen.bind(this)),$e(e,'packet',this.onpacket.bind(this)),$e(e,'error',this.onerror.bind(this)),$e(e,'close',this.onclose.bind(this))];}get active(){return!!this.subs;}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==='open'&&this.onopen(),this);}open(){return this.connect();}send(...e){return e.unshift('message'),this.emit.apply(this,e),this;}emit(e,...s){let n,o,i;if(Hu.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');if(s.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(s),this;const r={type:W.EVENT,data:s};if(r.options={},r.options.compress=this.flags.compress!==!1,typeof s[s.length-1]=='function'){const u=this.ids++,f=s.pop();this._registerAckCallback(u,f),r.id=u;}const l=(o=(n=this.io.engine)===null||n===void 0?void 0:n.transport)===null||o===void 0?void 0:o.writable,c=this.connected&&!(!((i=this.io.engine)===null||i===void 0)&&i._hasPingExpired());return this.flags.volatile&&!l||(c?(this.notifyOutgoingListeners(r),this.packet(r)):this.sendBuffer.push(r)),this.flags={},this;}_registerAckCallback(e,s){let n;const o=(n=this.flags.timeout)!==null&&n!==void 0?n:this._opts.ackTimeout;if(o===void 0){this.acks[e]=s;return;}const i=this.io.setTimeoutFn(() => {delete this.acks[e];for(let l=0;l<this.sendBuffer.length;l++)this.sendBuffer[l].id===e&&this.sendBuffer.splice(l,1);s.call(this,new Error('operation has timed out'));},o),r=(...l) => {this.io.clearTimeoutFn(i),s.apply(this,l);};r.withError=!0,this.acks[e]=r;}emitWithAck(e,...s){return new Promise((n,o) => {const i=(r,l) => r?o(r):n(l);i.withError=!0,s.push(i),this.emit(e,...s);});}_addToQueue(e){let s;typeof e[e.length-1]=='function'&&(s=e.pop());const n={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((o,...i) => n!==this._queue[0]?void 0:(o!==null?n.tryCount>this._opts.retries&&(this._queue.shift(),s&&s(o)):(this._queue.shift(),s&&s(null,...i)),n.pending=!1,this._drainQueue())),this._queue.push(n),this._drainQueue();}_drainQueue(e=!1){if(!this.connected||this._queue.length===0)return;const s=this._queue[0];s.pending&&!e||(s.pending=!0,s.tryCount++,this.flags=s.flags,this.emit.apply(this,s.args));}packet(e){e.nsp=this.nsp,this.io._packet(e);}onopen(){typeof this.auth=='function'?this.auth(e => {this._sendConnectPacket(e);}):this._sendConnectPacket(this.auth);}_sendConnectPacket(e){this.packet({type:W.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e});}onerror(e){this.connected||this.emitReserved('connect_error',e);}onclose(e,s){this.connected=!1,delete this.id,this.emitReserved('disconnect',e,s),this._clearAcks();}_clearAcks(){Object.keys(this.acks).forEach(e => {if(!this.sendBuffer.some(n => String(n.id)===e)){const n=this.acks[e];delete this.acks[e],n.withError&&n.call(this,new Error('socket has been disconnected'));}});}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case W.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved('connect_error',new Error('It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)'));break;case W.EVENT:case W.BINARY_EVENT:this.onevent(e);break;case W.ACK:case W.BINARY_ACK:this.onack(e);break;case W.DISCONNECT:this.ondisconnect();break;case W.CONNECT_ERROR:this.destroy();const n=new Error(e.data.message);n.data=e.data.data,this.emitReserved('connect_error',n);break;}}onevent(e){const s=e.data||[];e.id!=null&&s.push(this.ack(e.id)),this.connected?this.emitEvent(s):this.receiveBuffer.push(Object.freeze(s));}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const s=this._anyListeners.slice();for(const n of s)n.apply(this,e);}super.emit.apply(this,e),this._pid&&e.length&&typeof e[e.length-1]=='string'&&(this._lastOffset=e[e.length-1]);}ack(e){const s=this;let n=!1;return function(...o){n||(n=!0,s.packet({type:W.ACK,id:e,data:o}));};}onack(e){const s=this.acks[e.id];typeof s=='function'&&(delete this.acks[e.id],s.withError&&e.data.unshift(null),s.apply(this,e.data));}onconnect(e,s){this.id=e,this.recovered=s&&this._pid===s,this._pid=s,this.connected=!0,this.emitBuffered(),this.emitReserved('connect'),this._drainQueue(!0);}emitBuffered(){this.receiveBuffer.forEach(e => this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e => {this.notifyOutgoingListeners(e),this.packet(e);}),this.sendBuffer=[];}ondisconnect(){this.destroy(),this.onclose('io server disconnect');}destroy(){this.subs&&(this.subs.forEach(e => e()),this.subs=void 0),this.io._destroy(this);}disconnect(){return this.connected&&this.packet({type:W.DISCONNECT}),this.destroy(),this.connected&&this.onclose('io client disconnect'),this;}close(){return this.disconnect();}compress(e){return this.flags.compress=e,this;}get volatile(){return this.flags.volatile=!0,this;}timeout(e){return this.flags.timeout=e,this;}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this;}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this;}offAny(e){if(!this._anyListeners)return this;if(e){const s=this._anyListeners;for(let n=0;n<s.length;n++)if(e===s[n])return s.splice(n,1),this;}else this._anyListeners=[];return this;}listenersAny(){return this._anyListeners||[];}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this;}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this;}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const s=this._anyOutgoingListeners;for(let n=0;n<s.length;n++)if(e===s[n])return s.splice(n,1),this;}else this._anyOutgoingListeners=[];return this;}listenersAnyOutgoing(){return this._anyOutgoingListeners||[];}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const s=this._anyOutgoingListeners.slice();for(const n of s)n.apply(this,e.data);}}}function is(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0;}is.prototype.duration=function(){let t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){const e=Math.random(),s=Math.floor(e*this.jitter*t);t=(Math.floor(e*10)&1)==0?t-s:t+s;}return Math.min(t,this.max)|0;};is.prototype.reset=function(){this.attempts=0;};is.prototype.setMin=function(t){this.ms=t;};is.prototype.setMax=function(t){this.max=t;};is.prototype.setJitter=function(t){this.jitter=t;};class eo extends fe{constructor(e,s){let n;super(),this.nsps={},this.subs=[],e&&typeof e=='object'&&(s=e,e=void 0),s=s||{},s.path=s.path||'/socket.io',this.opts=s,_n(this,s),this.reconnection(s.reconnection!==!1),this.reconnectionAttempts(s.reconnectionAttempts||1/0),this.reconnectionDelay(s.reconnectionDelay||1e3),this.reconnectionDelayMax(s.reconnectionDelayMax||5e3),this.randomizationFactor((n=s.randomizationFactor)!==null&&n!==void 0?n:.5),this.backoff=new is({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(s.timeout==null?2e4:s.timeout),this._readyState='closed',this.uri=e;const o=s.parser||qu;this.encoder=new o.Encoder,this.decoder=new o.Decoder,this._autoConnect=s.autoConnect!==!1,this._autoConnect&&this.open();}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection;}reconnectionAttempts(e){return e===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=e,this);}reconnectionDelay(e){let s;return e===void 0?this._reconnectionDelay:(this._reconnectionDelay=e,(s=this.backoff)===null||s===void 0||s.setMin(e),this);}randomizationFactor(e){let s;return e===void 0?this._randomizationFactor:(this._randomizationFactor=e,(s=this.backoff)===null||s===void 0||s.setJitter(e),this);}reconnectionDelayMax(e){let s;return e===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,(s=this.backoff)===null||s===void 0||s.setMax(e),this);}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout;}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect();}open(e){if(~this._readyState.indexOf('open'))return this;this.engine=new Du(this.uri,this.opts);const s=this.engine,n=this;this._readyState='opening',this.skipReconnect=!1;const o=$e(s,'open',function(){n.onopen(),e&&e();}),i=l => {this.cleanup(),this._readyState='closed',this.emitReserved('error',l),e?e(l):this.maybeReconnectOnOpen();},r=$e(s,'error',i);if(this._timeout!==!1){const l=this._timeout,c=this.setTimeoutFn(() => {o(),i(new Error('timeout')),s.close();},l);this.opts.autoUnref&&c.unref(),this.subs.push(() => {this.clearTimeoutFn(c);});}return this.subs.push(o),this.subs.push(r),this;}connect(e){return this.open(e);}onopen(){this.cleanup(),this._readyState='open',this.emitReserved('open');const e=this.engine;this.subs.push($e(e,'ping',this.onping.bind(this)),$e(e,'data',this.ondata.bind(this)),$e(e,'error',this.onerror.bind(this)),$e(e,'close',this.onclose.bind(this)),$e(this.decoder,'decoded',this.ondecoded.bind(this)));}onping(){this.emitReserved('ping');}ondata(e){try{this.decoder.add(e);}catch(s){this.onclose('parse error',s);}}ondecoded(e){bn(() => {this.emitReserved('packet',e);},this.setTimeoutFn);}onerror(e){this.emitReserved('error',e);}socket(e,s){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new nl(this,e,s),this.nsps[e]=n),n;}_destroy(e){const s=Object.keys(this.nsps);for(const n of s)if(this.nsps[n].active)return;this._close();}_packet(e){const s=this.encoder.encode(e);for(let n=0;n<s.length;n++)this.engine.write(s[n],e.options);}cleanup(){this.subs.forEach(e => e()),this.subs.length=0,this.decoder.destroy();}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose('forced close');}disconnect(){return this._close();}onclose(e,s){let n;this.cleanup(),(n=this.engine)===null||n===void 0||n.close(),this.backoff.reset(),this._readyState='closed',this.emitReserved('close',e,s),this._reconnection&&!this.skipReconnect&&this.reconnect();}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved('reconnect_failed'),this._reconnecting=!1;else{const s=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(() => {e.skipReconnect||(this.emitReserved('reconnect_attempt',e.backoff.attempts),!e.skipReconnect&&e.open(o => {o?(e._reconnecting=!1,e.reconnect(),this.emitReserved('reconnect_error',o)):e.onreconnect();}));},s);this.opts.autoUnref&&n.unref(),this.subs.push(() => {this.clearTimeoutFn(n);});}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved('reconnect',e);}}const ds={};function Ys(t,e){typeof t=='object'&&(e=t,t=void 0),e=e||{};const s=Ou(t,e.path||'/socket.io'),n=s.source,o=s.id,i=s.path,r=ds[o]&&i in ds[o].nsps,l=e.forceNew||e['force new connection']||e.multiplex===!1||r;let c;return l?c=new eo(n,e):(ds[o]||(ds[o]=new eo(n,e)),c=ds[o]),s.query&&!e.query&&(e.query=s.queryKey),c.socket(s.path,e);}Object.assign(Ys,{Manager:eo,Socket:nl,io:Ys,connect:Ys});function Ku(){return ol().__VUE_DEVTOOLS_GLOBAL_HOOK__;}function ol(){return typeof navigator<'u'&&typeof window<'u'?window:typeof globalThis<'u'?globalThis:{};}const Wu=typeof Proxy=='function',zu='devtools-plugin:setup',Yu='plugin:settings:set';let Vt,to;function Ju(){let t;return Vt!==void 0||(typeof window<'u'&&window.performance?(Vt=!0,to=window.performance):typeof globalThis<'u'&&(!((t=globalThis.perf_hooks)===null||t===void 0)&&t.performance)?(Vt=!0,to=globalThis.perf_hooks.performance):Vt=!1),Vt;}function Qu(){return Ju()?to.now():Date.now();}class Xu{constructor(e,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=s;const n={};if(e.settings)for(const r in e.settings){const l=e.settings[r];n[r]=l.defaultValue;}const o=`__vue-devtools-plugin-settings__${e.id}`;let i=Object.assign({},n);try{const r=localStorage.getItem(o),l=JSON.parse(r);Object.assign(i,l);}catch{}this.fallbacks={getSettings(){return i;},setSettings(r){try{localStorage.setItem(o,JSON.stringify(r));}catch{}i=r;},now(){return Qu();}},s&&s.on(Yu,(r,l) => {r===this.plugin.id&&this.fallbacks.setSettings(l);}),this.proxiedOn=new Proxy({},{get:(r,l) => this.target?this.target.on[l]:(...c) => {this.onQueue.push({method:l,args:c});}}),this.proxiedTarget=new Proxy({},{get:(r,l) => this.target?this.target[l]:l==='on'?this.proxiedOn:Object.keys(this.fallbacks).includes(l)?(...c) => (this.targetQueue.push({method:l,args:c,resolve:() => {}}),this.fallbacks[l](...c)):(...c) => new Promise(h => {this.targetQueue.push({method:l,args:c,resolve:h});})});}async setRealTarget(e){this.target=e;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args));}}function Zu(t,e){const s=t,n=ol(),o=Ku(),i=Wu&&s.enableEarlyProxy;if(o&&(n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!i))o.emit(zu,t,e);else{const r=i?new Xu(s,o):null;(n.__VUE_DEVTOOLS_PLUGINS__=n.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:e,proxy:r}),r&&e(r.proxiedTarget);}}/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */const ed='store';function rs(t,e){Object.keys(t).forEach(function(s){return e(t[s],s);});}function il(t){return t!==null&&typeof t=='object';}function td(t){return t&&typeof t.then=='function';}function sd(t,e){return function(){return t(e);};}function rl(t,e,s){return e.indexOf(t)<0&&(s&&s.prepend?e.unshift(t):e.push(t)),function(){const n=e.indexOf(t);n>-1&&e.splice(n,1);};}function ll(t,e){t._actions=Object.create(null),t._mutations=Object.create(null),t._wrappedGetters=Object.create(null),t._modulesNamespaceMap=Object.create(null);const s=t.state;vn(t,s,[],t._modules.root,!0),Ro(t,s,e);}function Ro(t,e,s){const n=t._state,o=t._scope;t.getters={},t._makeLocalGettersCache=Object.create(null);const i=t._wrappedGetters,r={},l={},c=Vl(!0);c.run(function(){rs(i,function(h,u){r[u]=sd(h,t),l[u]=Ne(function(){return r[u]();}),Object.defineProperty(t.getters,u,{get:function(){return l[u].value;},enumerable:!0});});}),t._state=Os({data:e}),t._scope=c,t.strict&&ld(t),n&&s&&t._withCommit(function(){n.data=null;}),o&&o.stop();}function vn(t,e,s,n,o){const i=!s.length,r=t._modules.getNamespace(s);if(n.namespaced&&(t._modulesNamespaceMap[r],t._modulesNamespaceMap[r]=n),!i&&!o){const l=Do(e,s.slice(0,-1)),c=s[s.length-1];t._withCommit(function(){l[c]=n.state;});}const h=n.context=nd(t,r,s);n.forEachMutation(function(u,f){const y=r+f;od(t,y,u,h);}),n.forEachAction(function(u,f){const y=u.root?f:r+f,_=u.handler||u;id(t,y,_,h);}),n.forEachGetter(function(u,f){const y=r+f;rd(t,y,u,h);}),n.forEachChild(function(u,f){vn(t,e,s.concat(f),u,o);});}function nd(t,e,s){const n=e==='',o={dispatch:n?t.dispatch:function(i,r,l){let c=rn(i,r,l),h=c.payload,u=c.options,f=c.type;return(!u||!u.root)&&(f=e+f),t.dispatch(f,h);},commit:n?t.commit:function(i,r,l){let c=rn(i,r,l),h=c.payload,u=c.options,f=c.type;(!u||!u.root)&&(f=e+f),t.commit(f,h,u);}};return Object.defineProperties(o,{getters:{get:n?function(){return t.getters;}:function(){return al(t,e);}},state:{get:function(){return Do(t.state,s);}}}),o;}function al(t,e){if(!t._makeLocalGettersCache[e]){const s={},n=e.length;Object.keys(t.getters).forEach(function(o){if(o.slice(0,n)===e){const i=o.slice(n);Object.defineProperty(s,i,{get:function(){return t.getters[o];},enumerable:!0});}}),t._makeLocalGettersCache[e]=s;}return t._makeLocalGettersCache[e];}function od(t,e,s,n){const o=t._mutations[e]||(t._mutations[e]=[]);o.push(function(r){s.call(t,n.state,r);});}function id(t,e,s,n){const o=t._actions[e]||(t._actions[e]=[]);o.push(function(r){let l=s.call(t,{dispatch:n.dispatch,commit:n.commit,getters:n.getters,state:n.state,rootGetters:t.getters,rootState:t.state},r);return td(l)||(l=Promise.resolve(l)),t._devtoolHook?l.catch(function(c){throw t._devtoolHook.emit('vuex:error',c),c;}):l;});}function rd(t,e,s,n){t._wrappedGetters[e]||(t._wrappedGetters[e]=function(i){return s(n.state,n.getters,i.state,i.getters);});}function ld(t){Xt(function(){return t._state.data;},function(){},{deep:!0,flush:'sync'});}function Do(t,e){return e.reduce(function(s,n){return s[n];},t);}function rn(t,e,s){return il(t)&&t.type&&(s=e,e=t,t=t.type),{type:t,payload:e,options:s};}let ad='vuex bindings',ki='vuex:mutations',Nn='vuex:actions',Gt='vuex',cd=0;function ud(t,e){Zu({id:'org.vuejs.vuex',app:t,label:'Vuex',homepage:'https://next.vuex.vuejs.org/',logo:'https://vuejs.org/images/icons/favicon-96x96.png',packageName:'vuex',componentStateTypes:[ad]},function(s){s.addTimelineLayer({id:ki,label:'Vuex Mutations',color:wi}),s.addTimelineLayer({id:Nn,label:'Vuex Actions',color:wi}),s.addInspector({id:Gt,label:'Vuex',icon:'storage',treeFilterPlaceholder:'Filter stores...'}),s.on.getInspectorTree(function(n){if(n.app===t&&n.inspectorId===Gt)if(n.filter){const o=[];fl(o,e._modules.root,n.filter,''),n.rootNodes=o;}else n.rootNodes=[dl(e._modules.root,'')];}),s.on.getInspectorState(function(n){if(n.app===t&&n.inspectorId===Gt){const o=n.nodeId;al(e,o),n.state=hd(gd(e._modules,o),o==='root'?e.getters:e._makeLocalGettersCache,o);}}),s.on.editInspectorState(function(n){if(n.app===t&&n.inspectorId===Gt){let o=n.nodeId,i=n.path;o!=='root'&&(i=o.split('/').filter(Boolean).concat(i)),e._withCommit(function(){n.set(e._state.data,i,n.state.value);});}}),e.subscribe(function(n,o){const i={};n.payload&&(i.payload=n.payload),i.state=o,s.notifyComponentUpdate(),s.sendInspectorTree(Gt),s.sendInspectorState(Gt),s.addTimelineEvent({layerId:ki,event:{time:Date.now(),title:n.type,data:i}});}),e.subscribeAction({before:function(n,o){const i={};n.payload&&(i.payload=n.payload),n._id=cd++,n._time=Date.now(),i.state=o,s.addTimelineEvent({layerId:Nn,event:{time:n._time,title:n.type,groupId:n._id,subtitle:'start',data:i}});},after:function(n,o){const i={},r=Date.now()-n._time;i.duration={_custom:{type:'duration',display:r+'ms',tooltip:'Action duration',value:r}},n.payload&&(i.payload=n.payload),i.state=o,s.addTimelineEvent({layerId:Nn,event:{time:Date.now(),title:n.type,groupId:n._id,subtitle:'end',data:i}});}});});}var wi=8702998,dd=6710886,fd=16777215,cl={label:'namespaced',textColor:fd,backgroundColor:dd};function ul(t){return t&&t!=='root'?t.split('/').slice(-2,-1)[0]:'Root';}function dl(t,e){return{id:e||'root',label:ul(e),tags:t.namespaced?[cl]:[],children:Object.keys(t._children).map(function(s){return dl(t._children[s],e+s+'/');})};}function fl(t,e,s,n){n.includes(s)&&t.push({id:n||'root',label:n.endsWith('/')?n.slice(0,n.length-1):n||'Root',tags:e.namespaced?[cl]:[]}),Object.keys(e._children).forEach(function(o){fl(t,e._children[o],s,n+o+'/');});}function hd(t,e,s){e=s==='root'?e:e[s];const n=Object.keys(e),o={state:Object.keys(t.state).map(function(r){return{key:r,editable:!0,value:t.state[r]};})};if(n.length){const i=pd(e);o.getters=Object.keys(i).map(function(r){return{key:r.endsWith('/')?ul(r):r,editable:!1,value:so(function(){return i[r];})};});}return o;}function pd(t){const e={};return Object.keys(t).forEach(function(s){const n=s.split('/');if(n.length>1){let o=e,i=n.pop();n.forEach(function(r){o[r]||(o[r]={_custom:{value:{},display:r,tooltip:'Module',abstract:!0}}),o=o[r]._custom.value;}),o[i]=so(function(){return t[s];});}else e[s]=so(function(){return t[s];});}),e;}function gd(t,e){const s=e.split('/').filter(function(n){return n;});return s.reduce(function(n,o,i){const r=n[o];if(!r)throw new Error('Missing module "'+o+'" for path "'+e+'".');return i===s.length-1?r:r._children;},e==='root'?t:t.root._children);}function so(t){try{return t();}catch(e){return e;}}const Ge=function(e,s){this.runtime=s,this._children=Object.create(null),this._rawModule=e;const n=e.state;this.state=(typeof n=='function'?n():n)||{};},hl={namespaced:{configurable:!0}};hl.namespaced.get=function(){return!!this._rawModule.namespaced;};Ge.prototype.addChild=function(e,s){this._children[e]=s;};Ge.prototype.removeChild=function(e){delete this._children[e];};Ge.prototype.getChild=function(e){return this._children[e];};Ge.prototype.hasChild=function(e){return e in this._children;};Ge.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters);};Ge.prototype.forEachChild=function(e){rs(this._children,e);};Ge.prototype.forEachGetter=function(e){this._rawModule.getters&&rs(this._rawModule.getters,e);};Ge.prototype.forEachAction=function(e){this._rawModule.actions&&rs(this._rawModule.actions,e);};Ge.prototype.forEachMutation=function(e){this._rawModule.mutations&&rs(this._rawModule.mutations,e);};Object.defineProperties(Ge.prototype,hl);const It=function(e){this.register([],e,!1);};It.prototype.get=function(e){return e.reduce(function(s,n){return s.getChild(n);},this.root);};It.prototype.getNamespace=function(e){let s=this.root;return e.reduce(function(n,o){return s=s.getChild(o),n+(s.namespaced?o+'/':'');},'');};It.prototype.update=function(e){pl([],this.root,e);};It.prototype.register=function(e,s,n){const o=this;n===void 0&&(n=!0);const i=new Ge(s,n);if(e.length===0)this.root=i;else{const r=this.get(e.slice(0,-1));r.addChild(e[e.length-1],i);}s.modules&&rs(s.modules,function(l,c){o.register(e.concat(c),l,n);});};It.prototype.unregister=function(e){const s=this.get(e.slice(0,-1)),n=e[e.length-1],o=s.getChild(n);o&&o.runtime&&s.removeChild(n);};It.prototype.isRegistered=function(e){const s=this.get(e.slice(0,-1)),n=e[e.length-1];return s?s.hasChild(n):!1;};function pl(t,e,s){if(e.update(s),s.modules)for(const n in s.modules){if(!e.getChild(n))return;pl(t.concat(n),e.getChild(n),s.modules[n]);}}function md(t){return new Le(t);}var Le=function(e){const s=this;e===void 0&&(e={});let n=e.plugins;n===void 0&&(n=[]);let o=e.strict;o===void 0&&(o=!1);const i=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new It(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=i;const r=this,l=this,c=l.dispatch,h=l.commit;this.dispatch=function(y,_){return c.call(r,y,_);},this.commit=function(y,_,D){return h.call(r,y,_,D);},this.strict=o;const u=this._modules.root.state;vn(this,u,[],this._modules.root),Ro(this,u),n.forEach(function(f){return f(s);});},Oo={state:{configurable:!0}};Le.prototype.install=function(e,s){e.provide(s||ed,this),e.config.globalProperties.$store=this;const n=this._devtools!==void 0?this._devtools:!1;n&&ud(e,this);};Oo.state.get=function(){return this._state.data;};Oo.state.set=function(t){};Le.prototype.commit=function(e,s,n){const o=this,i=rn(e,s,n),r=i.type,l=i.payload,c={type:r,payload:l},h=this._mutations[r];h&&(this._withCommit(function(){h.forEach(function(f){f(l);});}),this._subscribers.slice().forEach(function(u){return u(c,o.state);}));};Le.prototype.dispatch=function(e,s){const n=this,o=rn(e,s),i=o.type,r=o.payload,l={type:i,payload:r},c=this._actions[i];if(c){try{this._actionSubscribers.slice().filter(function(u){return u.before;}).forEach(function(u){return u.before(l,n.state);});}catch{}const h=c.length>1?Promise.all(c.map(function(u){return u(r);})):c[0](r);return new Promise(function(u,f){h.then(function(y){try{n._actionSubscribers.filter(function(_){return _.after;}).forEach(function(_){return _.after(l,n.state);});}catch{}u(y);},function(y){try{n._actionSubscribers.filter(function(_){return _.error;}).forEach(function(_){return _.error(l,n.state,y);});}catch{}f(y);});});}};Le.prototype.subscribe=function(e,s){return rl(e,this._subscribers,s);};Le.prototype.subscribeAction=function(e,s){const n=typeof e=='function'?{before:e}:e;return rl(n,this._actionSubscribers,s);};Le.prototype.watch=function(e,s,n){const o=this;return Xt(function(){return e(o.state,o.getters);},s,Object.assign({},n));};Le.prototype.replaceState=function(e){const s=this;this._withCommit(function(){s._state.data=e;});};Le.prototype.registerModule=function(e,s,n){n===void 0&&(n={}),typeof e=='string'&&(e=[e]),this._modules.register(e,s),vn(this,this.state,e,this._modules.get(e),n.preserveState),Ro(this,this.state);};Le.prototype.unregisterModule=function(e){const s=this;typeof e=='string'&&(e=[e]),this._modules.unregister(e),this._withCommit(function(){const n=Do(s.state,e.slice(0,-1));delete n[e[e.length-1]];}),ll(this);};Le.prototype.hasModule=function(e){return typeof e=='string'&&(e=[e]),this._modules.isRegistered(e);};Le.prototype.hotUpdate=function(e){this._modules.update(e),ll(this,!0);};Le.prototype._withCommit=function(e){const s=this._committing;this._committing=!0,e(),this._committing=s;};Object.defineProperties(Le.prototype,Oo);const Mt=_d(function(t,e){const s={};return yd(e).forEach(function(n){let o=n.key,i=n.val;i=t+i,s[o]=function(){if(!(t&&!vd(this.$store,'mapGetters',t)))return this.$store.getters[i];},s[o].vuex=!0;}),s;});function yd(t){return bd(t)?Array.isArray(t)?t.map(function(e){return{key:e,val:e};}):Object.keys(t).map(function(e){return{key:e,val:t[e]};}):[];}function bd(t){return Array.isArray(t)||il(t);}function _d(t){return function(e,s){return typeof e!='string'?(s=e,e=''):e.charAt(e.length-1)!=='/'&&(e+='/'),t(e,s);};}function vd(t,e,s){const n=t._modulesNamespaceMap[s];return n;}const Ue=(t,e) => {const s=t.__vccOpts||t;for(const[n,o]of e)s[n]=o;return s;},kd={name:'App',data(){return{socket:Ys({withCredentials:!0,transports:['websocket','polling']}),activityTimeout:null,inactivityLimit:60*60*1e3};},computed:{...Mt(['isAuthenticated'])},provide(){return{socket:this.socket};},created(){const{commit:t}=this.$store;this.$root.socket=this.socket,this.socket.on('connect_error',e => {console.error('Socket.io connection error:',e);}),fetch('/api/users/me',{credentials:'include'}).then(e => e.json()).then(({authenticated:e,user:s}) => {t('setAuthenticated',e),e&&s&&(t('setUser',s),this.initActivityTracker());}).catch(e => {console.error('Error checking authentication status:',e);});},methods:{redirect(t){this.$router.push(t);},logout(){fetch('/api/logout',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include'}).then(t => {if(!t.ok)throw new Error('Logout failed');return t.json();}).then(() => {this.$store.commit('setAuthenticated',!1),this.$store.commit('setUser',null),this.$router.push('/login');}).catch(t => {console.error('Error during logout:',t);});},initActivityTracker(){const t=() => {this.activityTimeout&&clearTimeout(this.activityTimeout),this.activityTimeout=setTimeout(this.handleInactivityLogout,this.inactivityLimit);};['mousedown','keydown','touchstart','scroll'].forEach(e => {window.addEventListener(e,t);}),t();},handleInactivityLogout(){console.log('Logging out due to inactivity'),fetch('/api/logout',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include'}).then(() => {this.$store.commit('setAuthenticated',!1),this.$store.commit('setUser',null),this.$router.push('/login'),alert('You have been logged out due to inactivity.');}).catch(t => {console.error('Error during inactivity logout:',t);});}}},wd={class:'navbar navbar-expand-md navbar-dark bg-dark'},Ed={id:'navbarNav',class:'collapse navbar-collapse mx-2'},xd={class:'navbar-nav'},Td={class:'nav-item'},Sd={class:'nav-item'},Ld={class:'nav-item'},Cd={key:0,class:'nav-item'},Ad={key:1,class:'nav-item'},Bd={key:2,class:'nav-item'},Rd={class:'nav-item'},Dd={class:'container-fluid py-4'};function Od(t,e,s,n,o,i){const r=Nt('router-view');return g(),m(ae,null,[a('nav',wd,[e[7]||(e[7]=a('button',{class:'navbar-toggler mx-2 mb-2',type:'button','data-bs-toggle':'collapse','data-bs-target':'#navbarNav'},[a('span',{class:'navbar-toggler-icon'})],-1)),a('div',Ed,[a('ul',xd,[a('li',Td,[a('a',{class:'nav-link',href:'#',onClick:e[0]||(e[0]=l => i.redirect('/booking-lists'))},'Booking Lists')]),a('li',Sd,[a('a',{class:'nav-link',href:'#',onClick:e[1]||(e[1]=l => i.redirect('/groups'))},'My Groups')]),a('li',Ld,[a('a',{class:'nav-link',href:'#',onClick:e[2]||(e[2]=l => i.redirect('/my-bookings'))},'My Bookings')]),t.isAuthenticated?K('',!0):(g(),m('li',Cd,[a('a',{class:'nav-link',href:'#',onClick:e[3]||(e[3]=l => i.redirect('/login'))},'Login')])),t.isAuthenticated?K('',!0):(g(),m('li',Ad,[a('a',{class:'nav-link',href:'#',onClick:e[4]||(e[4]=l => i.redirect('/register'))},'Register')])),t.isAuthenticated?(g(),m('li',Bd,[a('a',{class:'nav-link',href:'#',onClick:e[5]||(e[5]=(...l) => i.logout&&i.logout(...l))},'Logout')])):K('',!0),a('li',Rd,[a('a',{class:'nav-link',href:'#',onClick:e[6]||(e[6]=l => i.redirect('/admin/login'))},'Admin')])])])]),a('section',Dd,[de(r)])],64);}const Pd=Ue(kd,[['render',Od]]);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Ht=typeof document<'u';function gl(t){return typeof t=='object'||'displayName'in t||'props'in t||'__vccOpts'in t;}function Nd(t){return t.__esModule||t[Symbol.toStringTag]==='Module'||t.default&&gl(t.default);}const X=Object.assign;function In(t,e){const s={};for(const n in e){const o=e[n];s[n]=Ve(o)?o.map(t):t(o);}return s;}const ks=() => {},Ve=Array.isArray,ml=/#/g,Id=/&/g,Md=/\//g,Ud=/=/g,$d=/\?/g,yl=/\+/g,jd=/%5B/g,Fd=/%5D/g,bl=/%5E/g,Vd=/%60/g,_l=/%7B/g,Gd=/%7C/g,vl=/%7D/g,qd=/%20/g;function Po(t){return encodeURI(''+t).replace(Gd,'|').replace(jd,'[').replace(Fd,']');}function Hd(t){return Po(t).replace(_l,'{').replace(vl,'}').replace(bl,'^');}function no(t){return Po(t).replace(yl,'%2B').replace(qd,'+').replace(ml,'%23').replace(Id,'%26').replace(Vd,'`').replace(_l,'{').replace(vl,'}').replace(bl,'^');}function Kd(t){return no(t).replace(Ud,'%3D');}function Wd(t){return Po(t).replace(ml,'%23').replace($d,'%3F');}function zd(t){return t==null?'':Wd(t).replace(Md,'%2F');}function As(t){try{return decodeURIComponent(''+t);}catch{}return''+t;}const Yd=/\/$/,Jd=t => t.replace(Yd,'');function Mn(t,e,s='/'){let n,o={},i='',r='';const l=e.indexOf('#');let c=e.indexOf('?');return l<c&&l>=0&&(c=-1),c>-1&&(n=e.slice(0,c),i=e.slice(c+1,l>-1?l:e.length),o=t(i)),l>-1&&(n=n||e.slice(0,l),r=e.slice(l,e.length)),n=ef(n??e,s),{fullPath:n+(i&&'?')+i+r,path:n,query:o,hash:As(r)};}function Qd(t,e){const s=e.query?t(e.query):'';return e.path+(s&&'?')+s+(e.hash||'');}function Ei(t,e){return!e||!t.toLowerCase().startsWith(e.toLowerCase())?t:t.slice(e.length)||'/';}function Xd(t,e,s){const n=e.matched.length-1,o=s.matched.length-1;return n>-1&&n===o&&ns(e.matched[n],s.matched[o])&&kl(e.params,s.params)&&t(e.query)===t(s.query)&&e.hash===s.hash;}function ns(t,e){return(t.aliasOf||t)===(e.aliasOf||e);}function kl(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(!Zd(t[s],e[s]))return!1;return!0;}function Zd(t,e){return Ve(t)?xi(t,e):Ve(e)?xi(e,t):t===e;}function xi(t,e){return Ve(e)?t.length===e.length&&t.every((s,n) => s===e[n]):t.length===1&&t[0]===e;}function ef(t,e){if(t.startsWith('/'))return t;if(!t)return e;const s=e.split('/'),n=t.split('/'),o=n[n.length-1];(o==='..'||o==='.')&&n.push('');let i=s.length-1,r,l;for(r=0;r<n.length;r++)if(l=n[r],l!=='.')if(l==='..')i>1&&i--;else break;return s.slice(0,i).join('/')+'/'+n.slice(r).join('/');}const _t={path:'/',name:void 0,params:{},query:{},hash:'',fullPath:'/',matched:[],meta:{},redirectedFrom:void 0};let Bs;(function(t){t.pop='pop',t.push='push';})(Bs||(Bs={}));let ws;(function(t){t.back='back',t.forward='forward',t.unknown='';})(ws||(ws={}));function tf(t){if(!t)if(Ht){const e=document.querySelector('base');t=e&&e.getAttribute('href')||'/',t=t.replace(/^\w+:\/\/[^\/]+/,'');}else t='/';return t[0]!=='/'&&t[0]!=='#'&&(t='/'+t),Jd(t);}const sf=/^[^#]+#/;function nf(t,e){return t.replace(sf,'#')+e;}function of(t,e){const s=document.documentElement.getBoundingClientRect(),n=t.getBoundingClientRect();return{behavior:e.behavior,left:n.left-s.left-(e.left||0),top:n.top-s.top-(e.top||0)};}const kn=() => ({left:window.scrollX,top:window.scrollY});function rf(t){let e;if('el'in t){const s=t.el,n=typeof s=='string'&&s.startsWith('#'),o=typeof s=='string'?n?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!o)return;e=of(o,t);}else e=t;'scrollBehavior'in document.documentElement.style?window.scrollTo(e):window.scrollTo(e.left!=null?e.left:window.scrollX,e.top!=null?e.top:window.scrollY);}function Ti(t,e){return(history.state?history.state.position-e:-1)+t;}const oo=new Map;function lf(t,e){oo.set(t,e);}function af(t){const e=oo.get(t);return oo.delete(t),e;}const cf=() => location.protocol+'//'+location.host;function wl(t,e){const{pathname:s,search:n,hash:o}=e,i=t.indexOf('#');if(i>-1){let l=o.includes(t.slice(i))?t.slice(i).length:1,c=o.slice(l);return c[0]!=='/'&&(c='/'+c),Ei(c,'');}return Ei(s,t)+n+o;}function uf(t,e,s,n){let o=[],i=[],r=null;const l=({state:y}) => {const _=wl(t,location),D=s.value,O=e.value;let q=0;if(y){if(s.value=_,e.value=y,r&&r===D){r=null;return;}q=O?y.position-O.position:0;}else n(_);o.forEach($ => {$(s.value,D,{delta:q,type:Bs.pop,direction:q?q>0?ws.forward:ws.back:ws.unknown});});};function c(){r=s.value;}function h(y){o.push(y);const _=() => {const D=o.indexOf(y);D>-1&&o.splice(D,1);};return i.push(_),_;}function u(){const{history:y}=window;y.state&&y.replaceState(X({},y.state,{scroll:kn()}),'');}function f(){for(const y of i)y();i=[],window.removeEventListener('popstate',l),window.removeEventListener('beforeunload',u);}return window.addEventListener('popstate',l),window.addEventListener('beforeunload',u,{passive:!0}),{pauseListeners:c,listen:h,destroy:f};}function Si(t,e,s,n=!1,o=!1){return{back:t,current:e,forward:s,replaced:n,position:window.history.length,scroll:o?kn():null};}function df(t){const{history:e,location:s}=window,n={value:wl(t,s)},o={value:e.state};o.value||i(n.value,{back:null,current:n.value,forward:null,position:e.length-1,replaced:!0,scroll:null},!0);function i(c,h,u){const f=t.indexOf('#'),y=f>-1?(s.host&&document.querySelector('base')?t:t.slice(f))+c:cf()+t+c;try{e[u?'replaceState':'pushState'](h,'',y),o.value=h;}catch(_){console.error(_),s[u?'replace':'assign'](y);}}function r(c,h){const u=X({},e.state,Si(o.value.back,c,o.value.forward,!0),h,{position:o.value.position});i(c,u,!0),n.value=c;}function l(c,h){const u=X({},o.value,e.state,{forward:c,scroll:kn()});i(u.current,u,!0);const f=X({},Si(n.value,c,null),{position:u.position+1},h);i(c,f,!1),n.value=c;}return{location:n,state:o,push:l,replace:r};}function ff(t){t=tf(t);const e=df(t),s=uf(t,e.state,e.location,e.replace);function n(i,r=!0){r||s.pauseListeners(),history.go(i);}const o=X({location:'',base:t,go:n,createHref:nf.bind(null,t)},e,s);return Object.defineProperty(o,'location',{enumerable:!0,get:() => e.location.value}),Object.defineProperty(o,'state',{enumerable:!0,get:() => e.state.value}),o;}function hf(t){return typeof t=='string'||t&&typeof t=='object';}function El(t){return typeof t=='string'||typeof t=='symbol';}const xl=Symbol('');let Li;(function(t){t[t.aborted=4]='aborted',t[t.cancelled=8]='cancelled',t[t.duplicated=16]='duplicated';})(Li||(Li={}));function os(t,e){return X(new Error,{type:t,[xl]:!0},e);}function lt(t,e){return t instanceof Error&&xl in t&&(e==null||!!(t.type&e));}const Ci='[^/]+?',pf={sensitive:!1,strict:!1,start:!0,end:!0},gf=/[.+*?^${}()[\]/\\]/g;function mf(t,e){const s=X({},pf,e),n=[];let o=s.start?'^':'';const i=[];for(const h of t){const u=h.length?[]:[90];s.strict&&!h.length&&(o+='/');for(let f=0;f<h.length;f++){const y=h[f];let _=40+(s.sensitive?.25:0);if(y.type===0)f||(o+='/'),o+=y.value.replace(gf,'\\$&'),_+=40;else if(y.type===1){const{value:D,repeatable:O,optional:q,regexp:$}=y;i.push({name:D,repeatable:O,optional:q});const I=$||Ci;if(I!==Ci){_+=10;try{new RegExp(`(${I})`);}catch(P){throw new Error(`Invalid custom RegExp for param "${D}" (${I}): `+P.message);}}let j=O?`((?:${I})(?:/(?:${I}))*)`:`(${I})`;f||(j=q&&h.length<2?`(?:/${j})`:'/'+j),q&&(j+='?'),o+=j,_+=20,q&&(_+=-8),O&&(_+=-20),I==='.*'&&(_+=-50);}u.push(_);}n.push(u);}if(s.strict&&s.end){const h=n.length-1;n[h][n[h].length-1]+=.7000000000000001;}s.strict||(o+='/?'),s.end?o+='$':s.strict&&!o.endsWith('/')&&(o+='(?:/|$)');const r=new RegExp(o,s.sensitive?'':'i');function l(h){const u=h.match(r),f={};if(!u)return null;for(let y=1;y<u.length;y++){const _=u[y]||'',D=i[y-1];f[D.name]=_&&D.repeatable?_.split('/'):_;}return f;}function c(h){let u='',f=!1;for(const y of t){(!f||!u.endsWith('/'))&&(u+='/'),f=!1;for(const _ of y)if(_.type===0)u+=_.value;else if(_.type===1){const{value:D,repeatable:O,optional:q}=_,$=D in h?h[D]:'';if(Ve($)&&!O)throw new Error(`Provided param "${D}" is an array but it is not repeatable (* or + modifiers)`);const I=Ve($)?$.join('/'):$;if(!I)if(q)y.length<2&&(u.endsWith('/')?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${D}"`);u+=I;}}return u||'/';}return{re:r,score:n,keys:i,parse:l,stringify:c};}function yf(t,e){let s=0;for(;s<t.length&&s<e.length;){const n=e[s]-t[s];if(n)return n;s++;}return t.length<e.length?t.length===1&&t[0]===80?-1:1:t.length>e.length?e.length===1&&e[0]===80?1:-1:0;}function Tl(t,e){let s=0;const n=t.score,o=e.score;for(;s<n.length&&s<o.length;){const i=yf(n[s],o[s]);if(i)return i;s++;}if(Math.abs(o.length-n.length)===1){if(Ai(n))return 1;if(Ai(o))return-1;}return o.length-n.length;}function Ai(t){const e=t[t.length-1];return t.length>0&&e[e.length-1]<0;}const bf={type:0,value:''},_f=/[a-zA-Z0-9_]/;function vf(t){if(!t)return[[]];if(t==='/')return[[bf]];if(!t.startsWith('/'))throw new Error(`Invalid path "${t}"`);function e(_){throw new Error(`ERR (${s})/"${h}": ${_}`);}let s=0,n=s;const o=[];let i;function r(){i&&o.push(i),i=[];}let l=0,c,h='',u='';function f(){h&&(s===0?i.push({type:0,value:h}):s===1||s===2||s===3?(i.length>1&&(c==='*'||c==='+')&&e(`A repeatable param (${h}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:h,regexp:u,repeatable:c==='*'||c==='+',optional:c==='*'||c==='?'})):e('Invalid state to consume buffer'),h='');}function y(){h+=c;}for(;l<t.length;){if(c=t[l++],c==='\\'&&s!==2){n=s,s=4;continue;}switch(s){case 0:c==='/'?(h&&f(),r()):c===':'?(f(),s=1):y();break;case 4:y(),s=n;break;case 1:c==='('?s=2:_f.test(c)?y():(f(),s=0,c!=='*'&&c!=='?'&&c!=='+'&&l--);break;case 2:c===')'?u[u.length-1]=='\\'?u=u.slice(0,-1)+c:s=3:u+=c;break;case 3:f(),s=0,c!=='*'&&c!=='?'&&c!=='+'&&l--,u='';break;default:e('Unknown state');break;}}return s===2&&e(`Unfinished custom RegExp for param "${h}"`),f(),r(),o;}function kf(t,e,s){const n=mf(vf(t.path),s),o=X(n,{record:t,parent:e,children:[],alias:[]});return e&&!o.record.aliasOf==!e.record.aliasOf&&e.children.push(o),o;}function wf(t,e){const s=[],n=new Map;e=Oi({strict:!1,end:!0,sensitive:!1},e);function o(f){return n.get(f);}function i(f,y,_){const D=!_,O=Ri(f);O.aliasOf=_&&_.record;const q=Oi(e,f),$=[O];if('alias'in f){const P=typeof f.alias=='string'?[f.alias]:f.alias;for(const ne of P)$.push(Ri(X({},O,{components:_?_.record.components:O.components,path:ne,aliasOf:_?_.record:O})));}let I,j;for(const P of $){const{path:ne}=P;if(y&&ne[0]!=='/'){const ge=y.record.path,ue=ge[ge.length-1]==='/'?'':'/';P.path=y.record.path+(ne&&ue+ne);}if(I=kf(P,y,q),_?_.alias.push(I):(j=j||I,j!==I&&j.alias.push(I),D&&f.name&&!Di(I)&&r(f.name)),Sl(I)&&c(I),O.children){const ge=O.children;for(let ue=0;ue<ge.length;ue++)i(ge[ue],I,_&&_.children[ue]);}_=_||I;}return j?() => {r(j);}:ks;}function r(f){if(El(f)){const y=n.get(f);y&&(n.delete(f),s.splice(s.indexOf(y),1),y.children.forEach(r),y.alias.forEach(r));}else{const y=s.indexOf(f);y>-1&&(s.splice(y,1),f.record.name&&n.delete(f.record.name),f.children.forEach(r),f.alias.forEach(r));}}function l(){return s;}function c(f){const y=Tf(f,s);s.splice(y,0,f),f.record.name&&!Di(f)&&n.set(f.record.name,f);}function h(f,y){let _,D={},O,q;if('name'in f&&f.name){if(_=n.get(f.name),!_)throw os(1,{location:f});q=_.record.name,D=X(Bi(y.params,_.keys.filter(j => !j.optional).concat(_.parent?_.parent.keys.filter(j => j.optional):[]).map(j => j.name)),f.params&&Bi(f.params,_.keys.map(j => j.name))),O=_.stringify(D);}else if(f.path!=null)O=f.path,_=s.find(j => j.re.test(O)),_&&(D=_.parse(O),q=_.record.name);else{if(_=y.name?n.get(y.name):s.find(j => j.re.test(y.path)),!_)throw os(1,{location:f,currentLocation:y});q=_.record.name,D=X({},y.params,f.params),O=_.stringify(D);}const $=[];let I=_;for(;I;)$.unshift(I.record),I=I.parent;return{name:q,path:O,params:D,matched:$,meta:xf($)};}t.forEach(f => i(f));function u(){s.length=0,n.clear();}return{addRoute:i,resolve:h,removeRoute:r,clearRoutes:u,getRoutes:l,getRecordMatcher:o};}function Bi(t,e){const s={};for(const n of e)n in t&&(s[n]=t[n]);return s;}function Ri(t){const e={path:t.path,redirect:t.redirect,name:t.name,meta:t.meta||{},aliasOf:t.aliasOf,beforeEnter:t.beforeEnter,props:Ef(t),children:t.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:'components'in t?t.components||null:t.component&&{default:t.component}};return Object.defineProperty(e,'mods',{value:{}}),e;}function Ef(t){const e={},s=t.props||!1;if('component'in t)e.default=s;else for(const n in t.components)e[n]=typeof s=='object'?s[n]:s;return e;}function Di(t){for(;t;){if(t.record.aliasOf)return!0;t=t.parent;}return!1;}function xf(t){return t.reduce((e,s) => X(e,s.meta),{});}function Oi(t,e){const s={};for(const n in t)s[n]=n in e?e[n]:t[n];return s;}function Tf(t,e){let s=0,n=e.length;for(;s!==n;){const i=s+n>>1;Tl(t,e[i])<0?n=i:s=i+1;}const o=Sf(t);return o&&(n=e.lastIndexOf(o,n-1)),n;}function Sf(t){let e=t;for(;e=e.parent;)if(Sl(e)&&Tl(t,e)===0)return e;}function Sl({record:t}){return!!(t.name||t.components&&Object.keys(t.components).length||t.redirect);}function Lf(t){const e={};if(t===''||t==='?')return e;const n=(t[0]==='?'?t.slice(1):t).split('&');for(let o=0;o<n.length;++o){const i=n[o].replace(yl,' '),r=i.indexOf('='),l=As(r<0?i:i.slice(0,r)),c=r<0?null:As(i.slice(r+1));if(l in e){let h=e[l];Ve(h)||(h=e[l]=[h]),h.push(c);}else e[l]=c;}return e;}function Pi(t){let e='';for(let s in t){const n=t[s];if(s=Kd(s),n==null){n!==void 0&&(e+=(e.length?'&':'')+s);continue;}(Ve(n)?n.map(i => i&&no(i)):[n&&no(n)]).forEach(i => {i!==void 0&&(e+=(e.length?'&':'')+s,i!=null&&(e+='='+i));});}return e;}function Cf(t){const e={};for(const s in t){const n=t[s];n!==void 0&&(e[s]=Ve(n)?n.map(o => o==null?null:''+o):n==null?n:''+n);}return e;}const Af=Symbol(''),Ni=Symbol(''),No=Symbol(''),Ll=Symbol(''),io=Symbol('');function fs(){let t=[];function e(n){return t.push(n),() => {const o=t.indexOf(n);o>-1&&t.splice(o,1);};}function s(){t=[];}return{add:e,list:() => t.slice(),reset:s};}function wt(t,e,s,n,o,i=r => r()){const r=n&&(n.enterCallbacks[o]=n.enterCallbacks[o]||[]);return() => new Promise((l,c) => {const h=y => {y===!1?c(os(4,{from:s,to:e})):y instanceof Error?c(y):hf(y)?c(os(2,{from:e,to:y})):(r&&n.enterCallbacks[o]===r&&typeof y=='function'&&r.push(y),l());},u=i(() => t.call(n&&n.instances[o],e,s,h));let f=Promise.resolve(u);t.length<3&&(f=f.then(h)),f.catch(y => c(y));});}function Un(t,e,s,n,o=i => i()){const i=[];for(const r of t)for(const l in r.components){const c=r.components[l];if(!(e!=='beforeRouteEnter'&&!r.instances[l]))if(gl(c)){const u=(c.__vccOpts||c)[e];u&&i.push(wt(u,s,n,r,l,o));}else{const h=c();i.push(() => h.then(u => {if(!u)throw new Error(`Couldn't resolve component "${l}" at "${r.path}"`);const f=Nd(u)?u.default:u;r.mods[l]=u,r.components[l]=f;const _=(f.__vccOpts||f)[e];return _&&wt(_,s,n,r,l,o)();}));}}return i;}function Ii(t){const e=dt(No),s=dt(Ll),n=Ne(() => {const c=Yt(t.to);return e.resolve(c);}),o=Ne(() => {const{matched:c}=n.value,{length:h}=c,u=c[h-1],f=s.matched;if(!u||!f.length)return-1;const y=f.findIndex(ns.bind(null,u));if(y>-1)return y;const _=Mi(c[h-2]);return h>1&&Mi(u)===_&&f[f.length-1].path!==_?f.findIndex(ns.bind(null,c[h-2])):y;}),i=Ne(() => o.value>-1&&Pf(s.params,n.value.params)),r=Ne(() => o.value>-1&&o.value===s.matched.length-1&&kl(s.params,n.value.params));function l(c={}){if(Of(c)){const h=e[Yt(t.replace)?'replace':'push'](Yt(t.to)).catch(ks);return t.viewTransition&&typeof document<'u'&&'startViewTransition'in document&&document.startViewTransition(() => h),h;}return Promise.resolve();}return{route:n,href:Ne(() => n.value.href),isActive:i,isExactActive:r,navigate:l};}function Bf(t){return t.length===1?t[0]:t;}const Rf=yr({name:'RouterLink',compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:'page'},viewTransition:Boolean},useLink:Ii,setup(t,{slots:e}){const s=Os(Ii(t)),{options:n}=dt(No),o=Ne(() => ({[Ui(t.activeClass,n.linkActiveClass,'router-link-active')]:s.isActive,[Ui(t.exactActiveClass,n.linkExactActiveClass,'router-link-exact-active')]:s.isExactActive}));return() => {const i=e.default&&Bf(e.default(s));return t.custom?i:Gr('a',{'aria-current':s.isExactActive?t.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:o.value},i);};}}),Df=Rf;function Of(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)&&!t.defaultPrevented&&!(t.button!==void 0&&t.button!==0)){if(t.currentTarget&&t.currentTarget.getAttribute){const e=t.currentTarget.getAttribute('target');if(/\b_blank\b/i.test(e))return;}return t.preventDefault&&t.preventDefault(),!0;}}function Pf(t,e){for(const s in e){const n=e[s],o=t[s];if(typeof n=='string'){if(n!==o)return!1;}else if(!Ve(o)||o.length!==n.length||n.some((i,r) => i!==o[r]))return!1;}return!0;}function Mi(t){return t?t.aliasOf?t.aliasOf.path:t.path:'';}const Ui=(t,e,s) => t??e??s,Nf=yr({name:'RouterView',inheritAttrs:!1,props:{name:{type:String,default:'default'},route:Object},compatConfig:{MODE:3},setup(t,{attrs:e,slots:s}){const n=dt(io),o=Ne(() => t.route||n.value),i=dt(Ni,0),r=Ne(() => {let h=Yt(i);const{matched:u}=o.value;let f;for(;(f=u[h])&&!f.components;)h++;return h;}),l=Ne(() => o.value.matched[r.value]);Gs(Ni,Ne(() => r.value+1)),Gs(Af,l),Gs(io,o);const c=ca();return Xt(() => [c.value,l.value,t.name],([h,u,f],[y,_,D]) => {u&&(u.instances[f]=h,_&&_!==u&&h&&h===y&&(u.leaveGuards.size||(u.leaveGuards=_.leaveGuards),u.updateGuards.size||(u.updateGuards=_.updateGuards))),h&&u&&(!_||!ns(u,_)||!y)&&(u.enterCallbacks[f]||[]).forEach(O => O(h));},{flush:'post'}),() => {const h=o.value,u=t.name,f=l.value,y=f&&f.components[u];if(!y)return $i(s.default,{Component:y,route:h});const _=f.props[u],D=_?_===!0?h.params:typeof _=='function'?_(h):_:null,q=Gr(y,X({},D,e,{onVnodeUnmounted:$ => {$.component.isUnmounted&&(f.instances[u]=null);},ref:c}));return $i(s.default,{Component:q,route:h})||q;};}});function $i(t,e){if(!t)return null;const s=t(e);return s.length===1?s[0]:s;}const If=Nf;function Mf(t){const e=wf(t.routes,t),s=t.parseQuery||Lf,n=t.stringifyQuery||Pi,o=t.history,i=fs(),r=fs(),l=fs(),c=ua(_t);let h=_t;Ht&&t.scrollBehavior&&'scrollRestoration'in history&&(history.scrollRestoration='manual');const u=In.bind(null,k => ''+k),f=In.bind(null,zd),y=In.bind(null,As);function _(k,R){let A,N;return El(k)?(A=e.getRecordMatcher(k),N=R):N=k,e.addRoute(N,A);}function D(k){const R=e.getRecordMatcher(k);R&&e.removeRoute(R);}function O(){return e.getRoutes().map(k => k.record);}function q(k){return!!e.getRecordMatcher(k);}function $(k,R){if(R=X({},R||c.value),typeof k=='string'){const b=Mn(s,k,R.path),v=e.resolve({path:b.path},R),E=o.createHref(b.fullPath);return X(b,v,{params:y(v.params),hash:As(b.hash),redirectedFrom:void 0,href:E});}let A;if(k.path!=null)A=X({},k,{path:Mn(s,k.path,R.path).path});else{const b=X({},k.params);for(const v in b)b[v]==null&&delete b[v];A=X({},k,{params:f(b)}),R.params=f(R.params);}const N=e.resolve(A,R),oe=k.hash||'';N.params=u(y(N.params));const d=Qd(n,X({},k,{hash:Hd(oe),path:N.path})),p=o.createHref(d);return X({fullPath:d,hash:oe,query:n===Pi?Cf(k.query):k.query||{}},N,{redirectedFrom:void 0,href:p});}function I(k){return typeof k=='string'?Mn(s,k,c.value.path):X({},k);}function j(k,R){if(h!==k)return os(8,{from:R,to:k});}function P(k){return ue(k);}function ne(k){return P(X(I(k),{replace:!0}));}function ge(k){const R=k.matched[k.matched.length-1];if(R&&R.redirect){const{redirect:A}=R;let N=typeof A=='function'?A(k):A;return typeof N=='string'&&(N=N.includes('?')||N.includes('#')?N=I(N):{path:N},N.params={}),X({query:k.query,hash:k.hash,params:N.path!=null?{}:k.params},N);}}function ue(k,R){const A=h=$(k),N=c.value,oe=k.state,d=k.force,p=k.replace===!0,b=ge(A);if(b)return ue(X(I(b),{state:typeof b=='object'?X({},oe,b.state):oe,force:d,replace:p}),R||A);const v=A;v.redirectedFrom=R;let E;return!d&&Xd(n,N,A)&&(E=os(16,{to:v,from:N}),We(N,N,!0,!1)),(E?Promise.resolve(E):He(v,N)).catch(w => lt(w)?lt(w,2)?w:bt(w):Q(w,v,N)).then(w => {if(w){if(lt(w,2))return ue(X({replace:p},I(w.to),{state:typeof w.to=='object'?X({},oe,w.to.state):oe,force:d}),R||v);}else w=Ct(v,N,!0,p,oe);return yt(v,N,w),w;});}function qe(k,R){const A=j(k,R);return A?Promise.reject(A):Promise.resolve();}function mt(k){const R=jt.values().next().value;return R&&typeof R.runWithContext=='function'?R.runWithContext(k):k();}function He(k,R){let A;const[N,oe,d]=Uf(k,R);A=Un(N.reverse(),'beforeRouteLeave',k,R);for(const b of N)b.leaveGuards.forEach(v => {A.push(wt(v,k,R));});const p=qe.bind(null,k,R);return A.push(p),Oe(A).then(() => {A=[];for(const b of i.list())A.push(wt(b,k,R));return A.push(p),Oe(A);}).then(() => {A=Un(oe,'beforeRouteUpdate',k,R);for(const b of oe)b.updateGuards.forEach(v => {A.push(wt(v,k,R));});return A.push(p),Oe(A);}).then(() => {A=[];for(const b of d)if(b.beforeEnter)if(Ve(b.beforeEnter))for(const v of b.beforeEnter)A.push(wt(v,k,R));else A.push(wt(b.beforeEnter,k,R));return A.push(p),Oe(A);}).then(() => (k.matched.forEach(b => b.enterCallbacks={}),A=Un(d,'beforeRouteEnter',k,R,mt),A.push(p),Oe(A))).then(() => {A=[];for(const b of r.list())A.push(wt(b,k,R));return A.push(p),Oe(A);}).catch(b => lt(b,8)?b:Promise.reject(b));}function yt(k,R,A){l.list().forEach(N => mt(() => N(k,R,A)));}function Ct(k,R,A,N,oe){const d=j(k,R);if(d)return d;const p=R===_t,b=Ht?history.state:{};A&&(N||p?o.replace(k.fullPath,X({scroll:p&&b&&b.scroll},oe)):o.push(k.fullPath,oe)),c.value=k,We(k,R,A,p),bt();}let Ke;function ls(){Ke||(Ke=o.listen((k,R,A) => {if(!Is.listening)return;const N=$(k),oe=ge(N);if(oe){ue(X(oe,{replace:!0,force:!0}),N).catch(ks);return;}h=N;const d=c.value;Ht&&lf(Ti(d.fullPath,A.delta),kn()),He(N,d).catch(p => lt(p,12)?p:lt(p,2)?(ue(X(I(p.to),{force:!0}),N).then(b => {lt(b,20)&&!A.delta&&A.type===Bs.pop&&o.go(-1,!1);}).catch(ks),Promise.reject()):(A.delta&&o.go(-A.delta,!1),Q(p,N,d))).then(p => {p=p||Ct(N,d,!1),p&&(A.delta&&!lt(p,8)?o.go(-A.delta,!1):A.type===Bs.pop&&lt(p,20)&&o.go(-1,!1)),yt(N,d,p);}).catch(ks);}));}let Ut=fs(),pe=fs(),se;function Q(k,R,A){bt(k);const N=pe.list();return N.length?N.forEach(oe => oe(k,R,A)):console.error(k),Promise.reject(k);}function it(){return se&&c.value!==_t?Promise.resolve():new Promise((k,R) => {Ut.add([k,R]);});}function bt(k){return se||(se=!k,ls(),Ut.list().forEach(([R,A]) => k?A(k):R()),Ut.reset()),k;}function We(k,R,A,N){const{scrollBehavior:oe}=t;if(!Ht||!oe)return Promise.resolve();const d=!A&&af(Ti(k.fullPath,0))||(N||!A)&&history.state&&history.state.scroll||null;return vo().then(() => oe(k,R,d)).then(p => p&&rf(p)).catch(p => Q(p,k,R));}const xe=k => o.go(k);let $t;const jt=new Set,Is={currentRoute:c,listening:!0,addRoute:_,removeRoute:D,clearRoutes:e.clearRoutes,hasRoute:q,getRoutes:O,resolve:$,options:t,push:P,replace:ne,go:xe,back:() => xe(-1),forward:() => xe(1),beforeEach:i.add,beforeResolve:r.add,afterEach:l.add,onError:pe.add,isReady:it,install(k){const R=this;k.component('RouterLink',Df),k.component('RouterView',If),k.config.globalProperties.$router=R,Object.defineProperty(k.config.globalProperties,'$route',{enumerable:!0,get:() => Yt(c)}),Ht&&!$t&&c.value===_t&&($t=!0,P(o.location).catch(oe => {}));const A={};for(const oe in _t)Object.defineProperty(A,oe,{get:() => c.value[oe],enumerable:!0});k.provide(No,R),k.provide(Ll,ar(A)),k.provide(io,c);const N=k.unmount;jt.add(k),k.unmount=function(){jt.delete(k),jt.size<1&&(h=_t,Ke&&Ke(),Ke=null,c.value=_t,$t=!1,se=!1),N();};}};function Oe(k){return k.reduce((R,A) => R.then(() => mt(A)),Promise.resolve());}return Is;}function Uf(t,e){const s=[],n=[],o=[],i=Math.max(e.matched.length,t.matched.length);for(let r=0;r<i;r++){const l=e.matched[r];l&&(t.matched.find(h => ns(h,l))?n.push(l):s.push(l));const c=t.matched[r];c&&(e.matched.find(h => ns(h,c))||o.push(c));}return[s,n,o];}const ro=md({state:{authenticated:!1,user:null,selectedTimeslot:null,adminAuthenticated:!1,adminUsername:null,bookingLists:[],groups:[]},getters:{isAuthenticated(t){return t.authenticated;},getUser(t){return t.user;},getSelectedTimeslot(t){return t.selectedTimeslot;},isAdminAuthenticated(t){return t.adminAuthenticated;},getAdminUsername(t){return t.adminUsername;},getBookingLists(t){return t.bookingLists;},getGroups(t){return t.groups;}},mutations:{setAuthenticated(t,e){t.authenticated=e;},setUser(t,e){t.user=e;},setSelectedTimeslot(t,e){t.selectedTimeslot=e;},setAdminAuthenticated(t,e){t.adminAuthenticated=e;},setAdminUsername(t,e){t.adminUsername=e;},setBookingLists(t,e){t.bookingLists=e;},setGroups(t,e){t.groups=e;},addGroup(t,e){t.groups.push(e);},updateGroup(t,e){const s=t.groups.findIndex(n => n.id===e.id);s!==-1&&t.groups.splice(s,1,e);},removeGroup(t,e){t.groups=t.groups.filter(s => s.id!==e);}},actions:{async fetchBookingLists({commit:t}){try{const s=await(await fetch('/api/booking-lists')).json();return t('setBookingLists',s.bookingLists),s.bookingLists;}catch(e){throw console.error('Error fetching booking lists:',e),e;}},async fetchGroups({commit:t}){try{const s=await(await fetch('/api/groups')).json();return t('setGroups',s.groups),s.groups;}catch(e){throw console.error('Error fetching groups:',e),e;}},async createGroup({commit:t},e){try{const n=await(await fetch('/api/groups',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({name:e})})).json();return t('addGroup',n),n;}catch(s){throw console.error('Error creating group:',s),s;}},async addGroupMember({commit:t},{groupId:e,username:s}){try{const o=await(await fetch(`/api/groups/${e}/members`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({username:s})})).json();return t('updateGroup',o),o;}catch(n){throw console.error('Error adding group member:',n),n;}},async removeGroupMember({commit:t},{groupId:e,memberId:s}){try{const o=await(await fetch(`/api/groups/${e}/members/${s}`,{method:'DELETE'})).json();return o.message&&o.message.includes('empty group deleted')?t('removeGroup',e):t('updateGroup',o),o;}catch(n){throw console.error('Error removing group member:',n),n;}}}}),$f={name:'LoginView',data(){return{username:'',password:'',loading:!1,error:null};},methods:{async login(){if(!this.username||!this.password){this.error='Please enter both username and password';return;}this.loading=!0,this.error=null;try{console.log('Attempting to login with username:',this.username);const t=await fetch('/api/login',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify({username:this.username,password:this.password})});console.log('Login response status:',t.status);const e=await t.text();if(console.log('Login response text:',e),!e)throw new Error('Empty response from server');const s=JSON.parse(e);if(!t.ok)throw new Error(s.error||'Login failed');console.log('Login successful:',s),this.$store.commit('setAuthenticated',!0),this.$store.commit('setUser',s.user),this.$router.push('/booking-lists');}catch(t){console.error('Login error:',t),this.error=t.message||'Login failed. Please try again.';}finally{this.loading=!1;}}}},jf={class:'row justify-content-center'},Ff={class:'col-md-6'},Vf={class:'card'},Gf={class:'card-body'},qf={class:'mb-3'},Hf={class:'mb-3'},Kf={key:0,class:'alert alert-danger mt-3'},Wf={class:'d-flex justify-content-between align-items-center'},zf=['disabled'],Yf={key:0,class:'spinner-border spinner-border-sm me-2',role:'status','aria-hidden':'true'};function Jf(t,e,s,n,o,i){const r=Nt('router-link');return g(),m('div',jf,[a('div',Ff,[a('div',Vf,[e[7]||(e[7]=a('div',{class:'card-header bg-primary text-white'},[a('h3',{class:'mb-0'},'Login')],-1)),a('div',Gf,[a('form',{onSubmit:e[2]||(e[2]=Fe((...l) => i.login&&i.login(...l),['prevent']))},[a('div',qf,[e[3]||(e[3]=a('label',{for:'username',class:'form-label'},'Username',-1)),H(a('input',{id:'username','onUpdate:modelValue':e[0]||(e[0]=l => o.username=l),type:'text',class:'form-control',placeholder:'Enter your username',required:''},null,512),[[J,o.username]])]),a('div',Hf,[e[4]||(e[4]=a('label',{for:'password',class:'form-label'},'Password',-1)),H(a('input',{id:'password','onUpdate:modelValue':e[1]||(e[1]=l => o.password=l),type:'password',class:'form-control',placeholder:'Enter your password',required:''},null,512),[[J,o.password]])]),o.error?(g(),m('div',Kf,T(o.error),1)):K('',!0),a('div',Wf,[a('button',{type:'submit',class:'btn btn-primary',disabled:o.loading},[o.loading?(g(),m('span',Yf)):K('',!0),e[5]||(e[5]=z(' Login '))],8,zf),de(r,{to:'/register',class:'text-decoration-none'},{default:Pt(() => e[6]||(e[6]=[z('Don\'t have an account? Register')])),_:1,__:[6]})])],32)])])])]);}const Qf=Ue($f,[['render',Jf]]),Xf={name:'RegisterView',data(){return{username:'',password:'',confirmPassword:'',email:'',loading:!1,error:null};},methods:{async register(){if(this.username.length<3){this.error='Username must be at least 3 characters long';return;}if(this.password.length<6){this.error='Password must be at least 6 characters long';return;}if(this.password!==this.confirmPassword){this.error='Passwords do not match';return;}this.loading=!0,this.error=null;try{const t=await fetch('/api/register',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({username:this.username,password:this.password,email:this.email||null})}),e=await t.json();if(!t.ok)throw new Error(e.error||'Registration failed');this.$store.commit('setAuthenticated',!0),this.$store.commit('setUser',e.user),this.$router.push('/booking-lists');}catch(t){console.error('Registration error:',t),this.error=t.message||'Registration failed. Please try again.';}finally{this.loading=!1;}}}},Zf={class:'row justify-content-center'},eh={class:'col-md-6'},th={class:'card'},sh={class:'card-body'},nh={class:'mb-3'},oh={class:'mb-3'},ih={class:'mb-3'},rh={class:'mb-3'},lh={key:0,class:'alert alert-danger mt-3'},ah={class:'d-flex justify-content-between align-items-center'},ch=['disabled'],uh={key:0,class:'spinner-border spinner-border-sm me-2',role:'status','aria-hidden':'true'};function dh(t,e,s,n,o,i){const r=Nt('router-link');return g(),m('div',Zf,[a('div',eh,[a('div',th,[e[13]||(e[13]=a('div',{class:'card-header bg-primary text-white'},[a('h3',{class:'mb-0'},'Register')],-1)),a('div',sh,[a('form',{onSubmit:e[4]||(e[4]=Fe((...l) => i.register&&i.register(...l),['prevent']))},[a('div',nh,[e[5]||(e[5]=a('label',{for:'username',class:'form-label'},'Username',-1)),H(a('input',{id:'username','onUpdate:modelValue':e[0]||(e[0]=l => o.username=l),type:'text',class:'form-control',placeholder:'Choose a username',required:''},null,512),[[J,o.username]]),e[6]||(e[6]=a('small',{class:'form-text text-muted'},'Username must be at least 3 characters long.',-1))]),a('div',oh,[e[7]||(e[7]=a('label',{for:'password',class:'form-label'},'Password',-1)),H(a('input',{id:'password','onUpdate:modelValue':e[1]||(e[1]=l => o.password=l),type:'password',class:'form-control',placeholder:'Choose a password',required:''},null,512),[[J,o.password]]),e[8]||(e[8]=a('small',{class:'form-text text-muted'},'Password must be at least 6 characters long.',-1))]),a('div',ih,[e[9]||(e[9]=a('label',{for:'confirmPassword',class:'form-label'},'Confirm Password',-1)),H(a('input',{id:'confirmPassword','onUpdate:modelValue':e[2]||(e[2]=l => o.confirmPassword=l),type:'password',class:'form-control',placeholder:'Confirm your password',required:''},null,512),[[J,o.confirmPassword]])]),a('div',rh,[e[10]||(e[10]=a('label',{for:'email',class:'form-label'},'Email (optional)',-1)),H(a('input',{id:'email','onUpdate:modelValue':e[3]||(e[3]=l => o.email=l),type:'email',class:'form-control',placeholder:'Enter your email'},null,512),[[J,o.email]])]),o.error?(g(),m('div',lh,T(o.error),1)):K('',!0),a('div',ah,[a('button',{type:'submit',class:'btn btn-primary',disabled:o.loading},[o.loading?(g(),m('span',uh)):K('',!0),e[11]||(e[11]=z(' Register '))],8,ch),de(r,{to:'/login',class:'text-decoration-none'},{default:Pt(() => e[12]||(e[12]=[z('Already have an account? Login')])),_:1,__:[12]})])],32)])])])]);}const fh=Ue(Xf,[['render',dh]]),hh={name:'BookingListsView',data(){return{loading:!0,error:null,examinationTypes:[],locations:[]};},computed:{...Mt(['getBookingLists']),bookingLists(){return this.getBookingLists;}},async created(){try{await this.$store.dispatch('fetchBookingLists');const e=await(await fetch('/api/examination-types')).json();this.examinationTypes=e.examinationTypes;const n=await(await fetch('/api/locations')).json();this.locations=n.locations,this.loading=!1;}catch(t){console.error('Error loading data:',t),this.error='Failed to load booking lists. Please try again later.',this.loading=!1;}},methods:{viewBookingList(t){this.$router.push(`/booking-lists/${t}`);},getExaminationTypeName(t){if(!t)return null;const e=this.examinationTypes.find(s => s.id===t);return e?e.name:null;},getLocationName(t){if(!t)return null;const e=this.locations.find(s => s.id===t);return e?e.name:null;},formatDate(t){return t?new Date(t).toLocaleDateString():'';}}},ph={key:0,class:'text-center my-5'},gh={key:1,class:'alert alert-danger'},mh={key:2,class:'alert alert-info'},yh={key:3,class:'row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4'},bh={class:'card h-100'},_h={class:'card-header'},vh={class:'card-title mb-0'},kh={class:'card-body'},wh={key:0,class:'card-text'},Eh={key:1,class:'card-text'},xh={class:'mb-3'},Th={key:0},Sh={key:1},Lh={class:'mb-3'},Ch={key:0},Ah={key:1},Bh={class:'mb-3'},Rh={key:0},Dh={key:1},Oh={key:2},Ph={key:3},Nh={class:'mb-3'},Ih={class:'card-footer'},Mh=['onClick','disabled'];function Uh(t,e,s,n,o,i){return g(),m('div',null,[e[5]||(e[5]=a('h1',{class:'mb-4'},'Available Booking Lists',-1)),o.loading?(g(),m('div',ph,e[0]||(e[0]=[a('div',{class:'spinner-border text-primary',role:'status'},[a('span',{class:'visually-hidden'},'Loading...')],-1),a('p',{class:'mt-2'},'Loading booking lists...',-1)]))):o.error?(g(),m('div',gh,T(o.error),1)):i.bookingLists.length===0?(g(),m('div',mh,' No booking lists are currently available. ')):(g(),m('div',yh,[(g(!0),m(ae,null,_e(i.bookingLists,r => (g(),m('div',{key:r.id,class:'col'},[a('div',bh,[a('div',_h,[a('h5',vh,T(r.title),1)]),a('div',kh,[r.description?(g(),m('p',wh,T(r.description),1)):(g(),m('p',Eh,'No description available.')),a('div',xh,[e[1]||(e[1]=a('strong',null,'Examination Type:',-1)),i.getExaminationTypeName(r.examinationTypeId)?(g(),m('span',Th,T(i.getExaminationTypeName(r.examinationTypeId)),1)):(g(),m('span',Sh,'Not specified'))]),a('div',Lh,[e[2]||(e[2]=a('strong',null,'Default Location:',-1)),i.getLocationName(r.locationId)?(g(),m('span',Ch,T(i.getLocationName(r.locationId)),1)):(g(),m('span',Ah,'Not specified'))]),a('div',Bh,[e[3]||(e[3]=a('strong',null,'Booking Period:',-1)),r.bookingStartDate&&r.bookingEndDate?(g(),m('span',Rh,T(i.formatDate(r.bookingStartDate))+' to '+T(i.formatDate(r.bookingEndDate)),1)):r.bookingStartDate?(g(),m('span',Dh,' From '+T(i.formatDate(r.bookingStartDate)),1)):r.bookingEndDate?(g(),m('span',Oh,' Until '+T(i.formatDate(r.bookingEndDate)),1)):(g(),m('span',Ph,'No restrictions'))]),a('div',Nh,[e[4]||(e[4]=a('strong',null,'Max Bookings:',-1)),z(' '+T(r.maxBookingsPerStudent)+' per student/group ',1)])]),a('div',Ih,[a('button',{class:'btn btn-primary w-100',onClick:l => i.viewBookingList(r.id),disabled:!r.bookingAllowed},T(r.bookingAllowed?'View Available Times':'Booking Not Currently Available'),9,Mh)])])]))),128))]))]);}const $h=Ue(hh,[['render',Uh]]),jh={name:'BookingListDetailView',inject:['socket'],data(){return{loading:!0,error:null,bookingList:null,timeslots:[],locations:[],reservingTimeslot:!1,reservingTimeslotId:null,cancellingBooking:!1,cancellingBookingId:null};},computed:{...Mt(['getUser']),user(){return this.getUser;}},created(){this.fetchData(),this.socket.on('timeslot_reserved',this.handleTimeslotReserved),this.socket.on('timeslot_reservation_cancelled',this.handleReservationCancelled),this.socket.on('timeslot_booked',this.handleTimeslotBooked),this.socket.on('timeslot_cancelled',this.handleTimeslotCancelled);},beforeUnmount(){this.socket.off('timeslot_reserved',this.handleTimeslotReserved),this.socket.off('timeslot_reservation_cancelled',this.handleReservationCancelled),this.socket.off('timeslot_booked',this.handleTimeslotBooked),this.socket.off('timeslot_cancelled',this.handleTimeslotCancelled);},methods:{async fetchData(){try{this.loading=!0;const t=this.$route.params.id,s=await(await fetch('/api/booking-lists')).json();if(this.bookingList=s.bookingLists.find(l => l.id===Number(t)),!this.bookingList){this.error='Booking list not found',this.loading=!1;return;}const o=await(await fetch(`/api/booking-lists/${t}/timeslots`)).json();this.timeslots=o.timeslots;const r=await(await fetch('/api/locations')).json();this.locations=r.locations,this.loading=!1;}catch(t){console.error('Error fetching data:',t),this.error='Failed to load data. Please try again later.',this.loading=!1;}},async reserveTimeslot(t){try{this.reservingTimeslot=!0,this.reservingTimeslotId=t;const e=await fetch(`/api/timeslots/${t}/reserve`,{method:'POST',headers:{'Content-Type':'application/json'}});if(e.ok){const s=await e.json();this.$store.commit('setSelectedTimeslot',s),this.$router.push(`/confirm-booking/${t}`);}else{const s=await e.json();alert(s.error||'Failed to reserve timeslot');}}catch(e){console.error('Error reserving timeslot:',e),alert('Failed to reserve timeslot. Please try again.');}finally{this.reservingTimeslot=!1,this.reservingTimeslotId=null;}},getLocationName(t){if(!t)return'Not specified';const e=this.locations.find(s => s.id===t);return e?e.name:'Unknown';},formatDate(t){return t?new Date(t).toLocaleDateString():'';},handleTimeslotReserved(t){const e=this.timeslots.findIndex(s => s.id===t.id);e!==-1&&this.timeslots.splice(e,1,t);},handleReservationCancelled(t){const e=this.timeslots.findIndex(s => s.id===t.id);e!==-1&&this.timeslots.splice(e,1,t);},handleTimeslotBooked(t){const e=this.timeslots.findIndex(s => s.id===t.id);e!==-1&&this.timeslots.splice(e,1,t);},handleTimeslotCancelled(t){console.log('Received timeslot_cancelled event:',t);const e=this.timeslots.findIndex(s => s.id===t.id);if(e!==-1){const s={...this.timeslots[e],...t,cancelled:!0,booked:!0};this.timeslots.splice(e,1,s),console.log('Updated timeslot via socket event:',s),this.$forceUpdate(),t.cancelledById!==this.user.id&&t.bookedByGroupId&&this.user.groups&&this.user.groups.some(n => Number(n.id)===Number(t.bookedByGroupId))&&alert('A member of your group has cancelled a booking.');}else console.log('Timeslot not found in local array, fetching fresh data'),this.fetchData();},isBookedByUserOrGroup(t){if(t.cancelled)return!1;if(t.bookedByStudentId===this.user.id)return!0;if(t.bookedByGroupId){if(this.$store.getters.getGroups&&this.$store.getters.getGroups.length>0)return this.$store.getters.getGroups.some(e => Number(e.id)===Number(t.bookedByGroupId));if(this.user.groups&&this.user.groups.length>0)return this.user.groups.some(e => Number(e.id)===Number(t.bookedByGroupId));}return!1;},canCancelBooking(t){const e=this.bookingList;if(!e||!e.cancellationAllowed||t.cancelled)return!1;if(t.bookedByStudentId===this.user.id)return!0;if(t.bookedByGroupId){if(this.$store.getters.getGroups&&this.$store.getters.getGroups.length>0)return this.$store.getters.getGroups.some(s => Number(s.id)===Number(t.bookedByGroupId));if(this.user.groups&&this.user.groups.length>0)return this.user.groups.some(s => Number(s.id)===Number(t.bookedByGroupId));}return!1;},async cancelBooking(t){if(confirm('Are you sure you want to cancel this booking?'))try{this.cancellingBooking=!0,this.cancellingBookingId=t;const e=await fetch(`/api/timeslots/${t}/cancel`,{method:'POST',headers:{'Content-Type':'application/json'}});if(e.ok)alert('Booking cancelled successfully');else{const s=await e.json();throw new Error(s.error||'Failed to cancel booking');}}catch(e){console.error('Error cancelling booking:',e),alert(e.message||'Failed to cancel booking. Please try again.');}finally{this.cancellingBooking=!1,this.cancellingBookingId=null;}}}},Fh={key:0,class:'text-center my-5'},Vh={key:1,class:'alert alert-danger'},Gh={key:2},qh={class:'card mb-4'},Hh={class:'card-header bg-primary text-white'},Kh={class:'mb-0'},Wh={class:'card-body'},zh={key:0},Yh={key:1,class:'alert alert-info'},Jh={key:2,class:'alert alert-info'},Qh={key:3,class:'table-responsive'},Xh={class:'table table-striped table-hover'},Zh={key:0,class:'badge bg-info'},ep={key:1,class:'badge bg-danger'},tp={key:2,class:'badge bg-warning'},sp={key:3,class:'badge bg-success'},np=['onClick','disabled'],op={key:0,class:'spinner-border spinner-border-sm me-1',role:'status','aria-hidden':'true'},ip=['onClick','disabled'],rp={key:0,class:'spinner-border spinner-border-sm me-1',role:'status','aria-hidden':'true'},lp={key:2},ap={key:3};function cp(t,e,s,n,o,i){return g(),m('div',null,[e[5]||(e[5]=a('h1',{class:'mb-4'},'Booking List Details',-1)),o.loading?(g(),m('div',Fh,e[0]||(e[0]=[a('div',{class:'spinner-border text-primary',role:'status'},[a('span',{class:'visually-hidden'},'Loading...')],-1),a('p',{class:'mt-2'},'Loading timeslots...',-1)]))):o.error?(g(),m('div',Vh,T(o.error),1)):(g(),m('div',Gh,[a('div',qh,[a('div',Hh,[a('h5',Kh,T(o.bookingList?o.bookingList.title:'Booking List'),1)]),a('div',Wh,[o.bookingList&&o.bookingList.description?(g(),m('p',zh,T(o.bookingList.description),1)):K('',!0),o.bookingList.bookingAllowed?K('',!0):(g(),m('div',Yh,' Booking is not currently allowed for this list. ')),e[4]||(e[4]=a('h5',{class:'mt-4'},'Available Timeslots',-1)),o.timeslots.length===0?(g(),m('div',Jh,' No timeslots available for this booking list. ')):(g(),m('div',Qh,[a('table',Xh,[e[3]||(e[3]=a('thead',null,[a('tr',null,[a('th',null,'Date'),a('th',null,'Time'),a('th',null,'Location'),a('th',null,'Status'),a('th',null,'Action')])],-1)),a('tbody',null,[(g(!0),m(ae,null,_e(o.timeslots,r => (g(),m('tr',{key:r.id,class:Ds({'table-secondary':r.booked&&!r.cancelled||r.reserved})},[a('td',null,T(i.formatDate(r.date)),1),a('td',null,T(r.time),1),a('td',null,T(i.getLocationName(r.locationId)),1),a('td',null,[i.isBookedByUserOrGroup(r)?(g(),m('span',Zh,'Your Booking')):r.booked&&!r.cancelled?(g(),m('span',ep,'Booked')):r.reserved?(g(),m('span',tp,'Reserved')):!r.booked||r.cancelled?(g(),m('span',sp,'Available')):K('',!0)]),a('td',null,[(!r.booked||r.cancelled)&&!r.reserved?(g(),m('button',{key:0,class:'btn btn-sm btn-primary',onClick:l => i.reserveTimeslot(r.id),disabled:o.loading||o.reservingTimeslot},[o.reservingTimeslot&&o.reservingTimeslotId===r.id?(g(),m('span',op)):K('',!0),e[1]||(e[1]=z(' Reserve '))],8,np)):i.isBookedByUserOrGroup(r)?(g(),m('button',{key:1,class:'btn btn-sm btn-danger',onClick:l => i.cancelBooking(r.id),disabled:o.loading||o.cancellingBooking||!i.canCancelBooking(r)},[o.cancellingBooking&&o.cancellingBookingId===r.id?(g(),m('span',rp)):K('',!0),e[2]||(e[2]=z(' Cancel '))],8,ip)):r.booked&&!r.cancelled?(g(),m('span',lp,'Booked')):r.reserved?(g(),m('span',ap,'Reserved')):K('',!0)])],2))),128))])])]))])])]))]);}const up=Ue(jh,[['render',cp]]),dp={name:'ConfirmBookingView',inject:['socket'],data(){return{loading:!0,error:null,timeslot:null,locationName:'',timeRemaining:10,countdownInterval:null,bookingType:'individual',selectedGroupId:'',confirming:!1};},computed:{...Mt(['getSelectedTimeslot','getUser','getGroups']),user(){return this.getUser;},groups(){return this.getGroups;}},async created(){try{const t=this.$route.params.id,e=this.getSelectedTimeslot;if(e&&e.id===Number(t))this.timeslot=e;else{const s=await fetch(`/api/timeslots/${t}`);if(!s.ok)throw new Error('Failed to fetch timeslot details');this.timeslot=await s.json();}if(this.timeslot.locationId){const o=(await(await fetch('/api/locations')).json()).locations.find(i => i.id===this.timeslot.locationId);this.locationName=o?o.name:'Unknown';}else this.locationName='Not specified';await this.$store.dispatch('fetchGroups'),this.startCountdown(),this.loading=!1;}catch(t){console.error('Error loading data:',t),this.error='Failed to load timeslot details. Please try again.',this.loading=!1;}},beforeUnmount(){this.countdownInterval&&clearInterval(this.countdownInterval);},methods:{startCountdown(){if(this.timeslot&&this.timeslot.reservedUntil){const t=Date.now(),e=this.timeslot.reservedUntil;this.timeRemaining=Math.max(0,Math.floor((e-t)/1e3));}this.countdownInterval=setInterval(() => {this.timeRemaining>0?this.timeRemaining--:(clearInterval(this.countdownInterval),alert('Reservation time expired'),this.$router.push(`/booking-lists/${this.timeslot.bookingListId}`));},1e3);},async confirmBooking(){try{this.confirming=!0;const t=this.timeslot.id,e={};this.bookingType==='group'&&this.selectedGroupId&&(e.groupId=this.selectedGroupId);const s=await fetch(`/api/timeslots/${t}/book`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(e)});if(s.ok)this.countdownInterval&&clearInterval(this.countdownInterval),alert('Booking confirmed successfully!'),this.$router.push('/my-bookings');else{const n=await s.json();throw new Error(n.error||'Failed to confirm booking');}}catch(t){console.error('Error confirming booking:',t),alert(t.message||'Failed to confirm booking. Please try again.');}finally{this.confirming=!1;}},async cancelReservation(){try{this.countdownInterval&&clearInterval(this.countdownInterval),this.$router.push(`/booking-lists/${this.timeslot.bookingListId}`);}catch(t){console.error('Error cancelling reservation:',t);}},formatDate(t){return t?new Date(t).toLocaleDateString():'';}}},fp={key:0,class:'text-center my-5'},hp={key:1,class:'alert alert-danger'},pp={key:2,class:'row'},gp={class:'col-md-8 mx-auto'},mp={class:'card'},yp={class:'card-body'},bp={class:'alert alert-warning'},_p={class:'mb-4'},vp={class:'mb-3'},kp={class:'form-check'},wp={key:0,class:'form-check'},Ep={key:0,class:'mb-3'},xp=['value'],Tp={key:1,class:'alert alert-info'},Sp={class:'d-flex justify-content-between mt-4'},Lp=['disabled'],Cp=['disabled'],Ap={key:0,class:'spinner-border spinner-border-sm me-2',role:'status','aria-hidden':'true'};function Bp(t,e,s,n,o,i){const r=Nt('router-link');return g(),m('div',null,[e[21]||(e[21]=a('h1',{class:'mb-4'},'Confirm Booking',-1)),o.loading?(g(),m('div',fp,e[5]||(e[5]=[a('div',{class:'spinner-border text-primary',role:'status'},[a('span',{class:'visually-hidden'},'Loading...')],-1),a('p',{class:'mt-2'},'Loading timeslot details...',-1)]))):o.error?(g(),m('div',hp,T(o.error),1)):(g(),m('div',pp,[a('div',gp,[a('div',mp,[e[20]||(e[20]=a('div',{class:'card-header bg-primary text-white'},[a('h5',{class:'mb-0'},'Confirm Your Booking')],-1)),a('div',yp,[a('div',bp,[e[6]||(e[6]=a('strong',null,'Time remaining:',-1)),z(' '+T(o.timeRemaining)+' seconds ',1)]),e[19]||(e[19]=a('h5',null,'Timeslot Details',-1)),a('div',_p,[a('p',null,[e[7]||(e[7]=a('strong',null,'Date:',-1)),z(' '+T(i.formatDate(o.timeslot.date)),1)]),a('p',null,[e[8]||(e[8]=a('strong',null,'Time:',-1)),z(' '+T(o.timeslot.time),1)]),a('p',null,[e[9]||(e[9]=a('strong',null,'Location:',-1)),z(' '+T(o.locationName),1)])]),a('form',{onSubmit:e[4]||(e[4]=Fe((...l) => i.confirmBooking&&i.confirmBooking(...l),['prevent']))},[a('div',vp,[e[12]||(e[12]=a('label',{class:'form-label'},'Book as:',-1)),a('div',kp,[H(a('input',{class:'form-check-input',type:'radio',id:'bookAsIndividual','onUpdate:modelValue':e[0]||(e[0]=l => o.bookingType=l),value:'individual'},null,512),[[hi,o.bookingType]]),e[10]||(e[10]=a('label',{class:'form-check-label',for:'bookAsIndividual'},' Individual ',-1))]),i.groups.length>0?(g(),m('div',wp,[H(a('input',{class:'form-check-input',type:'radio',id:'bookAsGroup','onUpdate:modelValue':e[1]||(e[1]=l => o.bookingType=l),value:'group'},null,512),[[hi,o.bookingType]]),e[11]||(e[11]=a('label',{class:'form-check-label',for:'bookAsGroup'},' Group ',-1))])):K('',!0)]),o.bookingType==='group'&&i.groups.length>0?(g(),m('div',Ep,[e[14]||(e[14]=a('label',{for:'groupSelect',class:'form-label'},'Select Group:',-1)),H(a('select',{id:'groupSelect',class:'form-select','onUpdate:modelValue':e[2]||(e[2]=l => o.selectedGroupId=l),required:''},[e[13]||(e[13]=a('option',{value:'',disabled:''},'Select a group',-1)),(g(!0),m(ae,null,_e(i.groups,l => (g(),m('option',{key:l.id,value:l.id},T(l.name)+' ('+T(l.members.length)+' members) ',9,xp))),128))],512),[[Zt,o.selectedGroupId]])])):K('',!0),o.bookingType==='group'&&i.groups.length===0?(g(),m('div',Tp,[e[16]||(e[16]=z(' You don\'t have any groups. ')),de(r,{to:'/groups'},{default:Pt(() => e[15]||(e[15]=[z('Create a group')])),_:1,__:[15]}),e[17]||(e[17]=z(' first to book as a group. '))])):K('',!0),a('div',Sp,[a('button',{type:'button',class:'btn btn-secondary',onClick:e[3]||(e[3]=(...l) => i.cancelReservation&&i.cancelReservation(...l)),disabled:o.confirming},' Cancel ',8,Lp),a('button',{type:'submit',class:'btn btn-success',disabled:o.confirming||o.bookingType==='group'&&!o.selectedGroupId},[o.confirming?(g(),m('span',Ap)):K('',!0),e[18]||(e[18]=z(' Confirm Booking '))],8,Cp)])],32)])])])]))]);}const Rp=Ue(dp,[['render',Bp]]),Dp={name:'MyBookingsView',inject:['socket'],data(){return{loading:!0,error:null,bookings:[],bookingLists:[],locations:[],groups:[],cancelling:null};},computed:{...Mt(['getUser','getGroups']),user(){return this.getUser;},activeBookings(){return this.bookings.filter(t => !(t.cancelled||t.cancelled===1||t.cancelled===!0));},cancelledBookings(){return this.bookings.filter(t => t.cancelled||t.cancelled===1||t.cancelled===!0);}},created(){this.fetchData(),this.socket.on('timeslot_cancelled',this.handleTimeslotCancelled),this.$root.socket&&this.$root.socket.on('timeslot_cancelled',this.handleTimeslotCancelled);},beforeUnmount(){this.socket.off('timeslot_cancelled',this.handleTimeslotCancelled),this.$root.socket&&this.$root.socket.off('timeslot_cancelled',this.handleTimeslotCancelled);},methods:{async fetchData(){try{this.loading=!0,console.log('MyBookings: Starting to fetch data'),console.log('MyBookings: Fetching user bookings');const t=await fetch('/api/my-bookings');if(!t.ok)throw new Error(`Failed to fetch bookings: ${t.status} ${t.statusText}`);const e=await t.json();console.log('MyBookings: Received bookings data',e),this.bookings=e.bookings||[],console.log('MyBookings: Fetching booking lists');const s=await fetch('/api/booking-lists');if(!s.ok)throw new Error(`Failed to fetch booking lists: ${s.status} ${s.statusText}`);const n=await s.json();console.log('MyBookings: Received booking lists data',n),this.bookingLists=n.bookingLists||[],console.log('MyBookings: Fetching locations');const o=await fetch('/api/locations');if(!o.ok)throw new Error(`Failed to fetch locations: ${o.status} ${o.statusText}`);const i=await o.json();console.log('MyBookings: Received locations data',i),this.locations=i.locations||[],console.log('MyBookings: Fetching groups'),await this.$store.dispatch('fetchGroups'),console.log('MyBookings: All data fetched successfully'),this.loading=!1;}catch(t){console.error('Error fetching data:',t),this.error=`Failed to load bookings: ${t.message}`,this.loading=!1;}},async cancelBooking(t){try{this.cancelling=t,console.log(`Attempting to cancel booking with ID: ${t}`);const e=await fetch(`/api/timeslots/${t}/cancel`,{method:'POST',headers:{'Content-Type':'application/json'}});if(e.ok){const s=await e.json();console.log('Received updated booking after cancellation:',s);const n=this.bookings.findIndex(o => o.id===t);n!==-1&&(s.cancelled=!0,this.bookings.splice(n,1,s),console.log('Updated booking in local state:',this.bookings[n])),this.$forceUpdate();}else{const s=await e.json();alert(s.error||'Failed to cancel booking');}}catch(e){console.error('Error cancelling booking:',e),alert('Failed to cancel booking. Please try again.');}finally{this.cancelling=null;}},getBookingListTitle(t){const e=this.bookingLists.find(s => s.id===t);return e?e.title:'Unknown';},getLocationName(t){if(!t)return'Not specified';const e=this.locations.find(s => s.id===t);return e?e.name:'Unknown';},getGroupName(t){const e=this.getGroups.find(s => s.id===t);return e?e.name:'Unknown';},canCancel(t){const e=this.bookingLists.find(s => s.id===t);return e?e.cancellationAllowed:!0;},canCancelBooking(t){if(console.log('Checking if user can cancel booking:',t),console.log('Current user:',this.user),t.cancelled||t.cancelled===1||t.cancelled===!0)return console.log('Booking is already cancelled'),!1;if((t.booked_by_student_id||t.bookedByStudentId)===this.user.id)return console.log('Booking was made by this user'),!0;const n=t.booked_by_group_id||t.bookedByGroupId;if(n){if(console.log('Booking was made by group:',n),console.log('User groups from store:',this.getGroups),console.log('User groups from user object:',this.user.groups),this.getGroups&&this.getGroups.length>0){const o=this.getGroups.some(i => i.id===n);return console.log('User is member of the booking group (from store):',o),o;}if(this.user.groups&&this.user.groups.length>0){const o=this.user.groups.some(i => Number(i.id)===Number(n));return console.log('User is member of the booking group (from user object):',o),o;}console.log('No group information available for user');}return console.log('User cannot cancel this booking'),!1;},formatDate(t){return t?new Date(t).toLocaleDateString():'';},formatDateTime(t){if(!t)return'';const e=new Date(t);return`${e.toLocaleDateString()} ${e.toLocaleTimeString()}`;},handleTimeslotCancelled(t){console.log('Received timeslot_cancelled event:',t);const e=this.bookings.findIndex(s => s.id===t.id);if(e!==-1){const s={...this.bookings[e],...t,cancelled:!0,cancelledAt:t.cancelledAt||new Date().toISOString()};this.bookings.splice(e,1,s),console.log('Updated booking via socket event:',s),this.$forceUpdate(),t.cancelledById===this.user.id?console.log('Booking was cancelled by current user'):t.bookedByGroupId&&this.user.groups&&this.user.groups.some(o => Number(o.id)===Number(t.bookedByGroupId))&&alert('A member of your group has cancelled a booking.');}else console.log('Booking not found in local array, fetching fresh data'),this.fetchData();}}},Op={key:0,class:'text-center my-5'},Pp={key:1,class:'alert alert-danger'},Np={key:2,class:'alert alert-info'},Ip={key:3},Mp={class:'card mb-4'},Up={class:'card-body'},$p={key:0,class:'alert alert-info'},jp={key:1,class:'table-responsive'},Fp={class:'table table-striped'},Vp={key:0},Gp={key:1},qp=['onClick','disabled'],Hp={key:0,class:'spinner-border spinner-border-sm me-1',role:'status','aria-hidden':'true'},Kp={class:'card'},Wp={class:'card-body'},zp={key:0,class:'alert alert-info'},Yp={key:1,class:'table-responsive'},Jp={class:'table table-striped'},Qp={key:0},Xp={key:1};function Zp(t,e,s,n,o,i){const r=Nt('router-link');return g(),m('div',null,[e[10]||(e[10]=a('h1',{class:'mb-4'},'My Bookings',-1)),o.loading?(g(),m('div',Op,e[0]||(e[0]=[a('div',{class:'spinner-border text-primary',role:'status'},[a('span',{class:'visually-hidden'},'Loading...')],-1),a('p',{class:'mt-2'},'Loading your bookings...',-1)]))):o.error?(g(),m('div',Pp,T(o.error),1)):o.bookings.length===0?(g(),m('div',Np,[e[2]||(e[2]=z(' You don\'t have any bookings yet. ')),de(r,{to:'/booking-lists'},{default:Pt(() => e[1]||(e[1]=[z('Browse booking lists')])),_:1,__:[1]}),e[3]||(e[3]=z(' to make a booking. '))])):(g(),m('div',Ip,[a('div',Mp,[e[6]||(e[6]=a('div',{class:'card-header bg-primary text-white'},[a('h5',{class:'mb-0'},'Active Bookings')],-1)),a('div',Up,[i.activeBookings.length===0?(g(),m('div',$p,' You don\'t have any active bookings. ')):(g(),m('div',jp,[a('table',Fp,[e[5]||(e[5]=a('thead',null,[a('tr',null,[a('th',null,'Date'),a('th',null,'Time'),a('th',null,'Location'),a('th',null,'Booking List'),a('th',null,'Booked As'),a('th',null,'Actions')])],-1)),a('tbody',null,[(g(!0),m(ae,null,_e(i.activeBookings,l => (g(),m('tr',{key:l.id},[a('td',null,T(i.formatDate(l.date)),1),a('td',null,T(l.time),1),a('td',null,T(l.location_name||i.getLocationName(l.location_id||l.locationId)),1),a('td',null,T(l.booking_list_title||i.getBookingListTitle(l.bookingListId)),1),a('td',null,[l.booked_by_group_id||l.bookedByGroupId?(g(),m('span',Vp,' Group: '+T(l.group_name||i.getGroupName(l.booked_by_group_id||l.bookedByGroupId)),1)):(g(),m('span',Gp,'Individual'))]),a('td',null,[a('button',{class:'btn btn-sm btn-danger',onClick:c => i.cancelBooking(l.id),disabled:o.cancelling===l.id||!i.canCancel(l.bookingListId)||!i.canCancelBooking(l)},[o.cancelling===l.id?(g(),m('span',Hp)):K('',!0),e[4]||(e[4]=z(' Cancel '))],8,qp)])]))),128))])])]))])]),a('div',Kp,[e[9]||(e[9]=a('div',{class:'card-header bg-secondary text-white'},[a('h5',{class:'mb-0'},'Booking History')],-1)),a('div',Wp,[i.cancelledBookings.length===0?(g(),m('div',zp,' You don\'t have any cancelled bookings. ')):(g(),m('div',Yp,[a('table',Jp,[e[8]||(e[8]=a('thead',null,[a('tr',null,[a('th',null,'Date'),a('th',null,'Time'),a('th',null,'Location'),a('th',null,'Booking List'),a('th',null,'Booked As'),a('th',null,'Status'),a('th',null,'Cancelled At')])],-1)),a('tbody',null,[(g(!0),m(ae,null,_e(i.cancelledBookings,l => (g(),m('tr',{key:l.id,class:'table-secondary'},[a('td',null,T(i.formatDate(l.date)),1),a('td',null,T(l.time),1),a('td',null,T(l.location_name||i.getLocationName(l.location_id||l.locationId)),1),a('td',null,T(l.booking_list_title||i.getBookingListTitle(l.bookingListId)),1),a('td',null,[l.booked_by_group_id||l.bookedByGroupId?(g(),m('span',Qp,' Group: '+T(l.group_name||i.getGroupName(l.booked_by_group_id||l.bookedByGroupId)),1)):(g(),m('span',Xp,'Individual'))]),e[7]||(e[7]=a('td',null,[a('span',{class:'badge bg-secondary'},'Cancelled')],-1)),a('td',null,T(i.formatDateTime(l.cancelledAt)),1)]))),128))])])]))])])]))]);}const eg=Ue(Dp,[['render',Zp]]),tg={name:'GroupsView',data(){return{loading:!0,error:null,newGroupName:'',creatingGroup:!1,newMemberUsernames:{},addingMember:!1,addingToGroupId:null,removingMember:!1,memberErrors:{}};},computed:{...Mt(['getGroups']),groups(){return this.getGroups;}},async created(){try{await this.$store.dispatch('fetchGroups'),this.loading=!1;}catch(t){console.error('Error loading groups:',t),this.error='Failed to load groups. Please try again later.',this.loading=!1;}},methods:{async createGroup(){if(this.newGroupName){this.creatingGroup=!0;try{await this.$store.dispatch('createGroup',this.newGroupName),this.newGroupName='';}catch(t){console.error('Error creating group:',t),this.error='Failed to create group. Please try again.';}finally{this.creatingGroup=!1;}}},async addMember(t){const e=this.newMemberUsernames[t];if(e){this.addingMember=!0,this.addingToGroupId=t,this.memberErrors[t]=null;try{await this.$store.dispatch('addGroupMember',{groupId:t,username:e}),this.newMemberUsernames[t]='';}catch(s){console.error('Error adding member:',s),this.memberErrors[t]='Failed to add member. Please check the username and try again.';}finally{this.addingMember=!1,this.addingToGroupId=null;}}},async removeMember(t,e){this.removingMember=!0;try{await this.$store.dispatch('removeGroupMember',{groupId:t,memberId:e});}catch(s){console.error('Error removing member:',s),this.error='Failed to remove member. Please try again.';}finally{this.removingMember=!1;}}}},sg={class:'row mb-4'},ng={class:'col-md-6'},og={class:'card'},ig={class:'card-body'},rg={class:'mb-3'},lg=['disabled'],ag={key:0,class:'spinner-border spinner-border-sm me-2',role:'status','aria-hidden':'true'},cg={key:0,class:'text-center my-5'},ug={key:1,class:'alert alert-danger'},dg={key:2,class:'alert alert-info'},fg={key:3},hg={class:'card-header bg-secondary text-white d-flex justify-content-between align-items-center'},pg={class:'mb-0'},gg={class:'card-body'},mg={class:'list-group mb-3'},yg=['onClick','disabled'],bg={class:'mt-4'},_g=['onSubmit'],vg={class:'input-group'},kg=['onUpdate:modelValue'],wg=['disabled'],Eg={key:0,class:'spinner-border spinner-border-sm me-2',role:'status','aria-hidden':'true'},xg={key:0,class:'text-danger mt-2'};function Tg(t,e,s,n,o,i){return g(),m('div',null,[e[9]||(e[9]=a('h1',{class:'mb-4'},'My Groups',-1)),a('div',sg,[a('div',ng,[a('div',og,[e[4]||(e[4]=a('div',{class:'card-header bg-primary text-white'},[a('h5',{class:'mb-0'},'Create New Group')],-1)),a('div',ig,[a('form',{onSubmit:e[1]||(e[1]=Fe((...r) => i.createGroup&&i.createGroup(...r),['prevent']))},[a('div',rg,[e[2]||(e[2]=a('label',{for:'groupName',class:'form-label'},'Group Name',-1)),H(a('input',{id:'groupName','onUpdate:modelValue':e[0]||(e[0]=r => o.newGroupName=r),type:'text',class:'form-control',placeholder:'Enter group name',required:''},null,512),[[J,o.newGroupName]])]),a('button',{type:'submit',class:'btn btn-primary',disabled:o.creatingGroup},[o.creatingGroup?(g(),m('span',ag)):K('',!0),e[3]||(e[3]=z(' Create Group '))],8,lg)],32)])])])]),o.loading?(g(),m('div',cg,e[5]||(e[5]=[a('div',{class:'spinner-border text-primary',role:'status'},[a('span',{class:'visually-hidden'},'Loading...')],-1),a('p',{class:'mt-2'},'Loading your groups...',-1)]))):o.error?(g(),m('div',ug,T(o.error),1)):i.groups.length===0?(g(),m('div',dg,' You don\'t have any groups yet. Create a new group to get started. ')):(g(),m('div',fg,[(g(!0),m(ae,null,_e(i.groups,r => (g(),m('div',{key:r.id,class:'card mb-4'},[a('div',hg,[a('h5',pg,T(r.name),1)]),a('div',gg,[e[8]||(e[8]=a('h6',null,'Members:',-1)),a('ul',mg,[(g(!0),m(ae,null,_e(r.members,l => (g(),m('li',{key:l.id,class:'list-group-item d-flex justify-content-between align-items-center'},[z(T(l.username)+' ',1),a('button',{class:'btn btn-sm btn-danger',onClick:c => i.removeMember(r.id,l.id),disabled:o.removingMember},' Remove ',8,yg)]))),128))]),a('div',bg,[e[7]||(e[7]=a('h6',null,'Add Member:',-1)),a('form',{onSubmit:Fe(l => i.addMember(r.id),['prevent'])},[a('div',vg,[H(a('input',{type:'text',class:'form-control',placeholder:'Enter username','onUpdate:modelValue':l => o.newMemberUsernames[r.id]=l,required:''},null,8,kg),[[J,o.newMemberUsernames[r.id]]]),a('button',{type:'submit',class:'btn btn-primary',disabled:o.addingMember},[o.addingMember&&o.addingToGroupId===r.id?(g(),m('span',Eg)):K('',!0),e[6]||(e[6]=z(' Add '))],8,wg)]),o.memberErrors[r.id]?(g(),m('div',xg,T(o.memberErrors[r.id]),1)):K('',!0)],40,_g)])])]))),128))]))]);}const Sg=Ue(tg,[['render',Tg]]),Lg={name:'AdminLoginView',data(){return{username:'',password:'',loading:!1,error:null};},methods:{async login(){if(!this.username||!this.password){this.error='Please enter both username and password';return;}this.loading=!0,this.error=null;try{console.log('Attempting admin login with username:',this.username);const t=await fetch('/api/admin/login',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify({username:this.username,password:this.password})});console.log('Admin login response status:',t.status);const e=await t.text();if(console.log('Admin login response text:',e),!e)throw new Error('Empty response from server');const s=JSON.parse(e);if(!t.ok)throw new Error(s.error||'Login failed');console.log('Admin login successful:',s),this.$store.commit('setAdminAuthenticated',!0),this.$store.commit('setAdminUsername',this.username),this.$router.push('/admin');}catch(t){console.error('Admin login error:',t),this.error=t.message||'Login failed. Please try again.';}finally{this.loading=!1;}}}},Cg={class:'row justify-content-center'},Ag={class:'col-md-6'},Bg={class:'card'},Rg={class:'card-body'},Dg={class:'mb-3'},Og={class:'mb-3'},Pg={key:0,class:'alert alert-danger mt-3'},Ng={class:'d-flex justify-content-between align-items-center'},Ig=['disabled'],Mg={key:0,class:'spinner-border spinner-border-sm me-2',role:'status','aria-hidden':'true'};function Ug(t,e,s,n,o,i){return g(),m('div',Cg,[a('div',Ag,[a('div',Bg,[e[7]||(e[7]=a('div',{class:'card-header bg-dark text-white'},[a('h3',{class:'mb-0'},'Admin Login')],-1)),a('div',Rg,[a('form',{onSubmit:e[2]||(e[2]=Fe((...r) => i.login&&i.login(...r),['prevent']))},[a('div',Dg,[e[3]||(e[3]=a('label',{for:'username',class:'form-label'},'Username',-1)),H(a('input',{id:'username','onUpdate:modelValue':e[0]||(e[0]=r => o.username=r),type:'text',class:'form-control',placeholder:'Enter admin username',required:''},null,512),[[J,o.username]])]),a('div',Og,[e[4]||(e[4]=a('label',{for:'password',class:'form-label'},'Password',-1)),H(a('input',{id:'password','onUpdate:modelValue':e[1]||(e[1]=r => o.password=r),type:'password',class:'form-control',placeholder:'Enter admin password',required:''},null,512),[[J,o.password]])]),o.error?(g(),m('div',Pg,T(o.error),1)):K('',!0),a('div',Ng,[a('button',{type:'submit',class:'btn btn-dark',disabled:o.loading},[o.loading?(g(),m('span',Mg)):K('',!0),e[5]||(e[5]=z(' Login '))],8,Ig),e[6]||(e[6]=a('small',{class:'text-muted'},'Default: admin1 / admin123',-1))])],32)])])])]);}const $g=Ue(Lg,[['render',Ug]]),jg={name:'AdminDashboardView',data(){return{showLocationsModal:!1,showExamTypesModal:!1,locations:[],examinationTypes:[],loadingLocations:!1,loadingExamTypes:!1,newLocation:{name:'',description:''},newExamType:{name:'',description:''},addingLocation:!1,addingExamType:!1};},computed:{...Mt(['getAdminUsername'])},watch:{showLocationsModal(t){t&&this.fetchLocations();},showExamTypesModal(t){t&&this.fetchExaminationTypes();}},methods:{async fetchLocations(){try{this.loadingLocations=!0;const e=await(await fetch('/api/admin/locations')).json();this.locations=e.locations;}catch(t){console.error('Error fetching locations:',t),alert('Failed to load locations');}finally{this.loadingLocations=!1;}},async fetchExaminationTypes(){try{this.loadingExamTypes=!0;const e=await(await fetch('/api/admin/examination-types')).json();this.examinationTypes=e.examinationTypes;}catch(t){console.error('Error fetching examination types:',t),alert('Failed to load examination types');}finally{this.loadingExamTypes=!1;}},async addLocation(){try{this.addingLocation=!0;const t=await fetch('/api/admin/locations',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(this.newLocation)});if(t.ok){const e=await t.json();this.locations.push(e),this.newLocation={name:'',description:''};}else{const e=await t.json();throw new Error(e.error||'Failed to add location');}}catch(t){console.error('Error adding location:',t),alert(t.message||'Failed to add location');}finally{this.addingLocation=!1;}},async addExamType(){try{this.addingExamType=!0;const t=await fetch('/api/admin/examination-types',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(this.newExamType)});if(t.ok){const e=await t.json();this.examinationTypes.push(e),this.newExamType={name:'',description:''};}else{const e=await t.json();throw new Error(e.error||'Failed to add examination type');}}catch(t){console.error('Error adding examination type:',t),alert(t.message||'Failed to add examination type');}finally{this.addingExamType=!1;}}}},Fg={class:'row'},Vg={class:'col-md-4 mb-4'},Gg={class:'card h-100'},qg={class:'card-body'},Hg={class:'col-md-4 mb-4'},Kg={class:'card h-100'},Wg={class:'card-body'},zg={class:'col-md-4 mb-4'},Yg={class:'card h-100'},Jg={class:'card-body'},Qg={key:0,class:'modal d-block',tabindex:'-1'},Xg={class:'modal-dialog modal-lg'},Zg={class:'modal-content'},em={class:'modal-header bg-success text-white'},tm={class:'modal-body'},sm={key:0,class:'text-center my-3'},nm={key:1},om={class:'row g-3'},im={class:'col-md-6'},rm={class:'col-md-6'},lm={class:'input-group'},am=['disabled'],cm={key:0,class:'spinner-border spinner-border-sm me-1',role:'status','aria-hidden':'true'},um={class:'table-responsive'},dm={class:'table table-striped'},fm={class:'modal-footer'},hm={key:1,class:'modal d-block',tabindex:'-1'},pm={class:'modal-dialog modal-lg'},gm={class:'modal-content'},mm={class:'modal-header bg-info text-white'},ym={class:'modal-body'},bm={key:0,class:'text-center my-3'},_m={key:1},vm={class:'row g-3'},km={class:'col-md-6'},wm={class:'col-md-6'},Em={class:'input-group'},xm=['disabled'],Tm={key:0,class:'spinner-border spinner-border-sm me-1',role:'status','aria-hidden':'true'},Sm={class:'table-responsive'},Lm={class:'table table-striped'},Cm={class:'modal-footer'},Am={key:2,class:'modal-backdrop fade show'};function Bm(t,e,s,n,o,i){const r=Nt('router-link');return g(),m('div',null,[e[31]||(e[31]=a('h1',{class:'mb-4'},'Admin Dashboard',-1)),a('div',Fg,[a('div',Vg,[a('div',Gg,[e[14]||(e[14]=a('div',{class:'card-header bg-primary text-white'},[a('h5',{class:'mb-0'},'Booking Lists')],-1)),a('div',qg,[e[13]||(e[13]=a('p',null,'Manage booking lists, timeslots, and booking constraints.',-1)),de(r,{to:'/admin/booking-lists',class:'btn btn-primary'},{default:Pt(() => e[12]||(e[12]=[z('Manage Booking Lists')])),_:1,__:[12]})])])]),a('div',Hg,[a('div',Kg,[e[16]||(e[16]=a('div',{class:'card-header bg-success text-white'},[a('h5',{class:'mb-0'},'Locations')],-1)),a('div',Wg,[e[15]||(e[15]=a('p',null,'Manage locations for bookings.',-1)),a('button',{class:'btn btn-success',onClick:e[0]||(e[0]=l => o.showLocationsModal=!0)},'Manage Locations')])])]),a('div',zg,[a('div',Yg,[e[18]||(e[18]=a('div',{class:'card-header bg-info text-white'},[a('h5',{class:'mb-0'},'Examination Types')],-1)),a('div',Jg,[e[17]||(e[17]=a('p',null,'Manage examination types for booking lists.',-1)),a('button',{class:'btn btn-info',onClick:e[1]||(e[1]=l => o.showExamTypesModal=!0)},'Manage Examination Types')])])])]),o.showLocationsModal?(g(),m('div',Qg,[a('div',Xg,[a('div',Zg,[a('div',em,[e[19]||(e[19]=a('h5',{class:'modal-title'},'Manage Locations',-1)),a('button',{type:'button',class:'btn-close',onClick:e[2]||(e[2]=l => o.showLocationsModal=!1)})]),a('div',tm,[o.loadingLocations?(g(),m('div',sm,e[20]||(e[20]=[a('div',{class:'spinner-border text-success',role:'status'},[a('span',{class:'visually-hidden'},'Loading...')],-1)]))):(g(),m('div',nm,[e[23]||(e[23]=a('h6',null,'Add New Location',-1)),a('form',{onSubmit:e[5]||(e[5]=Fe((...l) => i.addLocation&&i.addLocation(...l),['prevent'])),class:'mb-4'},[a('div',om,[a('div',im,[H(a('input',{type:'text',class:'form-control','onUpdate:modelValue':e[3]||(e[3]=l => o.newLocation.name=l),placeholder:'Location name',required:''},null,512),[[J,o.newLocation.name]])]),a('div',rm,[a('div',lm,[H(a('input',{type:'text',class:'form-control','onUpdate:modelValue':e[4]||(e[4]=l => o.newLocation.description=l),placeholder:'Description (optional)'},null,512),[[J,o.newLocation.description]]),a('button',{type:'submit',class:'btn btn-success',disabled:o.addingLocation},[o.addingLocation?(g(),m('span',cm)):K('',!0),e[21]||(e[21]=z(' Add '))],8,am)])])])],32),e[24]||(e[24]=a('h6',null,'Existing Locations',-1)),a('div',um,[a('table',dm,[e[22]||(e[22]=a('thead',null,[a('tr',null,[a('th',null,'ID'),a('th',null,'Name'),a('th',null,'Description')])],-1)),a('tbody',null,[(g(!0),m(ae,null,_e(o.locations,l => (g(),m('tr',{key:l.id},[a('td',null,T(l.id),1),a('td',null,T(l.name),1),a('td',null,T(l.description||'N/A'),1)]))),128))])])])]))]),a('div',fm,[a('button',{type:'button',class:'btn btn-secondary',onClick:e[6]||(e[6]=l => o.showLocationsModal=!1)},'Close')])])])])):K('',!0),o.showExamTypesModal?(g(),m('div',hm,[a('div',pm,[a('div',gm,[a('div',mm,[e[25]||(e[25]=a('h5',{class:'modal-title'},'Manage Examination Types',-1)),a('button',{type:'button',class:'btn-close',onClick:e[7]||(e[7]=l => o.showExamTypesModal=!1)})]),a('div',ym,[o.loadingExamTypes?(g(),m('div',bm,e[26]||(e[26]=[a('div',{class:'spinner-border text-info',role:'status'},[a('span',{class:'visually-hidden'},'Loading...')],-1)]))):(g(),m('div',_m,[e[29]||(e[29]=a('h6',null,'Add New Examination Type',-1)),a('form',{onSubmit:e[10]||(e[10]=Fe((...l) => i.addExamType&&i.addExamType(...l),['prevent'])),class:'mb-4'},[a('div',vm,[a('div',km,[H(a('input',{type:'text',class:'form-control','onUpdate:modelValue':e[8]||(e[8]=l => o.newExamType.name=l),placeholder:'Type name',required:''},null,512),[[J,o.newExamType.name]])]),a('div',wm,[a('div',Em,[H(a('input',{type:'text',class:'form-control','onUpdate:modelValue':e[9]||(e[9]=l => o.newExamType.description=l),placeholder:'Description (optional)'},null,512),[[J,o.newExamType.description]]),a('button',{type:'submit',class:'btn btn-info',disabled:o.addingExamType},[o.addingExamType?(g(),m('span',Tm)):K('',!0),e[27]||(e[27]=z(' Add '))],8,xm)])])])],32),e[30]||(e[30]=a('h6',null,'Existing Examination Types',-1)),a('div',Sm,[a('table',Lm,[e[28]||(e[28]=a('thead',null,[a('tr',null,[a('th',null,'ID'),a('th',null,'Name'),a('th',null,'Description')])],-1)),a('tbody',null,[(g(!0),m(ae,null,_e(o.examinationTypes,l => (g(),m('tr',{key:l.id},[a('td',null,T(l.id),1),a('td',null,T(l.name),1),a('td',null,T(l.description||'N/A'),1)]))),128))])])])]))]),a('div',Cm,[a('button',{type:'button',class:'btn btn-secondary',onClick:e[11]||(e[11]=l => o.showExamTypesModal=!1)},'Close')])])])])):K('',!0),o.showLocationsModal||o.showExamTypesModal?(g(),m('div',Am)):K('',!0)]);}const Rm=Ue(jg,[['render',Bm],['__scopeId','data-v-1f161faa']]),Dm={name:'AdminBookingListsView',inject:['socket'],data(){return{loading:!0,error:null,bookingLists:[],locations:[],examinationTypes:[],showCreateModal:!1,creating:!1,deletingBookingList:null,newBookingList:{title:'',description:'',locationId:'',examinationTypeId:'',bookingStartDate:'',bookingEndDate:'',visibilityStartDate:'',visibilityEndDate:'',cancellationDeadline:'',maxBookingsPerStudent:1}};},created(){this.fetchData(),this.socket.on('booking_list_updated',this.handleBookingListUpdated),this.socket.on('booking_list_deleted',this.handleBookingListDeleted);},beforeUnmount(){this.socket.off('booking_list_updated',this.handleBookingListUpdated),this.socket.off('booking_list_deleted',this.handleBookingListDeleted);},methods:{async fetchData(){try{this.loading=!0;const e=await(await fetch('/api/admin/booking-lists')).json();this.bookingLists=e.bookingLists;const n=await(await fetch('/api/admin/locations')).json();this.locations=n.locations;const i=await(await fetch('/api/admin/examination-types')).json();this.examinationTypes=i.examinationTypes,this.loading=!1;}catch(t){console.error('Error fetching data:',t),this.error='Failed to load data. Please try again later.',this.loading=!1;}},async createBookingList(){try{this.creating=!0;const t={...this.newBookingList};for(const s in t)t[s]===''&&(t[s]=null);const e=await fetch('/api/admin/booking-lists',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(t)});if(e.ok){const s=await e.json();this.bookingLists.push(s),this.showCreateModal=!1,this.resetNewBookingList();}else{const s=await e.json();throw new Error(s.error||'Failed to create booking list');}}catch(t){console.error('Error creating booking list:',t),alert(t.message||'Failed to create booking list');}finally{this.creating=!1;}},resetNewBookingList(){this.newBookingList={title:'',description:'',locationId:'',examinationTypeId:'',bookingStartDate:'',bookingEndDate:'',visibilityStartDate:'',visibilityEndDate:'',cancellationDeadline:'',maxBookingsPerStudent:1};},getExaminationTypeName(t){if(!t)return null;const e=this.examinationTypes.find(s => s.id===t);return e?e.name:null;},getLocationName(t){if(!t)return null;const e=this.locations.find(s => s.id===t);return e?e.name:null;},formatDate(t){return t?new Date(t).toLocaleDateString():'';},async deleteBookingList(t){if(confirm('Are you sure you want to delete this booking list? This will also delete all associated timeslots and cannot be undone.'))try{this.deletingBookingList=t;const e=await fetch(`/api/admin/booking-lists/${t}`,{method:'DELETE'});if(e.ok)this.bookingLists=this.bookingLists.filter(s => s.id!==t);else{const s=await e.json();throw new Error(s.error||'Failed to delete booking list');}}catch(e){console.error('Error deleting booking list:',e),alert(e.message||'Failed to delete booking list');}finally{this.deletingBookingList=null;}},handleBookingListUpdated(t){const e=this.bookingLists.findIndex(s => s.id===t.id);e!==-1&&this.bookingLists.splice(e,1,t);},handleBookingListDeleted(t){this.bookingLists=this.bookingLists.filter(e => e.id!==t);}}},Om={class:'mb-4'},Pm={key:0,class:'text-center my-5'},Nm={key:1,class:'alert alert-danger'},Im={key:2,class:'alert alert-info'},Mm={key:3,class:'row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4'},Um={class:'card h-100'},$m={class:'card-header'},jm={class:'card-title mb-0'},Fm={class:'card-body'},Vm={key:0,class:'card-text'},Gm={key:1,class:'card-text'},qm={class:'mb-3'},Hm={key:0},Km={key:1},Wm={class:'mb-3'},zm={key:0},Ym={key:1},Jm={class:'mb-3'},Qm={key:0},Xm={key:1},Zm={key:2},ey={key:3},ty={class:'mb-3'},sy={key:0},ny={key:1},oy={key:2},iy={key:3},ry={class:'mb-3'},ly={key:0},ay={key:1},cy={class:'mb-3'},uy={class:'card-footer'},dy={class:'d-flex justify-content-between mb-2'},fy=['onClick','disabled'],hy={key:0,class:'spinner-border spinner-border-sm',role:'status','aria-hidden':'true'},py={key:1,class:'bi bi-trash'},gy={key:4,class:'modal d-block',tabindex:'-1'},my={class:'modal-dialog modal-lg'},yy={class:'modal-content'},by={class:'modal-header bg-primary text-white'},_y={class:'modal-body'},vy={class:'mb-3'},ky={class:'mb-3'},wy={class:'row mb-3'},Ey={class:'col-md-6'},xy=['value'],Ty={class:'col-md-6'},Sy=['value'],Ly={class:'row mb-3'},Cy={class:'col-md-6'},Ay={class:'col-md-6'},By={class:'row mb-3'},Ry={class:'col-md-6'},Dy={class:'col-md-6'},Oy={class:'row mb-3'},Py={class:'col-md-6'},Ny={class:'col-md-6'},Iy={class:'modal-footer'},My=['disabled'],Uy={key:0,class:'spinner-border spinner-border-sm me-2',role:'status','aria-hidden':'true'},$y={key:5,class:'modal-backdrop fade show'};function jy(t,e,s,n,o,i){const r=Nt('router-link');return g(),m('div',null,[e[36]||(e[36]=a('h1',{class:'mb-4'},'Manage Booking Lists',-1)),a('div',Om,[a('button',{class:'btn btn-primary',onClick:e[0]||(e[0]=l => o.showCreateModal=!0)},' Create New Booking List ')]),o.loading?(g(),m('div',Pm,e[14]||(e[14]=[a('div',{class:'spinner-border text-primary',role:'status'},[a('span',{class:'visually-hidden'},'Loading...')],-1),a('p',{class:'mt-2'},'Loading booking lists...',-1)]))):o.error?(g(),m('div',Nm,T(o.error),1)):o.bookingLists.length===0?(g(),m('div',Im,' No booking lists found. Create a new booking list to get started. ')):(g(),m('div',Mm,[(g(!0),m(ae,null,_e(o.bookingLists,l => (g(),m('div',{key:l.id,class:'col'},[a('div',Um,[a('div',$m,[a('h5',jm,T(l.title),1)]),a('div',Fm,[l.description?(g(),m('p',Vm,T(l.description),1)):(g(),m('p',Gm,'No description available.')),a('div',qm,[e[15]||(e[15]=a('strong',null,'Examination Type:',-1)),i.getExaminationTypeName(l.examinationTypeId)?(g(),m('span',Hm,T(i.getExaminationTypeName(l.examinationTypeId)),1)):(g(),m('span',Km,'Not specified'))]),a('div',Wm,[e[16]||(e[16]=a('strong',null,'Default Location:',-1)),i.getLocationName(l.locationId)?(g(),m('span',zm,T(i.getLocationName(l.locationId)),1)):(g(),m('span',Ym,'Not specified'))]),a('div',Jm,[e[17]||(e[17]=a('strong',null,'Booking Period:',-1)),l.bookingStartDate&&l.bookingEndDate?(g(),m('span',Qm,T(i.formatDate(l.bookingStartDate))+' to '+T(i.formatDate(l.bookingEndDate)),1)):l.bookingStartDate?(g(),m('span',Xm,' From '+T(i.formatDate(l.bookingStartDate)),1)):l.bookingEndDate?(g(),m('span',Zm,' Until '+T(i.formatDate(l.bookingEndDate)),1)):(g(),m('span',ey,'No restrictions'))]),a('div',ty,[e[18]||(e[18]=a('strong',null,'Visibility Period:',-1)),l.visibilityStartDate&&l.visibilityEndDate?(g(),m('span',sy,T(i.formatDate(l.visibilityStartDate))+' to '+T(i.formatDate(l.visibilityEndDate)),1)):l.visibilityStartDate?(g(),m('span',ny,' From '+T(i.formatDate(l.visibilityStartDate)),1)):l.visibilityEndDate?(g(),m('span',oy,' Until '+T(i.formatDate(l.visibilityEndDate)),1)):(g(),m('span',iy,'Always visible'))]),a('div',ry,[e[19]||(e[19]=a('strong',null,'Cancellation Deadline:',-1)),l.cancellationDeadline?(g(),m('span',ly,T(i.formatDate(l.cancellationDeadline)),1)):(g(),m('span',ay,'No deadline'))]),a('div',cy,[e[20]||(e[20]=a('strong',null,'Max Bookings:',-1)),z(' '+T(l.maxBookingsPerStudent)+' per student/group ',1)])]),a('div',uy,[a('div',dy,[de(r,{to:`/admin/booking-lists/${l.id}`,class:'btn btn-primary flex-grow-1 me-2'},{default:Pt(() => e[21]||(e[21]=[z(' Manage Timeslots ')])),_:2,__:[21]},1032,['to']),a('button',{class:'btn btn-danger',onClick:c => i.deleteBookingList(l.id),disabled:o.deletingBookingList===l.id},[o.deletingBookingList===l.id?(g(),m('span',hy)):(g(),m('i',py))],8,fy)])])])]))),128))])),o.showCreateModal?(g(),m('div',gy,[a('div',my,[a('div',yy,[a('div',by,[e[22]||(e[22]=a('h5',{class:'modal-title'},'Create New Booking List',-1)),a('button',{type:'button',class:'btn-close',onClick:e[1]||(e[1]=l => o.showCreateModal=!1)})]),a('div',_y,[a('form',{onSubmit:e[13]||(e[13]=Fe((...l) => i.createBookingList&&i.createBookingList(...l),['prevent']))},[a('div',vy,[e[23]||(e[23]=a('label',{for:'title',class:'form-label'},'Title *',-1)),H(a('input',{id:'title','onUpdate:modelValue':e[2]||(e[2]=l => o.newBookingList.title=l),type:'text',class:'form-control',required:''},null,512),[[J,o.newBookingList.title]])]),a('div',ky,[e[24]||(e[24]=a('label',{for:'description',class:'form-label'},'Description',-1)),H(a('textarea',{id:'description','onUpdate:modelValue':e[3]||(e[3]=l => o.newBookingList.description=l),class:'form-control',rows:'3'},null,512),[[J,o.newBookingList.description]])]),a('div',wy,[a('div',Ey,[e[26]||(e[26]=a('label',{for:'locationType',class:'form-label'},'Default Location',-1)),H(a('select',{id:'locationType','onUpdate:modelValue':e[4]||(e[4]=l => o.newBookingList.locationId=l),class:'form-select'},[e[25]||(e[25]=a('option',{value:''},'None',-1)),(g(!0),m(ae,null,_e(o.locations,l => (g(),m('option',{key:l.id,value:l.id},T(l.name),9,xy))),128))],512),[[Zt,o.newBookingList.locationId]])]),a('div',Ty,[e[28]||(e[28]=a('label',{for:'examinationType',class:'form-label'},'Examination Type',-1)),H(a('select',{id:'examinationType','onUpdate:modelValue':e[5]||(e[5]=l => o.newBookingList.examinationTypeId=l),class:'form-select'},[e[27]||(e[27]=a('option',{value:''},'None',-1)),(g(!0),m(ae,null,_e(o.examinationTypes,l => (g(),m('option',{key:l.id,value:l.id},T(l.name),9,Sy))),128))],512),[[Zt,o.newBookingList.examinationTypeId]])])]),a('div',Ly,[a('div',Cy,[e[29]||(e[29]=a('label',{for:'bookingStartDate',class:'form-label'},'Booking Start Date',-1)),H(a('input',{id:'bookingStartDate','onUpdate:modelValue':e[6]||(e[6]=l => o.newBookingList.bookingStartDate=l),type:'date',class:'form-control'},null,512),[[J,o.newBookingList.bookingStartDate]])]),a('div',Ay,[e[30]||(e[30]=a('label',{for:'bookingEndDate',class:'form-label'},'Booking End Date',-1)),H(a('input',{id:'bookingEndDate','onUpdate:modelValue':e[7]||(e[7]=l => o.newBookingList.bookingEndDate=l),type:'date',class:'form-control'},null,512),[[J,o.newBookingList.bookingEndDate]])])]),a('div',By,[a('div',Ry,[e[31]||(e[31]=a('label',{for:'visibilityStartDate',class:'form-label'},'Visibility Start Date',-1)),H(a('input',{id:'visibilityStartDate','onUpdate:modelValue':e[8]||(e[8]=l => o.newBookingList.visibilityStartDate=l),type:'date',class:'form-control'},null,512),[[J,o.newBookingList.visibilityStartDate]])]),a('div',Dy,[e[32]||(e[32]=a('label',{for:'visibilityEndDate',class:'form-label'},'Visibility End Date',-1)),H(a('input',{id:'visibilityEndDate','onUpdate:modelValue':e[9]||(e[9]=l => o.newBookingList.visibilityEndDate=l),type:'date',class:'form-control'},null,512),[[J,o.newBookingList.visibilityEndDate]])])]),a('div',Oy,[a('div',Py,[e[33]||(e[33]=a('label',{for:'cancellationDeadline',class:'form-label'},'Cancellation Deadline',-1)),H(a('input',{id:'cancellationDeadline','onUpdate:modelValue':e[10]||(e[10]=l => o.newBookingList.cancellationDeadline=l),type:'date',class:'form-control'},null,512),[[J,o.newBookingList.cancellationDeadline]])]),a('div',Ny,[e[34]||(e[34]=a('label',{for:'maxBookings',class:'form-label'},'Max Bookings per Student/Group',-1)),H(a('input',{id:'maxBookings','onUpdate:modelValue':e[11]||(e[11]=l => o.newBookingList.maxBookingsPerStudent=l),type:'number',min:'1',class:'form-control',required:''},null,512),[[J,o.newBookingList.maxBookingsPerStudent,void 0,{number:!0}]])])]),a('div',Iy,[a('button',{type:'button',class:'btn btn-secondary',onClick:e[12]||(e[12]=l => o.showCreateModal=!1)},'Cancel'),a('button',{type:'submit',class:'btn btn-primary',disabled:o.creating},[o.creating?(g(),m('span',Uy)):K('',!0),e[35]||(e[35]=z(' Create '))],8,My)])],32)])])])])):K('',!0),o.showCreateModal?(g(),m('div',$y)):K('',!0)]);}const Fy=Ue(Dm,[['render',jy],['__scopeId','data-v-496bf688']]),Vy={name:'AdminBookingListDetailView',inject:['socket'],data(){return{loading:!0,error:null,bookingList:{},timeslots:[],locations:[],examinationTypes:[],students:[],groups:[],showEditModal:!1,updating:!1,addingTimeslot:!1,deletingTimeslot:null,newTimeslot:{date:new Date().toISOString().split('T')[0],time:'',locationId:''},editedBookingList:{}};},created(){this.fetchData(),this.socket.on('timeslot_created',this.handleTimeslotCreated),this.socket.on('timeslot_deleted',this.handleTimeslotDeleted),this.socket.on('booking_list_updated',this.handleBookingListUpdated),this.socket.on('booking_list_deleted',this.handleBookingListDeleted);},beforeUnmount(){this.socket.off('timeslot_created',this.handleTimeslotCreated),this.socket.off('timeslot_deleted',this.handleTimeslotDeleted),this.socket.off('booking_list_updated',this.handleBookingListUpdated),this.socket.off('booking_list_deleted',this.handleBookingListDeleted);},methods:{async fetchData(){try{this.loading=!0;const t=this.$route.params.id,e=await fetch('/api/admin/booking-lists');if(!e.ok)throw new Error('Failed to fetch booking lists');const s=await e.json();if(this.bookingList=s.bookingLists.find(f => f.id===Number(t)),!this.bookingList){this.error='Booking list not found',this.loading=!1;return;}this.editedBookingList={...this.bookingList};const n=await fetch(`/api/admin/booking-lists/${t}/timeslots`);if(!n.ok)throw new Error('Failed to fetch timeslots');const o=await n.json();this.timeslots=o.timeslots;const i=await fetch('/api/admin/locations');if(!i.ok)throw new Error('Failed to fetch locations');const r=await i.json();this.locations=r.locations;const l=await fetch('/api/admin/examination-types');if(!l.ok)throw new Error('Failed to fetch examination types');const c=await l.json();this.examinationTypes=c.examinationTypes;const h=await fetch('/api/admin/students');if(!h.ok)console.warn('Failed to fetch students, will display IDs instead of names');else{const f=await h.json();this.students=f.students;}const u=await fetch('/api/admin/groups');if(!u.ok)console.warn('Failed to fetch groups, will display IDs instead of names');else{const f=await u.json();this.groups=f.groups;}this.loading=!1;}catch(t){console.error('Error fetching data:',t),this.error='Failed to load data: '+t.message,this.loading=!1;}},async addTimeslot(){try{this.addingTimeslot=!0;const t={...this.newTimeslot,bookingListId:this.bookingList.id},e=await fetch('/api/admin/timeslots',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(t)});if(e.ok)this.newTimeslot={date:new Date().toISOString().split('T')[0],time:'',locationId:''};else{const s=await e.json();throw new Error(s.error||'Failed to add timeslot');}}catch(t){console.error('Error adding timeslot:',t),alert(t.message||'Failed to add timeslot');}finally{this.addingTimeslot=!1;}},async deleteTimeslot(t){if(confirm('Are you sure you want to delete this timeslot?'))try{this.deletingTimeslot=t;const e=await fetch(`/api/admin/timeslots/${t}`,{method:'DELETE'});if(e.ok)this.timeslots=this.timeslots.filter(s => s.id!==t);else{const s=await e.json();throw new Error(s.error||'Failed to delete timeslot');}}catch(e){console.error('Error deleting timeslot:',e),alert(e.message||'Failed to delete timeslot');}finally{this.deletingTimeslot=null;}},async updateBookingList(){try{this.updating=!0;const t={...this.editedBookingList};for(const s in t)t[s]===''&&(t[s]=null);const e=await fetch(`/api/admin/booking-lists/${t.id}`,{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify(t)});if(e.ok){const s=await e.json();this.bookingList=s,this.showEditModal=!1;}else{const s=await e.json();throw new Error(s.error||'Failed to update booking list');}}catch(t){console.error('Error updating booking list:',t),alert(t.message||'Failed to update booking list');}finally{this.updating=!1;}},getExaminationTypeName(t){if(!t)return null;const e=this.examinationTypes.find(s => s.id===t);return e?e.name:null;},getLocationName(t){if(!t)return null;const e=this.locations.find(s => s.id===t);return e?e.name:null;},getStudentName(t){if(!t)return'Unknown';const e=this.students.find(s => s.id===t);return e?e.username:`Student ID: ${t}`;},getGroupName(t){if(!t)return'Unknown';const e=this.groups.find(s => s.id===t);return e?e.name:`Group ID: ${t}`;},formatDate(t){return t?new Date(t).toLocaleDateString():'';},handleTimeslotCreated(t){t.bookingListId===this.bookingList.id&&this.timeslots.push(t);},handleTimeslotDeleted(t){this.timeslots=this.timeslots.filter(e => e.id!==t);},handleBookingListUpdated(t){t.id===this.bookingList.id&&(this.bookingList=t);},handleBookingListDeleted(t){t===this.bookingList.id&&(alert('This booking list has been deleted by another administrator.'),this.$router.push('/admin/booking-lists'));}}},Gy={key:0,class:'text-center my-5'},qy={key:1,class:'alert alert-danger'},Hy={key:2},Ky={class:'card mb-4'},Wy={class:'card-header bg-primary text-white d-flex justify-content-between align-items-center'},zy={class:'mb-0'},Yy={class:'card-body'},Jy={key:0},Qy={class:'row mb-4'},Xy={class:'col-md-6'},Zy={class:'list-group'},eb={class:'list-group-item'},tb={key:0},sb={key:1},nb={class:'list-group-item'},ob={key:0},ib={key:1},rb={class:'list-group-item'},lb={class:'col-md-6'},ab={class:'list-group'},cb={class:'list-group-item'},ub={key:0},db={key:1},fb={key:2},hb={key:3},pb={class:'list-group-item'},gb={key:0},mb={key:1},yb={key:2},bb={key:3},_b={class:'list-group-item'},vb={key:0},kb={key:1},wb={class:'mb-4'},Eb={class:'col-md-3'},xb={class:'col-md-3'},Tb={class:'col-md-4'},Sb=['value'],Lb={class:'col-md-2 d-flex align-items-end'},Cb=['disabled'],Ab={key:0,class:'spinner-border spinner-border-sm me-1',role:'status','aria-hidden':'true'},Bb={class:'mb-4'},Rb={key:0,class:'alert alert-info'},Db={key:1,class:'table-responsive'},Ob={class:'table table-striped table-hover'},Pb={key:0,class:'badge bg-secondary'},Nb={key:1,class:'badge bg-danger'},Ib={key:2,class:'badge bg-warning'},Mb={key:3,class:'badge bg-success'},Ub={key:0},$b={key:0},jb={key:1},Fb={class:'text-muted'},Vb={key:1},Gb=['onClick','disabled'],qb={key:0,class:'spinner-border spinner-border-sm me-1',role:'status','aria-hidden':'true'},Hb={key:3,class:'modal d-block',tabindex:'-1'},Kb={class:'modal-dialog modal-lg'},Wb={class:'modal-content'},zb={class:'modal-header bg-primary text-white'},Yb={class:'modal-body'},Jb={class:'mb-3'},Qb={class:'mb-3'},Xb={class:'row mb-3'},Zb={class:'col-md-6'},e_=['value'],t_={class:'col-md-6'},s_=['value'],n_={class:'row mb-3'},o_={class:'col-md-6'},i_={class:'col-md-6'},r_={class:'row mb-3'},l_={class:'col-md-6'},a_={class:'col-md-6'},c_={class:'row mb-3'},u_={class:'col-md-6'},d_={class:'col-md-6'},f_={class:'modal-footer'},h_=['disabled'],p_={key:0,class:'spinner-border spinner-border-sm me-2',role:'status','aria-hidden':'true'},g_={key:4,class:'modal-backdrop fade show'};function m_(t,e,s,n,o,i){return g(),m('div',null,[e[51]||(e[51]=a('h1',{class:'mb-4'},'Manage Timeslots',-1)),o.loading?(g(),m('div',Gy,e[18]||(e[18]=[a('div',{class:'spinner-border text-primary',role:'status'},[a('span',{class:'visually-hidden'},'Loading...')],-1),a('p',{class:'mt-2'},'Loading booking list details...',-1)]))):o.error?(g(),m('div',qy,T(o.error),1)):(g(),m('div',Hy,[a('div',Ky,[a('div',Wy,[a('h5',zy,T(o.bookingList.title),1),a('button',{class:'btn btn-light btn-sm',onClick:e[0]||(e[0]=r => o.showEditModal=!0)},'Edit')]),a('div',Yy,[o.bookingList.description?(g(),m('p',Jy,T(o.bookingList.description),1)):K('',!0),a('div',Qy,[a('div',Xy,[e[22]||(e[22]=a('h6',null,'Booking Details',-1)),a('ul',Zy,[a('li',eb,[e[19]||(e[19]=a('strong',null,'Examination Type:',-1)),i.getExaminationTypeName(o.bookingList.examinationTypeId)?(g(),m('span',tb,T(i.getExaminationTypeName(o.bookingList.examinationTypeId)),1)):(g(),m('span',sb,'Not specified'))]),a('li',nb,[e[20]||(e[20]=a('strong',null,'Default Location:',-1)),i.getLocationName(o.bookingList.locationId)?(g(),m('span',ob,T(i.getLocationName(o.bookingList.locationId)),1)):(g(),m('span',ib,'Not specified'))]),a('li',rb,[e[21]||(e[21]=a('strong',null,'Max Bookings:',-1)),z(' '+T(o.bookingList.maxBookingsPerStudent)+' per student/group ',1)])])]),a('div',lb,[e[26]||(e[26]=a('h6',null,'Time Constraints',-1)),a('ul',ab,[a('li',cb,[e[23]||(e[23]=a('strong',null,'Booking Period:',-1)),o.bookingList.bookingStartDate&&o.bookingList.bookingEndDate?(g(),m('span',ub,T(i.formatDate(o.bookingList.bookingStartDate))+' to '+T(i.formatDate(o.bookingList.bookingEndDate)),1)):o.bookingList.bookingStartDate?(g(),m('span',db,' From '+T(i.formatDate(o.bookingList.bookingStartDate)),1)):o.bookingList.bookingEndDate?(g(),m('span',fb,' Until '+T(i.formatDate(o.bookingList.bookingEndDate)),1)):(g(),m('span',hb,'No restrictions'))]),a('li',pb,[e[24]||(e[24]=a('strong',null,'Visibility Period:',-1)),o.bookingList.visibilityStartDate&&o.bookingList.visibilityEndDate?(g(),m('span',gb,T(i.formatDate(o.bookingList.visibilityStartDate))+' to '+T(i.formatDate(o.bookingList.visibilityEndDate)),1)):o.bookingList.visibilityStartDate?(g(),m('span',mb,' From '+T(i.formatDate(o.bookingList.visibilityStartDate)),1)):o.bookingList.visibilityEndDate?(g(),m('span',yb,' Until '+T(i.formatDate(o.bookingList.visibilityEndDate)),1)):(g(),m('span',bb,'Always visible'))]),a('li',_b,[e[25]||(e[25]=a('strong',null,'Cancellation Deadline:',-1)),o.bookingList.cancellationDeadline?(g(),m('span',vb,T(i.formatDate(o.bookingList.cancellationDeadline)),1)):(g(),m('span',kb,'No deadline'))])])])]),a('div',wb,[e[32]||(e[32]=a('h5',null,'Add Timeslot',-1)),a('form',{onSubmit:e[4]||(e[4]=Fe((...r) => i.addTimeslot&&i.addTimeslot(...r),['prevent'])),class:'row g-3'},[a('div',Eb,[e[27]||(e[27]=a('label',{for:'date',class:'form-label'},'Date',-1)),H(a('input',{id:'date','onUpdate:modelValue':e[1]||(e[1]=r => o.newTimeslot.date=r),type:'date',class:'form-control',required:''},null,512),[[J,o.newTimeslot.date]])]),a('div',xb,[e[28]||(e[28]=a('label',{for:'time',class:'form-label'},'Time',-1)),H(a('input',{id:'time','onUpdate:modelValue':e[2]||(e[2]=r => o.newTimeslot.time=r),type:'time',class:'form-control',required:''},null,512),[[J,o.newTimeslot.time]])]),a('div',Tb,[e[30]||(e[30]=a('label',{for:'location',class:'form-label'},'Location (Optional)',-1)),H(a('select',{id:'location','onUpdate:modelValue':e[3]||(e[3]=r => o.newTimeslot.locationId=r),class:'form-select'},[e[29]||(e[29]=a('option',{value:''},'Use default location',-1)),(g(!0),m(ae,null,_e(o.locations,r => (g(),m('option',{key:r.id,value:r.id},T(r.name),9,Sb))),128))],512),[[Zt,o.newTimeslot.locationId]])]),a('div',Lb,[a('button',{type:'submit',class:'btn btn-primary w-100',disabled:o.addingTimeslot},[o.addingTimeslot?(g(),m('span',Ab)):K('',!0),e[31]||(e[31]=z(' Add '))],8,Cb)])],32)]),a('div',Bb,[e[36]||(e[36]=a('h5',null,'Timeslots',-1)),o.timeslots.length===0?(g(),m('div',Rb,' No timeslots available for this booking list. ')):(g(),m('div',Db,[a('table',Ob,[e[35]||(e[35]=a('thead',null,[a('tr',null,[a('th',null,'Date'),a('th',null,'Time'),a('th',null,'Location'),a('th',null,'Status'),a('th',null,'Booked By'),a('th',null,'Actions')])],-1)),a('tbody',null,[(g(!0),m(ae,null,_e(o.timeslots,r => (g(),m('tr',{key:r.id,class:Ds({'table-secondary':r.booked||r.reserved})},[a('td',null,T(i.formatDate(r.date)),1),a('td',null,T(r.time),1),a('td',null,T(i.getLocationName(r.locationId)||i.getLocationName(o.bookingList.locationId)||'Not specified'),1),a('td',null,[r.booked&&r.cancelled?(g(),m('span',Pb,'Cancelled')):r.booked?(g(),m('span',Nb,'Booked')):r.reserved?(g(),m('span',Ib,'Reserved')):(g(),m('span',Mb,'Available'))]),a('td',null,[r.bookedByStudentId?(g(),m('span',Ub,[z(T(i.getStudentName(r.bookedByStudentId))+' ',1),r.bookedByGroupId?(g(),m('span',$b,'(Group: '+T(i.getGroupName(r.bookedByGroupId))+')',1)):K('',!0),r.cancelled&&r.cancelledById?(g(),m('span',jb,[e[33]||(e[33]=a('br',null,null,-1)),a('small',Fb,'Cancelled by: '+T(i.getStudentName(r.cancelledById)),1)])):K('',!0)])):(g(),m('span',Vb,'-'))]),a('td',null,[a('button',{class:'btn btn-sm btn-danger',onClick:l => i.deleteTimeslot(r.id),disabled:o.deletingTimeslot===r.id},[o.deletingTimeslot===r.id?(g(),m('span',qb)):K('',!0),e[34]||(e[34]=z(' Delete '))],8,Gb)])],2))),128))])])]))])])])])),o.showEditModal?(g(),m('div',Hb,[a('div',Kb,[a('div',Wb,[a('div',zb,[e[37]||(e[37]=a('h5',{class:'modal-title'},'Edit Booking List',-1)),a('button',{type:'button',class:'btn-close',onClick:e[5]||(e[5]=r => o.showEditModal=!1)})]),a('div',Yb,[a('form',{onSubmit:e[17]||(e[17]=Fe((...r) => i.updateBookingList&&i.updateBookingList(...r),['prevent']))},[a('div',Jb,[e[38]||(e[38]=a('label',{for:'editTitle',class:'form-label'},'Title *',-1)),H(a('input',{id:'editTitle','onUpdate:modelValue':e[6]||(e[6]=r => o.editedBookingList.title=r),type:'text',class:'form-control',required:''},null,512),[[J,o.editedBookingList.title]])]),a('div',Qb,[e[39]||(e[39]=a('label',{for:'editDescription',class:'form-label'},'Description',-1)),H(a('textarea',{id:'editDescription','onUpdate:modelValue':e[7]||(e[7]=r => o.editedBookingList.description=r),class:'form-control',rows:'3'},null,512),[[J,o.editedBookingList.description]])]),a('div',Xb,[a('div',Zb,[e[41]||(e[41]=a('label',{for:'editLocationType',class:'form-label'},'Default Location',-1)),H(a('select',{id:'editLocationType','onUpdate:modelValue':e[8]||(e[8]=r => o.editedBookingList.locationId=r),class:'form-select'},[e[40]||(e[40]=a('option',{value:''},'None',-1)),(g(!0),m(ae,null,_e(o.locations,r => (g(),m('option',{key:r.id,value:r.id},T(r.name),9,e_))),128))],512),[[Zt,o.editedBookingList.locationId]])]),a('div',t_,[e[43]||(e[43]=a('label',{for:'editExaminationType',class:'form-label'},'Examination Type',-1)),H(a('select',{id:'editExaminationType','onUpdate:modelValue':e[9]||(e[9]=r => o.editedBookingList.examinationTypeId=r),class:'form-select'},[e[42]||(e[42]=a('option',{value:''},'None',-1)),(g(!0),m(ae,null,_e(o.examinationTypes,r => (g(),m('option',{key:r.id,value:r.id},T(r.name),9,s_))),128))],512),[[Zt,o.editedBookingList.examinationTypeId]])])]),a('div',n_,[a('div',o_,[e[44]||(e[44]=a('label',{for:'editBookingStartDate',class:'form-label'},'Booking Start Date',-1)),H(a('input',{id:'editBookingStartDate','onUpdate:modelValue':e[10]||(e[10]=r => o.editedBookingList.bookingStartDate=r),type:'date',class:'form-control'},null,512),[[J,o.editedBookingList.bookingStartDate]])]),a('div',i_,[e[45]||(e[45]=a('label',{for:'editBookingEndDate',class:'form-label'},'Booking End Date',-1)),H(a('input',{id:'editBookingEndDate','onUpdate:modelValue':e[11]||(e[11]=r => o.editedBookingList.bookingEndDate=r),type:'date',class:'form-control'},null,512),[[J,o.editedBookingList.bookingEndDate]])])]),a('div',r_,[a('div',l_,[e[46]||(e[46]=a('label',{for:'editVisibilityStartDate',class:'form-label'},'Visibility Start Date',-1)),H(a('input',{id:'editVisibilityStartDate','onUpdate:modelValue':e[12]||(e[12]=r => o.editedBookingList.visibilityStartDate=r),type:'date',class:'form-control'},null,512),[[J,o.editedBookingList.visibilityStartDate]])]),a('div',a_,[e[47]||(e[47]=a('label',{for:'editVisibilityEndDate',class:'form-label'},'Visibility End Date',-1)),H(a('input',{id:'editVisibilityEndDate','onUpdate:modelValue':e[13]||(e[13]=r => o.editedBookingList.visibilityEndDate=r),type:'date',class:'form-control'},null,512),[[J,o.editedBookingList.visibilityEndDate]])])]),a('div',c_,[a('div',u_,[e[48]||(e[48]=a('label',{for:'editCancellationDeadline',class:'form-label'},'Cancellation Deadline',-1)),H(a('input',{id:'editCancellationDeadline','onUpdate:modelValue':e[14]||(e[14]=r => o.editedBookingList.cancellationDeadline=r),type:'date',class:'form-control'},null,512),[[J,o.editedBookingList.cancellationDeadline]])]),a('div',d_,[e[49]||(e[49]=a('label',{for:'editMaxBookings',class:'form-label'},'Max Bookings per Student/Group',-1)),H(a('input',{id:'editMaxBookings','onUpdate:modelValue':e[15]||(e[15]=r => o.editedBookingList.maxBookingsPerStudent=r),type:'number',min:'1',class:'form-control',required:''},null,512),[[J,o.editedBookingList.maxBookingsPerStudent,void 0,{number:!0}]])])]),a('div',f_,[a('button',{type:'button',class:'btn btn-secondary',onClick:e[16]||(e[16]=r => o.showEditModal=!1)},'Cancel'),a('button',{type:'submit',class:'btn btn-primary',disabled:o.updating},[o.updating?(g(),m('span',p_)):K('',!0),e[50]||(e[50]=z(' Update '))],8,h_)])],32)])])])])):K('',!0),o.showEditModal?(g(),m('div',g_)):K('',!0)]);}const y_=Ue(Vy,[['render',m_],['__scopeId','data-v-ebb627e4']]),b_=[{path:'/',redirect:'/booking-lists'},{path:'/login',component:Qf,meta:{requiresGuest:!0}},{path:'/register',component:fh,meta:{requiresGuest:!0}},{path:'/booking-lists',component:$h,meta:{requiresAuth:!0}},{path:'/booking-lists/:id',component:up,meta:{requiresAuth:!0}},{path:'/confirm-booking/:id',component:Rp,meta:{requiresAuth:!0}},{path:'/my-bookings',component:eg,meta:{requiresAuth:!0}},{path:'/groups',component:Sg,meta:{requiresAuth:!0}},{path:'/admin/login',component:$g,meta:{requiresGuest:!0}},{path:'/admin',component:Rm,meta:{requiresAdmin:!0}},{path:'/admin/booking-lists',component:Fy,meta:{requiresAdmin:!0}},{path:'/admin/booking-lists/:id',component:y_,meta:{requiresAdmin:!0}}],Cl=Mf({history:ff(),routes:b_});Cl.beforeEach((t,e,s) => {const n=ro.getters.isAuthenticated,o=ro.getters.isAdminAuthenticated;t.meta.requiresAuth&&!n?s('/login'):t.meta.requiresAdmin&&!o?s('/admin/login'):t.meta.requiresGuest&&n?s('/booking-lists'):s();});Jc(Pd).use(ro).use(Cl).mount('#app');

# Live Interaction Persistence - <PERSON><PERSON>tt Förklaring

## 🎯 Problemet vi löste

När användare var mitt i en reservation (på confirm booking sidan) och servern startade om, förlorade de sin plats i booking-processen och var tvungna att börja om från b<PERSON><PERSON>. Detta uppfyllde inte Grade A-kravet att "live application state överlever serveromstart".

## 📋 Vad vi implementerade

### 1. Database Schema
**Fil:** `server/src/db.js`

```sql
CREATE TABLE IF NOT EXISTS user_navigation_state (
  session_id TEXT PRIMARY KEY,           -- Användarens session ID
  current_page TEXT,                     -- Vilken sida användaren är på
  selected_timeslot_id INTEGER,          -- Vald timeslot
  booking_type TEXT,                     -- 'individual' eller 'group'
  selected_group_id INTEGER,             -- Vald grupp (om group booking)
  reservation_expires_at INTEGER,        -- När reservationen går ut
  created_at INTEGER NOT NULL,           -- <PERSON><PERSON><PERSON> tidpunkt
  updated_at INTEGER NOT NULL            -- Senast uppdaterad
);
```

### 2. Persistent Storage Manager
**Fil:** `server/src/utils/persistentStorage.js`

Utökade befintlig persistent storage med navigation state funktionalitet:

```javascript
class PersistentStorageManager {
  constructor() {
    this.userNavigationState = new Map(); // sessionId -> navigation state
  }

  // Spara navigation state
  async saveNavigationState(sessionId, state) {
    await db.run(`INSERT OR REPLACE INTO user_navigation_state ...`);
    this.userNavigationState.set(sessionId, state);
  }

  // Hämta navigation state
  async getNavigationState(sessionId) {
    return this.userNavigationState.get(sessionId);
  }

  // Ta bort navigation state
  async removeNavigationState(sessionId) {
    await db.run('DELETE FROM user_navigation_state WHERE session_id = ?');
    this.userNavigationState.delete(sessionId);
  }
}
```

### 3. API Controller
**Fil:** `server/src/controllers/navigation.controller.js`

Skapade REST API endpoints för navigation state:

```javascript
// POST /api/navigation-state - Spara navigation state
router.post('/navigation-state', async (req, res) => {
  const navigationState = {
    currentPage: req.body.currentPage,
    selectedTimeslotId: req.body.selectedTimeslotId,
    bookingType: req.body.bookingType,
    selectedGroupId: req.body.selectedGroupId,
    reservationExpiresAt: req.body.reservationExpiresAt
  };
  
  await model.persistentStorage.saveNavigationState(sessionId, navigationState);
});

// GET /api/navigation-state - Hämta navigation state
// DELETE /api/navigation-state - Ta bort navigation state
```

### 4. Vue Navigation Tracking Mixin
**Fil:** `client/src/mixins/navigationTracking.js`

Skapade återanvändbar Vue mixin för navigation tracking:

```javascript
export default {
  mounted() {
    this.trackPageEntry(); // Spara när användaren kommer till sidan
  },

  beforeUnmount() {
    this.trackPageExit(); // Rensa när användaren lämnar
  },

  methods: {
    // Spara navigation state
    async trackPageEntry() {
      const navigationState = {
        currentPage: this.$route.path,
        selectedTimeslotId: this.$route.params.id,
        bookingType: this.bookingType,
        selectedGroupId: this.selectedGroupId,
        reservationExpiresAt: this.timeslot?.reservedUntil
      };
      
      await this.saveNavigationState(navigationState);
    },

    // Återställ navigation state efter serveromstart
    async restoreNavigationState() {
      const state = await fetch('/api/navigation-state');
      if (state.selectedTimeslotId) {
        await this.restoreBookingState(state);
      }
    }
  }
}
```

### 5. ConfirmBooking Component Integration
**Fil:** `client/src/views/ConfirmBooking.vue`

Integrerade navigation tracking i confirm booking komponenten:

```javascript
export default {
  mixins: [navigationTracking],

  async created() {
    // Försök återställa state först (vid serveromstart)
    await this.restoreNavigationState();
    
    // Sedan ladda timeslot data som vanligt
    const timeslotId = this.$route.params.id;
    // ...
  },

  watch: {
    // Spara när användaren ändrar booking type eller grupp
    bookingType(newValue) {
      this.updateNavigationState({
        bookingType: newValue,
        selectedGroupId: this.selectedGroupId
      });
    }
  }
}
```

## ⚙️ Hur det fungerar

### Steg 1: Användaren startar en reservation
```javascript
// När användaren klickar "Reserve" på en timeslot
reserveTimeslot(timeslotId) {
  // API call reserverar timeslot för 10 sekunder
  fetch(`/api/timeslots/${timeslotId}/reserve`, { method: 'POST' });
  
  // Användaren redirectas till /confirm-booking/123
  this.$router.push(`/confirm-booking/${timeslotId}`);
}
```

### Steg 2: Navigation state sparas automatiskt
```javascript
// ConfirmBooking komponenten mountas
mounted() {
  this.trackPageEntry(); // Sparar navigation state
}

trackPageEntry() {
  const state = {
    currentPage: '/confirm-booking/123',
    selectedTimeslotId: 123,
    bookingType: 'individual',
    reservationExpiresAt: 1640995200000 // 10 sekunder från nu
  };
  
  // POST /api/navigation-state
  await this.saveNavigationState(state);
}
```

### Steg 3: Användaren ändrar inställningar
```javascript
// När användaren väljer "Group booking"
watch: {
  bookingType(newValue) {
    // Uppdatera navigation state
    this.updateNavigationState({
      bookingType: 'group',
      selectedGroupId: this.selectedGroupId
    });
  }
}
```

### Steg 4: Server startar om
```bash
# Server stängs av
^C Shutting down server...

# Server startas igen
node src/index.js

# Navigation states laddas från databas
🔄 Persistent data loaded:
   🧭 1 navigation states
```

### Steg 5: Användaren laddar om sidan
```javascript
// ConfirmBooking komponenten skapas igen
async created() {
  // Försök återställa navigation state
  await this.restoreNavigationState();
}

async restoreNavigationState() {
  // GET /api/navigation-state
  const response = await fetch('/api/navigation-state');
  const state = await response.json();
  
  if (state.selectedTimeslotId) {
    // Hämta timeslot data
    const timeslot = await fetch(`/api/timeslots/${state.selectedTimeslotId}`);
    
    // Kontrollera om reservation fortfarande är giltig
    if (timeslot.reservedUntil > Date.now()) {
      // Återställ komponent state
      this.timeslot = timeslot;
      this.bookingType = state.bookingType;
      this.selectedGroupId = state.selectedGroupId;
      this.startCountdown(); // Starta countdown igen
    } else {
      // Reservation har gått ut, redirect tillbaka
      this.$router.push(`/booking-lists/${timeslot.bookingListId}`);
    }
  }
}
```

## 🎯 Resultat

### Före implementationen:
1. Användare reserverar timeslot → Kommer till confirm booking
2. Server startar om → Användare förlorar sin plats
3. Måste börja om från början

### Efter implementationen:
1. Användare reserverar timeslot → Kommer till confirm booking
2. Navigation state sparas automatiskt i databas
3. Server startar om → Navigation state laddas från databas
4. Användare laddar om sidan → Kommer tillbaka till confirm booking med samma inställningar
5. Countdown fortsätter från korrekt tid

### ✅ Vad som bevaras:
- **Vilken sida** användaren var på (`/confirm-booking/123`)
- **Vald timeslot** (ID 123)
- **Booking type** ('individual' eller 'group')
- **Vald grupp** (om group booking)
- **Reservation timeout** (när reservationen går ut)

### 🔄 Smart validering:
- Kontrollerar om reservation fortfarande är giltig
- Om reservationen gått ut → redirect till booking list
- Om reservationen är aktiv → återställ exakt state

## 🏆 Grade A Krav Uppfyllt

Nu överlever ALL live application state serveromstart:

1. **User sessions** - Inloggning och användardata ✅
2. **Form data** - Ifylld data i formulär ✅
3. **Live interactions** - Pågående reservationer och booking flow ✅
4. **Navigation state** - Var användaren befinner sig i processen ✅
5. **Booking selections** - Vald timeslot, booking type, grupp ✅

**Användare kan nu vara mitt i en reservation, servern kan starta om, och de kommer tillbaka till exakt samma ställe med samma inställningar och aktiv countdown!** 🎉

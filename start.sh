#!/bin/bash

# start.sh - <PERSON><PERSON><PERSON> to start both client and server

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# Function to handle cleanup on exit
cleanup() {
  echo "Shutting down server..."
  kill $SERVER_PID 2>/dev/null
  exit 0
}

# Set up trap to catch Ctrl+C
trap cleanup INT

echo "Starting booking system..."

# Check if node_modules exist in client and server, install if not
if [ ! -d "$SCRIPT_DIR/client/node_modules" ]; then
  echo "Installing client dependencies..."
  cd "$SCRIPT_DIR/client" && npm install
  cd "$SCRIPT_DIR"
fi

if [ ! -d "$SCRIPT_DIR/server/node_modules" ]; then
  echo "Installing server dependencies..."
  cd "$SCRIPT_DIR/server" && npm install
  cd "$SCRIPT_DIR"
fi

# Build the client first to ensure dist directory exists
echo "Building client..."
cd "$SCRIPT_DIR/client"
if command -v npm >/dev/null 2>&1; then
  npm run build 2>/dev/null || echo "Client build failed, using existing dist"
else
  echo "npm not found, using existing dist"
fi
cd "$SCRIPT_DIR"

# Start the server
echo "Starting server..."
cd "$SCRIPT_DIR/server" && node src/index.js &
SERVER_PID=$!

# Wait a moment for the server to initialize
sleep 3

echo "Server started successfully!"
echo "- Server HTTP: http://localhost:8989"
echo "- Server HTTPS: https://localhost:8990"

echo ""
echo "Press Ctrl+C to stop the server."

# Wait for server process
wait $SERVER_PID

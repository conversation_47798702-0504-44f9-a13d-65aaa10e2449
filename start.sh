#!/bin/bash

# start.sh - <PERSON><PERSON>t to start both client and server

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# Function to handle cleanup on exit
cleanup() {
  echo "Shutting down servers..."
  kill $CLIENT_PID $SERVER_PID 2>/dev/null
  exit 0
}

# Set up trap to catch Ctrl+C
trap cleanup INT

echo "Starting booking system..."

# Check if node_modules exist in client and server, install if not
if [ ! -d "$SCRIPT_DIR/client/node_modules" ]; then
  echo "Installing client dependencies..."
  cd "$SCRIPT_DIR/client" && npm install
  cd "$SCRIPT_DIR"
fi

if [ ! -d "$SCRIPT_DIR/server/node_modules" ]; then
  echo "Installing server dependencies..."
  cd "$SCRIPT_DIR/server" && npm install
  cd "$SCRIPT_DIR"
fi

# Build the client first to ensure dist directory exists
echo "Building client..."
cd "$SCRIPT_DIR/client" && npm run build
cd "$SCRIPT_DIR"

# Start the server
echo "Starting server..."
cd "$SCRIPT_DIR/server" && npm run dev &
SERVER_PID=$!

# Wait a moment for the server to initialize
sleep 2

# Start the client in development mode
echo "Starting client development server..."
cd "$SCRIPT_DIR/client" && npm run dev &
CLIENT_PID=$!

echo "Booking system is running!"
echo "- Server API: http://localhost:8989/api"
echo "- Client development server: http://localhost:5173"
echo ""
echo "Press Ctrl+C to stop both servers."

# Wait for both processes
wait $CLIENT_PID $SERVER_PID

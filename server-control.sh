#!/bin/bash

# server-control.sh - Enkel server-kontroll för Grade A-testning

case "$1" in
  start)
    echo "🚀 Starting booking server..."
    cd "$(dirname "$0")/server"
    node src/index.js
    ;;
  stop)
    echo "🛑 Stopping booking server..."
    pkill -f "node src/index.js" 2>/dev/null || true
    echo "✅ Server stopped"
    ;;
  restart)
    echo "🔄 Restarting booking server..."
    pkill -f "node src/index.js" 2>/dev/null || true
    sleep 2
    cd "$(dirname "$0")/server"
    node src/index.js &
    echo "✅ Server restarted"
    ;;
  status)
    echo "📊 Checking server status..."
    if pgrep -f "node src/index.js" > /dev/null; then
      echo "✅ Server is running"
      echo "📍 HTTP:  http://localhost:8989"
      echo "📍 HTTPS: https://localhost:8990"
    else
      echo "❌ Server is not running"
    fi
    ;;
  *)
    echo "Usage: $0 {start|stop|restart|status}"
    echo ""
    echo "Commands:"
    echo "  start   - Start the booking server"
    echo "  stop    - Stop the booking server"
    echo "  restart - Restart the booking server"
    echo "  status  - Check if server is running"
    exit 1
    ;;
esac

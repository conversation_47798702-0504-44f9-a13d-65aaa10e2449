/**
 * setup-test-data.js
 *
 * This script sets up test data for the booking system:
 * - Creates test student accounts (<PERSON> and <PERSON>)
 * - Creates a booking list with time constraints
 * - Adds several available time slots for testing
 */

// Use the server's database module directly
import { spawn } from 'child_process';

function setupTestData() {
  // eslint-disable-next-line no-console
  console.log('Setting up test data for the booking system...');

  // Run the setup script in the server directory
  const setupProcess = spawn('node', ['./server/setup-test-data.js'], {
    stdio: 'inherit',
    shell: true
  });

  setupProcess.on('close', (code) => {
    if (code === 0) {
      // eslint-disable-next-line no-console
      console.log('Test data setup completed successfully!');
    } else {
      // eslint-disable-next-line no-console
      console.error(`Test data setup failed with code ${code}`);
    }
  });
}

setupTestData();

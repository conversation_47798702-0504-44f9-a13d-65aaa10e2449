#!/bin/bash

# restart-server.sh - <PERSON><PERSON><PERSON><PERSON> script för att starta om servern

echo "🔄 Restarting booking system server..."

# Hitta och döda befintliga server-processer
echo "Stopping existing server processes..."
pkill -f "node src/index.js" 2>/dev/null || true
pkill -f "nodemon src/index.js" 2>/dev/null || true

# Vänta lite för att processer ska stängas ner
sleep 2

# Gå till server-katalogen
cd "$(dirname "$0")/server"

# Starta servern
echo "Starting server..."
node src/index.js &

# Visa process-ID
SERVER_PID=$!
echo "✅ Server started with PID: $SERVER_PID"
echo "📍 HTTP:  http://localhost:8989"
echo "📍 HTTPS: https://localhost:8990"
echo ""
echo "💡 Tips: Använd 'kill $SERVER_PID' för att stoppa servern"
echo "💡 Tips: Använd './restart-server.sh' för att starta om igen"

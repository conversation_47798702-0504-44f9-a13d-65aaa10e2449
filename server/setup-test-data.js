/**
 * setup-test-data.js
 *
 * This script sets up test data for the booking system:
 * - Creates test student accounts (<PERSON> and <PERSON>)
 * - Creates a booking list with time constraints
 * - Adds several available time slots for testing
 */

import bcrypt from 'bcrypt';
import { initDB, getDB } from './src/db.js';

async function setupTestData() {
  try {
    console.log('Initializing database...');
    await initDB();
    const db = getDB();

    // Create test student accounts
    console.log('Creating test student accounts...');

    // Check if <PERSON> already exists
    const kevinExists = await db.get('SELECT * FROM students WHERE username = ?', ['kevinlam']);
    if (!kevinExists) {
      const kevinPassword = await bcrypt.hash('password123', 10);
      await db.run(
        'INSERT INTO students (username, password, email) VALUES (?, ?, ?)',
        ['kevinlam', kevinPassword, '<EMAIL>']
      );
      console.log('Created student account: <PERSON>');
    } else {
      console.log('Student account <PERSON> already exists');
    }

    // Check if <PERSON> already exists
    const allanExists = await db.get('SELECT * FROM students WHERE username = ?', ['allaninma']);
    if (!allanExists) {
      const allanPassword = await bcrypt.hash('password123', 10);
      await db.run(
        'INSERT INTO students (username, password, email) VALUES (?, ?, ?)',
        ['allaninma', allanPassword, '<EMAIL>']
      );
      console.log('Created student account: Allan Inma');
    } else {
      console.log('Student account Allan Inma already exists');
    }

    // Create a booking list for testing
    console.log('Creating test booking list...');

    // Get current date and future dates for constraints
    const today = new Date();
    const oneMonthLater = new Date(today);
    oneMonthLater.setMonth(today.getMonth() + 1);

    const formatDate = (date) => {
      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    };

    const todayFormatted = formatDate(today);
    const oneMonthLaterFormatted = formatDate(oneMonthLater);

    // Check if test booking list already exists
    const bookingListExists = await db.get('SELECT * FROM booking_lists WHERE title = ?', ['Test Booking List']);
    let bookingListId;

    if (!bookingListExists) {
      const result = await db.run(
        `INSERT INTO booking_lists (
          title, description, location_id, examination_type_id,
          booking_start_date, booking_end_date,
          visibility_start_date, visibility_end_date,
          cancellation_deadline, max_bookings_per_student
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'Test Booking List',
          'Sample booking list for testing purposes',
          1, // Room A
          1, // Lab Presentation
          todayFormatted, // Booking starts today
          oneMonthLaterFormatted, // Booking ends in one month
          todayFormatted, // Visible from today
          oneMonthLaterFormatted, // Visible until one month later
          oneMonthLaterFormatted, // Can cancel until one month later
          1 // Max 1 booking per student
        ]
      );
      bookingListId = result.lastID;
      console.log(`Created test booking list with ID: ${bookingListId}`);
    } else {
      bookingListId = bookingListExists.id;
      console.log(`Using existing test booking list with ID: ${bookingListId}`);
    }

    // Delete existing timeslots for this booking list
    console.log('Deleting existing timeslots...');
    await db.run('DELETE FROM timeslots WHERE booking_list_id = ?', [bookingListId]);
    console.log('Existing timeslots deleted');

    // Create test timeslots
    console.log('Creating test timeslots...');

    // Generate dates for the next 3 days
    const dates = [];
    for (let i = 1; i <= 3; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(formatDate(date));
    }

    // Times for each day (reduced number of slots)
    const times = ['10:00', '13:00', '15:00'];

    // Create timeslots
    let createdCount = 0;
    for (const date of dates) {
      for (const time of times) {
        await db.run(
          `INSERT INTO timeslots (
            booking_list_id, date, time, location_id,
            booked, booked_by_student_id, booked_by_group_id,
            reserved_until, cancelled
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            bookingListId,
            date,
            time,
            1, // Room A
            0, // Not booked
            null, // No student
            null, // No group
            null, // Not reserved
            0 // Not cancelled
          ]
        );
        createdCount++;
      }
    }

    console.log(`Created ${createdCount} new timeslots`);
    console.log('Test data setup complete!');

    // Display login information
    console.log('\nTest Account Information:');
    console.log('1. Username: kevinlam, Password: password123');
    console.log('2. Username: allaninma, Password: password123');
    console.log('3. Admin: Username: admin1, Password: admin123');

  } catch (error) {
    console.error('Error setting up test data:', error);
    process.exit(1);
  }
}

setupTestData();

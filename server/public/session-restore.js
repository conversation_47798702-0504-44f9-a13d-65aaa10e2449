/**
 * session-restore.js - Client-side session restoration after server restart
 * 
 * This script automatically checks if the user is still authenticated
 * after a server restart and refreshes the page if needed
 */

(function() {
  'use strict';

  let reconnectAttempts = 0;
  const maxReconnectAttempts = 10;
  const reconnectInterval = 500; // 0.5 seconds - much faster

  /**
   * Check if user is authenticated
   */
  async function checkAuthentication() {
    try {
      const response = await fetch('/api/users/me', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        return data.authenticated;
      }
      return false;
    } catch (error) {
      console.log('Authentication check failed:', error.message);
      return false;
    }
  }

  /**
   * Handle server reconnection
   */
  async function handleReconnection() {
    console.log('🔄 Checking server connection...');
    
    try {
      // Check if server is back online
      const response = await fetch('/api/users/me', {
        method: 'GET',
        credentials: 'include'
      });

      if (response.ok) {
        console.log('✅ Server is back online');
        
        // Check if user is still authenticated
        const data = await response.json();
        if (data.authenticated) {
          console.log('✅ User is still authenticated, refreshing page...');
          // Quick refresh - only 500ms delay
          setTimeout(() => {
            window.location.reload();
          }, 500);
        } else {
          console.log('❌ User is not authenticated, redirecting to login...');
          window.location.href = '/';
        }
        return true;
      }
    } catch (error) {
      console.log('Server still offline:', error.message);
    }
    
    return false;
  }

  /**
   * Start reconnection attempts
   */
  function startReconnectionAttempts() {
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.log('❌ Max reconnection attempts reached');
      return;
    }

    reconnectAttempts++;
    console.log(`🔄 Reconnection attempt ${reconnectAttempts}/${maxReconnectAttempts}`);

    handleReconnection().then(success => {
      if (!success) {
        // Try again after interval
        setTimeout(startReconnectionAttempts, reconnectInterval);
      }
    });
  }

  /**
   * Monitor server connection
   */
  function monitorConnection() {
    // Check every 10 seconds if we're still connected (faster monitoring)
    setInterval(async () => {
      const isAuthenticated = await checkAuthentication();
      if (!isAuthenticated && window.location.pathname !== '/') {
        console.log('⚠️ Lost authentication, starting reconnection...');
        startReconnectionAttempts();
      }
    }, 10000);
  }

  /**
   * Handle page visibility change (when user comes back to tab)
   */
  function handleVisibilityChange() {
    if (!document.hidden) {
      // User came back to tab, check authentication
      setTimeout(async () => {
        const isAuthenticated = await checkAuthentication();
        if (!isAuthenticated && window.location.pathname !== '/') {
          console.log('⚠️ Authentication lost while tab was hidden');
          startReconnectionAttempts();
        }
      }, 1000);
    }
  }

  /**
   * Initialize session restoration
   */
  function init() {
    console.log('🔧 Session restoration initialized');
    
    // Monitor connection
    monitorConnection();
    
    // Handle visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Handle page load - check if we need to restore session
    window.addEventListener('load', async () => {
      // Quick check after page load
      setTimeout(async () => {
        const isAuthenticated = await checkAuthentication();
        if (!isAuthenticated && window.location.pathname !== '/' && window.location.pathname !== '/admin') {
          console.log('⚠️ Not authenticated on protected page, checking for restoration...');
          startReconnectionAttempts();
        }
      }, 1000);
    });
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();

/**
 * create-current-booking-list.js
 *
 * This script creates a new booking list with current dates and adds timeslots for it.
 */

import { initDB, getDB } from './src/db.js';

async function createCurrentBookingList() {
  try {
    console.log('Initializing database...');
    await initDB();
    const db = getDB();

    // Create a new booking list with current dates
    console.log('Creating new booking list with current dates...');

    // Get current date and future dates
    const today = new Date();
    const oneMonthLater = new Date(today);
    oneMonthLater.setMonth(today.getMonth() + 1);

    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`; // Format as YYYY-MM-DD
    };

    const todayFormatted = formatDate(today);
    const oneMonthLaterFormatted = formatDate(oneMonthLater);

    // Check if "Current Booking List" already exists
    const existingList = await db.get('SELECT * FROM booking_lists WHERE title = ?', ['Current Booking List']);

    let bookingListId;
    if (existingList) {
      bookingListId = existingList.id;
      console.log(`Using existing booking list with ID: ${bookingListId}`);

      // Update the dates to make sure it's current
      await db.run(
        `UPDATE booking_lists SET
          booking_start_date = ?,
          booking_end_date = ?,
          visibility_start_date = ?,
          visibility_end_date = ?,
          cancellation_deadline = ?
        WHERE id = ?`,
        [
          todayFormatted,
          oneMonthLaterFormatted,
          todayFormatted,
          oneMonthLaterFormatted,
          oneMonthLaterFormatted,
          bookingListId
        ]
      );
      console.log('Updated booking list dates');
    } else {
      // Create a new booking list
      const result = await db.run(
        `INSERT INTO booking_lists (
          title, description, location_id, examination_type_id,
          booking_start_date, booking_end_date,
          visibility_start_date, visibility_end_date,
          cancellation_deadline, max_bookings_per_student
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'Current Booking List',
          'Booking list with current dates for testing',
          1, // Room A
          1, // Lab Presentation
          todayFormatted,
          oneMonthLaterFormatted,
          todayFormatted,
          oneMonthLaterFormatted,
          oneMonthLaterFormatted,
          1 // Max 1 booking per student
        ]
      );
      bookingListId = result.lastID;
      console.log(`Created new booking list with ID: ${bookingListId}`);
    }

    // Create timeslots for the next 3 days
    console.log('Creating timeslots...');

    // Generate dates for the next 2 days
    const dates = [];
    for (let i = 1; i <= 2; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(formatDate(date));
    }

    // Times for each day (reduced number of slots)
    const times = ['10:00', '13:00', '15:00'];

    // Delete existing timeslots for this booking list
    console.log('Deleting existing timeslots...');
    await db.run('DELETE FROM timeslots WHERE booking_list_id = ?', [bookingListId]);
    console.log('Existing timeslots deleted');

    // Create timeslots
    let createdCount = 0;
    for (const date of dates) {
      for (const time of times) {
        await db.run(
          `INSERT INTO timeslots (
            booking_list_id, date, time, location_id,
            booked, booked_by_student_id, booked_by_group_id,
            reserved_until, cancelled
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            bookingListId,
            date,
            time,
            1, // Room A
            0, // Not booked
            null, // No student
            null, // No group
            null, // Not reserved
            0 // Not cancelled
          ]
        );
        createdCount++;
      }
    }

    console.log(`Created ${createdCount} new timeslots`);

    // Verify the booking list and timeslots
    const bookingList = await db.get('SELECT * FROM booking_lists WHERE id = ?', [bookingListId]);
    const timeslotCount = await db.get('SELECT COUNT(*) as count FROM timeslots WHERE booking_list_id = ?', [bookingListId]);

    console.log('\nBooking List Details:');
    console.log(`Title: ${bookingList.title}`);
    console.log(`Booking Period: ${bookingList.booking_start_date} to ${bookingList.booking_end_date}`);
    console.log(`Visibility Period: ${bookingList.visibility_start_date} to ${bookingList.visibility_end_date}`);
    console.log(`Timeslots: ${timeslotCount.count}`);

    console.log('\nTest Account Information:');
    console.log('1. Username: kevinlam, Password: password123');
    console.log('2. Username: allaninma, Password: password123');
    console.log('3. Admin: Username: admin1, Password: admin123');

  } catch (error) {
    console.error('Error creating booking list:', error);
  }
}

createCurrentBookingList();

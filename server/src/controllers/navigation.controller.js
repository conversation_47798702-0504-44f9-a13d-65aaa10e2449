/**
 * navigation.controller.js - Navigation State Controller
 *
 * Handles saving and restoring user navigation state for live interactions
 */

import { Router } from 'express';
import model from '../model.js';

const router = Router();

/**
 * Save user navigation state
 */
router.post('/navigation-state', async (req, res) => {
  try {
    const { id: sessionId } = req.session;
    
    if (!sessionId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const navigationState = {
      currentPage: req.body.currentPage,
      selectedTimeslotId: req.body.selectedTimeslotId || null,
      bookingType: req.body.bookingType || null,
      selectedGroupId: req.body.selectedGroupId || null,
      reservationExpiresAt: req.body.reservationExpiresAt || null,
      createdAt: req.body.timestamp || Date.now()
    };

    // Save to persistent storage
    if (model.persistentStorage) {
      await model.persistentStorage.saveNavigationState(sessionId, navigationState);
    }

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error saving navigation state:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get user navigation state
 */
router.get('/navigation-state', async (req, res) => {
  try {
    const { id: sessionId } = req.session;
    
    if (!sessionId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Get from persistent storage
    let navigationState = null;
    if (model.persistentStorage) {
      navigationState = await model.persistentStorage.getNavigationState(sessionId);
    }

    if (navigationState) {
      res.status(200).json(navigationState);
    } else {
      res.status(404).json({ error: 'No navigation state found' });
    }
  } catch (error) {
    console.error('Error getting navigation state:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Clear user navigation state
 */
router.delete('/navigation-state', async (req, res) => {
  try {
    const { id: sessionId } = req.session;
    
    if (!sessionId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Remove from persistent storage
    if (model.persistentStorage) {
      await model.persistentStorage.removeNavigationState(sessionId);
    }

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error clearing navigation state:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;

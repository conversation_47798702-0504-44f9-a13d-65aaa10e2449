/**
 * group.controller.js - Group Controller
 *
 * This controller handles all group-related operations:
 * - Creating groups
 * - Managing group members
 * - Getting group information
 */

import { Router } from 'express';
import { getDB } from '../db.js';
import model from '../model.js';
import {
  validateGroupCreation,
  validateIdParam,
  handleValidationErrors,
  sanitizeHtml,
  escapeHtml
} from '../utils/validation.js';
import Group from '../models/group.model.js';

const router = Router();

/**
 * Get all groups for the current student
 */
router.get('/groups', async (req, res) => {
  const { studentId } = req.session;

  if (!studentId) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    const db = getDB();
    
    // Get all groups the student is a member of
    const groups = await db.all(
      `SELECT g.* FROM groups g
       JOIN group_members gm ON g.id = gm.group_id
       WHERE gm.student_id = ?`,
      [studentId]
    );
    
    // Get members for each group
    for (const group of groups) {
      const members = await db.all(
        `SELECT s.id, s.username, s.email FROM students s
         JOIN group_members gm ON s.id = gm.student_id
         WHERE gm.group_id = ?`,
        [group.id]
      );
      
      group.members = members;
    }
    
    res.status(200).json({ groups });
  } catch (error) {
    console.error('Error getting groups:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create a new group
 */
router.post('/groups', async (req, res) => {
  const { name } = req.body;
  const { studentId } = req.session;

  if (!studentId) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  if (!name) {
    return res.status(400).json({ error: 'Group name is required' });
  }

  try {
    const db = getDB();
    
    // Create the group
    const result = await db.run(
      'INSERT INTO groups (name) VALUES (?)',
      [name]
    );
    
    const groupId = result.lastID;
    
    // Add the current student as a member
    await db.run(
      'INSERT INTO group_members (group_id, student_id) VALUES (?, ?)',
      [groupId, studentId]
    );
    
    // Create the group in memory
    const group = new Group(groupId, name);
    group.addMember(studentId);
    model.groups[groupId] = group;
    
    // Add the group to the student's groups
    const student = model.findStudentById(studentId);
    if (student) {
      student.addGroup(groupId);
    }
    
    // Get the group with members
    const groupWithMembers = await db.get(
      `SELECT g.* FROM groups g
       WHERE g.id = ?`,
      [groupId]
    );
    
    const members = await db.all(
      `SELECT s.id, s.username, s.email FROM students s
       JOIN group_members gm ON s.id = gm.student_id
       WHERE gm.group_id = ?`,
      [groupId]
    );
    
    groupWithMembers.members = members;
    
    res.status(201).json(groupWithMembers);
  } catch (error) {
    console.error('Error creating group:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Add a member to a group
 */
router.post('/groups/:id/members', async (req, res) => {
  const { id } = req.params;
  const { username } = req.body;
  const { studentId } = req.session;
  const groupId = Number(id);

  if (!studentId) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  if (isNaN(groupId)) {
    return res.status(400).json({ error: 'Invalid group ID' });
  }

  if (!username) {
    return res.status(400).json({ error: 'Username is required' });
  }

  try {
    const db = getDB();
    
    // Check if the group exists
    const group = await db.get('SELECT * FROM groups WHERE id = ?', [groupId]);
    if (!group) {
      return res.status(404).json({ error: 'Group not found' });
    }
    
    // Check if the current student is a member of the group
    const isMember = await db.get(
      'SELECT * FROM group_members WHERE group_id = ? AND student_id = ?',
      [groupId, studentId]
    );
    
    if (!isMember) {
      return res.status(403).json({ error: 'You are not a member of this group' });
    }
    
    // Find the student to add
    const studentToAdd = await db.get(
      'SELECT * FROM students WHERE username = ?',
      [username]
    );
    
    if (!studentToAdd) {
      return res.status(404).json({ error: 'Student not found' });
    }
    
    // Check if the student is already a member
    const isAlreadyMember = await db.get(
      'SELECT * FROM group_members WHERE group_id = ? AND student_id = ?',
      [groupId, studentToAdd.id]
    );
    
    if (isAlreadyMember) {
      return res.status(400).json({ error: 'Student is already a member of this group' });
    }
    
    // Add the student to the group
    await db.run(
      'INSERT INTO group_members (group_id, student_id) VALUES (?, ?)',
      [groupId, studentToAdd.id]
    );
    
    // Update in-memory models
    const groupModel = model.findGroupById(groupId);
    if (groupModel) {
      groupModel.addMember(studentToAdd.id);
    }
    
    const studentModel = model.findStudentById(studentToAdd.id);
    if (studentModel) {
      studentModel.addGroup(groupId);
    }
    
    // Get the updated group with members
    const updatedGroup = await db.get(
      `SELECT g.* FROM groups g
       WHERE g.id = ?`,
      [groupId]
    );
    
    const members = await db.all(
      `SELECT s.id, s.username, s.email FROM students s
       JOIN group_members gm ON s.id = gm.student_id
       WHERE gm.group_id = ?`,
      [groupId]
    );
    
    updatedGroup.members = members;
    
    res.status(200).json(updatedGroup);
  } catch (error) {
    console.error('Error adding group member:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Remove a member from a group
 */
router.delete('/groups/:groupId/members/:memberId', async (req, res) => {
  const { groupId, memberId } = req.params;
  const { studentId } = req.session;
  const groupIdNum = Number(groupId);
  const memberIdNum = Number(memberId);

  if (!studentId) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  if (isNaN(groupIdNum) || isNaN(memberIdNum)) {
    return res.status(400).json({ error: 'Invalid group or member ID' });
  }

  try {
    const db = getDB();
    
    // Check if the group exists
    const group = await db.get('SELECT * FROM groups WHERE id = ?', [groupIdNum]);
    if (!group) {
      return res.status(404).json({ error: 'Group not found' });
    }
    
    // Check if the current student is a member of the group
    const isMember = await db.get(
      'SELECT * FROM group_members WHERE group_id = ? AND student_id = ?',
      [groupIdNum, studentId]
    );
    
    if (!isMember) {
      return res.status(403).json({ error: 'You are not a member of this group' });
    }
    
    // Check if the member to remove exists in the group
    const memberExists = await db.get(
      'SELECT * FROM group_members WHERE group_id = ? AND student_id = ?',
      [groupIdNum, memberIdNum]
    );
    
    if (!memberExists) {
      return res.status(404).json({ error: 'Member not found in this group' });
    }
    
    // Remove the member from the group
    await db.run(
      'DELETE FROM group_members WHERE group_id = ? AND student_id = ?',
      [groupIdNum, memberIdNum]
    );
    
    // Update in-memory models
    const groupModel = model.findGroupById(groupIdNum);
    if (groupModel) {
      groupModel.removeMember(memberIdNum);
    }
    
    const studentModel = model.findStudentById(memberIdNum);
    if (studentModel) {
      studentModel.removeGroup(groupIdNum);
    }
    
    // Check if the group is now empty
    const remainingMembers = await db.get(
      'SELECT COUNT(*) as count FROM group_members WHERE group_id = ?',
      [groupIdNum]
    );
    
    if (remainingMembers.count === 0) {
      // Delete the empty group
      await db.run('DELETE FROM groups WHERE id = ?', [groupIdNum]);
      
      // Remove from in-memory model
      delete model.groups[groupIdNum];
      
      res.status(200).json({ message: 'Member removed and empty group deleted' });
    } else {
      // Get the updated group with members
      const updatedGroup = await db.get(
        `SELECT g.* FROM groups g
         WHERE g.id = ?`,
        [groupIdNum]
      );
      
      const members = await db.all(
        `SELECT s.id, s.username, s.email FROM students s
         JOIN group_members gm ON s.id = gm.student_id
         WHERE gm.group_id = ?`,
        [groupIdNum]
      );
      
      updatedGroup.members = members;
      
      res.status(200).json(updatedGroup);
    }
  } catch (error) {
    console.error('Error removing group member:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Export the router
export default { router };

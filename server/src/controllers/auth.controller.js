/**
 * auth.controller.js - Authentication Controller
 *
 * This controller handles all authentication-related operations:
 * - Login (creating a new user session)
 * - Logout (destroying the session)
 * - Registration (creating a new student account)
 * - Checking authentication status
 * - Protecting routes with authentication middleware
 */

import { Router } from 'express';
import { getDB } from '../db.js';
import model from '../model.js';
import bcrypt from 'bcrypt';
import {
  validateRegistration,
  validateLogin,
  handleValidationErrors
  // escapeHtml // Unused for now - handled by middleware
} from '../utils/validation.js';

const router = Router();

/**
 * Authentication middleware for protecting routes
 *
 * This middleware:
 * 1. Checks if the request has a valid session with a user
 * 2. If authenticated, updates the user's activity timestamp and allows the request
 * 3. If not authenticated, returns a 401 Unauthorized response
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {void}
 */
const requireAuth = (req, res, next) => {
  // Get the session ID from the request
  const { id, studentId, username, isAdmin } = req.session;

  console.log(`Auth check - Session ID: ${id}, Student ID: ${studentId}, Username: ${username}`);

  // Look up the user in the model using the session ID
  let user = model.findUserById(id);

  // If no user is found but we have session data, restore the user
  if (user === undefined && studentId && username) {
    // Restore user to model from session data
    user = model.createUser(id, username, studentId);
    console.log(`🔄 Restored user from session: ${username} (ID: ${studentId})`);

    // If this is an admin, ensure they're properly set up
    if (isAdmin) {
      console.log(`🔄 Restored admin session: ${username}`);
    }
  }

  // If still no user is found, the user is not authenticated
  if (user === undefined) {
    console.log(`Authentication failed - No user found for session ID: ${id}`);
    // Return 401 Unauthorized status with error message
    res.status(401).json({ error: 'Authentication required' });
    return;
  }

  console.log(`Authentication successful - User: ${user.getUsername()}`);

  // Update the user's last activity timestamp
  user.updateActivity();

  // User is authenticated, proceed to the next middleware or route handler
  next();
};

/**
 * Register a new student account
 */
router.post('/register', validateRegistration, handleValidationErrors, async (req, res) => {
  // Input is already validated and sanitized by middleware
  const { username, password, email } = req.body;

  try {
    const db = getDB();

    // Check if username already exists
    const existingUser = await db.get('SELECT * FROM students WHERE username = ?', [username]);
    if (existingUser) {
      return res.status(409).json({ error: 'Username already exists' });
    }

    // Check if email already exists (if provided)
    if (email) {
      const existingEmail = await db.get('SELECT * FROM students WHERE email = ?', [email]);
      if (existingEmail) {
        return res.status(409).json({ error: 'Email already exists' });
      }
    }

    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Insert the new student
    const result = await db.run(
      'INSERT INTO students (username, password, email) VALUES (?, ?, ?)',
      [username, hashedPassword, email || null]
    );

    // Create a new user session
    const studentId = result.lastID;
    const user = model.createUser(req.session.id, username, studentId);

    // Add the student to the model
    model.students[studentId] = user;

    // Set the student ID in the session
    req.session.studentId = studentId;

    // Return success response
    res.status(201).json({
      success: true,
      message: 'Registration successful',
      user: {
        id: studentId,
        username,
        email: email || null
      }
    });

  } catch (error) {
    console.error('Error registering student:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Login a student
 */
router.post('/login', validateLogin, handleValidationErrors, async (req, res) => {
  // Input is already validated and sanitized by middleware
  const { username, password } = req.body;

  try {
    const db = getDB();

    // Find the student by username
    const student = await db.get('SELECT * FROM students WHERE username = ?', [username]);

    // If student not found or password doesn't match, return error
    if (!student || !(await bcrypt.compare(password, student.password))) {
      return res.status(401).json({ error: 'Invalid username or password' });
    }

    // Create a new user session
    const _user = model.createUser(req.session.id, username, student.id);

    // Set the student ID in the session
    req.session.studentId = student.id;

    console.log(`Login successful - Session ID: ${req.session.id}, Student ID: ${student.id}, Username: ${username}`);

    // Return success response
    res.status(200).json({
      success: true,
      message: 'Login successful',
      user: {
        id: student.id,
        username: student.username,
        email: student.email
      }
    });

  } catch (error) {
    console.error('Error logging in student:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Logout the current user
 */
router.post('/logout', (req, res) => {
  // Get the session ID
  const { id } = req.session;

  // Find the user by session ID
  const user = model.findUserById(id);

  // If user is found, remove them from the model
  if (user) {
    delete model.users[id];
  }

  // Destroy the session
  req.session.destroy((err) => {
    if (err) {
      console.error('Error destroying session:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }

    // Return success response
    res.status(200).json({ success: true });
  });
});

/**
 * Get the current user's information
 */
router.get('/users/me', (req, res) => {
  // Get the session data
  const { id, studentId, username, isAdmin } = req.session;

  // Find the user by session ID
  let user = model.findUserById(id);

  // If no user is found but we have session data, restore the user
  if (!user && studentId && username) {
    user = model.createUser(id, username, studentId);
    console.log(`🔄 Restored user for /users/me: ${username}`);
  }

  // If still no user is found, return unauthenticated
  if (!user) {
    return res.status(200).json({ authenticated: false });
  }

  // Handle admin users
  if (isAdmin && studentId === -1) {
    return res.status(200).json({
      authenticated: true,
      isAdmin: true,
      user: {
        id: studentId,
        username: user.getUsername(),
        email: null
      }
    });
  }

  // Find the student by ID for regular users
  const student = model.findStudentById(studentId);

  // Return the user information
  res.status(200).json({
    authenticated: true,
    isAdmin: false,
    user: {
      id: studentId,
      username: user.getUsername(),
      email: student ? student.getEmail() : null
    }
  });
});

// Export the router and requireAuth middleware
export default { router, requireAuth };

/**
 * admin.controller.js - Admin Controller
 *
 * This controller handles all admin-related operations:
 * - Admin authentication (login/logout)
 * - Managing booking lists
 * - Managing timeslots
 * - Managing locations and examination types
 */

import { Router } from 'express';
import { getDB } from '../db.js';
import model from '../model.js';
import bcrypt from 'bcrypt';
import {
  validateBookingListCreation,
  validateTimeslotCreation,
  validateLocationCreation,
  validateExaminationTypeCreation,
  validateIdParam,
  handleValidationErrors,
  sanitizeHtml,
  escapeHtml
} from '../utils/validation.js';
import BookingList from '../models/booking-list.model.js';

const router = Router();

/**
 * Admin authentication middleware
 * Checks if the user is authenticated as an admin
 */
const requireAdmin = async (req, res, next) => {
  const { username, isAdmin, id } = req.session;

  console.log(`Admin auth check - Session username: ${username}, isAdmin: ${isAdmin}, Session ID: ${id}`);

  if (!username || !isAdmin) {
    console.log('Admin authentication failed - No admin credentials in session');
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    const db = getDB();
    const admin = await db.get('SELECT * FROM admins WHERE username = ?', [username]);

    if (!admin) {
      console.log(`Admin authentication failed - No admin found for username: ${username}`);
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Create a user in the model for the admin if it doesn't exist
    // This ensures that the requireAuth middleware will also work for admin routes
    if (!model.findUserById(id)) {
      console.log(`Creating user in model for admin: ${username}, session ID: ${id}`);
      model.createUser(id, username);
    }

    console.log(`Admin authentication successful - Admin: ${username}`);
    next();
  } catch (error) {
    console.error('Error in requireAdmin middleware:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Admin login
 */
router.post('/admin/login', async (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ error: 'Username and password are required' });
  }

  try {
    const db = getDB();
    const admin = await db.get('SELECT * FROM admins WHERE username = ?', [username]);

    if (!admin) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // In a real application, we would use bcrypt to compare passwords
    // For simplicity, we're comparing plain text passwords here
    if (password !== admin.password) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Store admin username in session
    req.session.username = username;
    req.session.isAdmin = true;

    // Create a user in the model for the admin
    // This ensures that the requireAuth middleware will also work for admin routes
    const user = model.createUser(req.session.id, username);
    console.log(`Created user in model for admin: ${username}, session ID: ${req.session.id}`);

    res.status(200).json({
      authenticated: true,
      username: admin.username
    });

  } catch (error) {
    console.error('Error in admin login:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Admin logout
 */
router.post('/admin/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('Error destroying session:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }

    res.status(200).json({ success: true });
  });
});

/**
 * Get admin status
 */
router.get('/admin/status', (req, res) => {
  const { username, isAdmin } = req.session;

  res.status(200).json({
    authenticated: Boolean(isAdmin),
    username: username || null
  });
});

/**
 * Get all locations
 */
router.get('/admin/locations', requireAdmin, async (req, res) => {
  try {
    const locations = model.getLocations();
    res.status(200).json({ locations });
  } catch (error) {
    console.error('Error getting locations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create a new location
 */
router.post('/admin/locations', requireAdmin, async (req, res) => {
  const { name, description } = req.body;

  if (!name) {
    return res.status(400).json({ error: 'Location name is required' });
  }

  try {
    const db = getDB();
    const result = await db.run(
      'INSERT INTO locations (name, description) VALUES (?, ?)',
      [name, description || null]
    );

    const locationId = result.lastID;
    const location = {
      id: locationId,
      name,
      description: description || null
    };

    // Add to in-memory collection
    model.locations[locationId] = location;

    res.status(201).json(location);
  } catch (error) {
    console.error('Error creating location:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all examination types
 */
router.get('/admin/examination-types', requireAdmin, async (req, res) => {
  try {
    const examinationTypes = model.getExaminationTypes();
    res.status(200).json({ examinationTypes });
  } catch (error) {
    console.error('Error getting examination types:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all students
 */
router.get('/admin/students', requireAdmin, async (req, res) => {
  try {
    const db = getDB();
    const students = await db.all('SELECT id, username, email FROM students');
    res.status(200).json({ students });
  } catch (error) {
    console.error('Error getting students:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all groups
 */
router.get('/admin/groups', requireAdmin, async (req, res) => {
  try {
    const db = getDB();
    const groups = await db.all('SELECT id, name FROM groups');

    // Get members for each group
    for (const group of groups) {
      const members = await db.all(
        `SELECT s.id, s.username FROM students s
         JOIN group_members gm ON s.id = gm.student_id
         WHERE gm.group_id = ?`,
        [group.id]
      );
      group.members = members;
    }

    res.status(200).json({ groups });
  } catch (error) {
    console.error('Error getting groups:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create a new examination type
 */
router.post('/admin/examination-types', requireAdmin, async (req, res) => {
  const { name, description } = req.body;

  if (!name) {
    return res.status(400).json({ error: 'Examination type name is required' });
  }

  try {
    const db = getDB();
    const result = await db.run(
      'INSERT INTO examination_types (name, description) VALUES (?, ?)',
      [name, description || null]
    );

    const typeId = result.lastID;
    const examinationType = {
      id: typeId,
      name,
      description: description || null
    };

    // Add to in-memory collection
    model.examinationTypes[typeId] = examinationType;

    res.status(201).json(examinationType);
  } catch (error) {
    console.error('Error creating examination type:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all booking lists
 */
router.get('/admin/booking-lists', requireAdmin, async (req, res) => {
  try {
    const bookingLists = Object.values(model.bookingLists).map(list => list.toJSON());
    res.status(200).json({ bookingLists });
  } catch (error) {
    console.error('Error getting booking lists:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all timeslots for a booking list (admin version)
 */
router.get('/admin/booking-lists/:id/timeslots', requireAdmin, async (req, res) => {
  const { id } = req.params;
  const bookingListId = Number(id);

  if (isNaN(bookingListId)) {
    return res.status(400).json({ error: 'Invalid booking list ID' });
  }

  try {
    // Find the booking list
    const bookingList = model.findBookingListById(bookingListId);
    if (!bookingList) {
      return res.status(404).json({ error: 'Booking list not found' });
    }

    // Get all timeslots for the booking list
    const timeslots = model.getTimeslotsForBookingList(bookingListId);

    // Convert to JSON
    const timeslotsJSON = timeslots.map(timeslot => timeslot.toJSON());

    // Deduplicate by ID to ensure no duplicates
    const timeslotMap = new Map();
    timeslotsJSON.forEach(timeslot => {
      if (!timeslotMap.has(timeslot.id)) {
        timeslotMap.set(timeslot.id, timeslot);
      }
    });

    res.status(200).json({ timeslots: Array.from(timeslotMap.values()) });
  } catch (error) {
    console.error('Error getting timeslots:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create a new booking list
 */
router.post('/admin/booking-lists', requireAdmin, async (req, res) => {
  const {
    title,
    description,
    locationId,
    examinationTypeId,
    bookingStartDate,
    bookingEndDate,
    visibilityStartDate,
    visibilityEndDate,
    cancellationDeadline,
    maxBookingsPerStudent
  } = req.body;

  if (!title) {
    return res.status(400).json({ error: 'Booking list title is required' });
  }

  try {
    const db = getDB();
    const result = await db.run(
      `INSERT INTO booking_lists (
        title,
        description,
        location_id,
        examination_type_id,
        booking_start_date,
        booking_end_date,
        visibility_start_date,
        visibility_end_date,
        cancellation_deadline,
        max_bookings_per_student
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        title,
        description || null,
        locationId || null,
        examinationTypeId || null,
        bookingStartDate || null,
        bookingEndDate || null,
        visibilityStartDate || null,
        visibilityEndDate || null,
        cancellationDeadline || null,
        maxBookingsPerStudent || 1
      ]
    );

    const bookingListId = result.lastID;
    const bookingList = new BookingList(
      bookingListId,
      title,
      description,
      locationId,
      examinationTypeId,
      bookingStartDate,
      bookingEndDate,
      visibilityStartDate,
      visibilityEndDate,
      cancellationDeadline,
      maxBookingsPerStudent
    );

    // Add to in-memory collection
    model.bookingLists[bookingListId] = bookingList;

    res.status(201).json(bookingList.toJSON());
  } catch (error) {
    console.error('Error creating booking list:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Update a booking list
 */
router.put('/admin/booking-lists/:id', requireAdmin, async (req, res) => {
  const { id } = req.params;
  const bookingListId = Number(id);

  if (isNaN(bookingListId)) {
    return res.status(400).json({ error: 'Invalid booking list ID' });
  }

  const {
    title,
    description,
    locationId,
    examinationTypeId,
    bookingStartDate,
    bookingEndDate,
    visibilityStartDate,
    visibilityEndDate,
    cancellationDeadline,
    maxBookingsPerStudent
  } = req.body;

  if (!title) {
    return res.status(400).json({ error: 'Booking list title is required' });
  }

  try {
    const bookingList = model.findBookingListById(bookingListId);

    if (!bookingList) {
      return res.status(404).json({ error: 'Booking list not found' });
    }

    const db = getDB();
    await db.run(
      `UPDATE booking_lists SET
        title = ?,
        description = ?,
        location_id = ?,
        examination_type_id = ?,
        booking_start_date = ?,
        booking_end_date = ?,
        visibility_start_date = ?,
        visibility_end_date = ?,
        cancellation_deadline = ?,
        max_bookings_per_student = ?
      WHERE id = ?`,
      [
        title,
        description || null,
        locationId || null,
        examinationTypeId || null,
        bookingStartDate || null,
        bookingEndDate || null,
        visibilityStartDate || null,
        visibilityEndDate || null,
        cancellationDeadline || null,
        maxBookingsPerStudent || 1,
        bookingListId
      ]
    );

    // Update in-memory object
    bookingList.title = title;
    bookingList.description = description;
    bookingList.locationId = locationId;
    bookingList.examinationTypeId = examinationTypeId;
    bookingList.bookingStartDate = bookingStartDate;
    bookingList.bookingEndDate = bookingEndDate;
    bookingList.visibilityStartDate = visibilityStartDate;
    bookingList.visibilityEndDate = visibilityEndDate;
    bookingList.cancellationDeadline = cancellationDeadline;
    bookingList.maxBookingsPerStudent = maxBookingsPerStudent;

    // Notify all clients about the updated booking list
    if (model.io) {
      model.io.emit('booking_list_updated', bookingList.toJSON());
    }

    res.status(200).json(bookingList.toJSON());
  } catch (error) {
    console.error('Error updating booking list:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create a new timeslot
 */
router.post('/admin/timeslots', requireAdmin, async (req, res) => {
  const { bookingListId, date, time, locationId } = req.body;

  console.log('Creating timeslot with request body:', req.body);

  if (!bookingListId || !time) {
    return res.status(400).json({ error: 'Booking list ID and time are required' });
  }

  // Use today's date if not provided
  const timeslotDate = date || new Date().toISOString().split('T')[0];

  try {
    // Validate booking list ID
    if (isNaN(Number(bookingListId))) {
      return res.status(400).json({ error: 'Booking list ID must be a number' });
    }

    // Validate time format (simple validation)
    if (!/^\d{1,2}:\d{2}$/.test(time)) {
      return res.status(400).json({ error: 'Time must be in format HH:MM' });
    }

    // Validate date format if provided
    if (date && !/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return res.status(400).json({ error: 'Date must be in format YYYY-MM-DD' });
    }

    // Check if booking list exists
    const bookingList = model.findBookingListById(Number(bookingListId));
    if (!bookingList) {
      return res.status(404).json({ error: 'Booking list not found' });
    }

    // Check if the timeslot date is within the visibility period of the booking list
    const timeslotDate = new Date(date);
    timeslotDate.setHours(0, 0, 0, 0); // Set to midnight for accurate comparison

    // Check against visibility end date if it exists
    if (bookingList.visibilityEndDate) {
      const visibilityEndDate = new Date(bookingList.visibilityEndDate);
      visibilityEndDate.setHours(0, 0, 0, 0);

      if (timeslotDate > visibilityEndDate) {
        return res.status(400).json({
          error: `Cannot add timeslot after the visibility end date (${bookingList.visibilityEndDate})`
        });
      }
    }

    // Check against visibility start date if it exists
    if (bookingList.visibilityStartDate) {
      const visibilityStartDate = new Date(bookingList.visibilityStartDate);
      visibilityStartDate.setHours(0, 0, 0, 0);

      if (timeslotDate < visibilityStartDate) {
        return res.status(400).json({
          error: `Cannot add timeslot before the visibility start date (${bookingList.visibilityStartDate})`
        });
      }
    }

    const db = getDB();

    // Check if a timeslot with the same booking list, date, and time already exists
    const existingTimeslot = await db.get(
      'SELECT * FROM timeslots WHERE booking_list_id = ? AND date = ? AND time = ?',
      [bookingListId, date, time]
    );

    if (existingTimeslot) {
      return res.status(409).json({
        error: `A timeslot for this booking list on ${date} at ${time} already exists`
      });
    }

    // Insert the new timeslot into the database
    const result = await db.run(
      `INSERT INTO timeslots (
        booking_list_id,
        date,
        time,
        location_id,
        booked,
        booked_by_student_id,
        booked_by_group_id,
        reserved_until,
        cancelled,
        cancelled_at,
        cancelled_by_id
      ) VALUES (?, ?, ?, ?, 0, NULL, NULL, NULL, 0, NULL, NULL)`,
      [bookingListId, timeslotDate, time, locationId || null]
    );

    const timeslotId = result.lastID;

    // Create a new Timeslot object
    const timeslot = model.createTimeslotObject(
      timeslotId,
      bookingListId,
      timeslotDate,
      time,
      locationId
    );

    // Add to in-memory collection
    model.timeslots[timeslotId] = timeslot;

    // Notify all clients about the new timeslot
    if (model.io) {
      model.io.emit('timeslot_created', timeslot.toJSON());
    }

    res.status(201).json(timeslot.toJSON());
  } catch (error) {
    console.error('Error creating timeslot:', error);
    res.status(500).json({ error: `Internal server error: ${error.message}` });
  }
});

/**
 * Delete a timeslot
 */
router.delete('/admin/timeslots/:id', requireAdmin, async (req, res) => {
  const { id } = req.params;
  const timeslotId = Number(id);

  if (isNaN(timeslotId)) {
    return res.status(400).json({ error: 'Invalid timeslot ID' });
  }

  try {
    const timeslot = model.findTimeslotById(timeslotId);

    if (!timeslot) {
      return res.status(404).json({ error: 'Timeslot not found' });
    }

    const db = getDB();
    await db.run('DELETE FROM timeslots WHERE id = ?', [timeslotId]);

    // Remove from in-memory collection
    delete model.timeslots[timeslotId];

    // Notify all clients about the deleted timeslot
    if (model.io) {
      model.io.emit('timeslot_deleted', timeslotId);
    }

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error deleting timeslot:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Delete a booking list
 */
router.delete('/admin/booking-lists/:id', requireAdmin, async (req, res) => {
  const { id } = req.params;
  const bookingListId = Number(id);

  if (isNaN(bookingListId)) {
    return res.status(400).json({ error: 'Invalid booking list ID' });
  }

  try {
    const bookingList = model.findBookingListById(bookingListId);

    if (!bookingList) {
      return res.status(404).json({ error: 'Booking list not found' });
    }

    const db = getDB();

    // First, delete all timeslots associated with this booking list
    await db.run('DELETE FROM timeslots WHERE booking_list_id = ?', [bookingListId]);

    // Then delete the booking list itself
    await db.run('DELETE FROM booking_lists WHERE id = ?', [bookingListId]);

    // Remove timeslots from in-memory collection
    for (const timeslotId in model.timeslots) {
      if (model.timeslots[timeslotId].bookingListId === bookingListId) {
        delete model.timeslots[timeslotId];
      }
    }

    // Remove booking list from in-memory collection
    delete model.bookingLists[bookingListId];

    // Notify all clients about the deleted booking list
    if (model.io) {
      model.io.emit('booking_list_deleted', bookingListId);
    }

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error deleting booking list:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Export the router and requireAdmin middleware
export default { router, requireAdmin };

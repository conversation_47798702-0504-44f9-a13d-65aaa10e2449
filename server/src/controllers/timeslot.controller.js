/**
 * timeslot.controller.js - Timeslot Controller
 *
 * This controller handles all timeslot-related operations:
 * - Listing timeslots
 * - Reserving timeslots
 * - Booking timeslots
 * - Cancelling bookings
 */

import { Router } from 'express';
import { getDB } from '../db.js';
import model from '../model.js';
// import Timeslot from '../models/timeslot.model.js'; // Unused - using model methods

const router = Router();

/**
 * Get all timeslots for a booking list
 */
router.get('/booking-lists/:id/timeslots', async (req, res) => {
  const { id } = req.params;
  const bookingListId = Number(id);

  if (isNaN(bookingListId)) {
    return res.status(400).json({ error: 'Invalid booking list ID' });
  }

  try {
    // Find the booking list
    const bookingList = model.findBookingListById(bookingListId);
    if (!bookingList) {
      return res.status(404).json({ error: 'Booking list not found' });
    }

    // Check if the booking list is visible
    if (!bookingList.isVisible()) {
      return res.status(403).json({ error: 'Booking list is not currently visible' });
    }

    // Get all timeslots for the booking list
    const timeslots = model.getTimeslotsForBookingList(bookingListId);

    // Convert to JSON
    const timeslotsJSON = timeslots.map(timeslot => timeslot.toJSON());

    // Deduplicate by ID to ensure no duplicates
    const timeslotMap = new Map();
    timeslotsJSON.forEach(timeslot => {
      if (!timeslotMap.has(timeslot.id)) {
        timeslotMap.set(timeslot.id, timeslot);
      }
    });

    // Convert back to array
    const uniqueTimeslots = Array.from(timeslotMap.values());

    console.log(`Returning ${uniqueTimeslots.length} timeslots after deduplication (from ${timeslotsJSON.length} total)`);

    // Return the deduplicated timeslots
    res.status(200).json({ timeslots: uniqueTimeslots });
  } catch (error) {
    console.error('Error getting timeslots:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Reserve a timeslot
 */
router.post('/timeslots/:id/reserve', async (req, res) => {
  const { id } = req.params;
  const timeslotId = Number(id);
  const { id: sessionId, studentId } = req.session;

  if (isNaN(timeslotId)) {
    return res.status(400).json({ error: 'Invalid timeslot ID' });
  }

  // Check if user is authenticated
  const user = model.findUserById(sessionId);
  if (!user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    // Find the timeslot
    const timeslot = model.findTimeslotById(timeslotId);
    if (!timeslot) {
      return res.status(404).json({ error: 'Timeslot not found' });
    }

    // Find the booking list
    const bookingList = model.findBookingListById(timeslot.getBookingListId());
    if (!bookingList) {
      return res.status(404).json({ error: 'Booking list not found' });
    }

    // Check if booking is allowed for this list
    if (!bookingList.isBookingAllowed()) {
      return res.status(403).json({ error: 'Booking is not currently allowed for this list' });
    }

    // Check if the timeslot is already booked
    if (timeslot.isBooked()) {
      return res.status(400).json({ error: 'Timeslot is already booked' });
    }

    // Check if the timeslot is already reserved
    if (timeslot.isReserved()) {
      return res.status(400).json({ error: 'Timeslot is already reserved by another user' });
    }

    // Check if the user already has a reserved timeslot
    const userReservation = user.getReservedTimeslot();
    if (userReservation) {
      // Check if the reservation is still valid
      const reservedTimeslot = model.findTimeslotById(userReservation);
      if (reservedTimeslot && reservedTimeslot.isReserved()) {
        return res.status(400).json({ error: 'You already have a reserved timeslot' });
      } else {
        // Clear the stale reservation
        console.log(`Clearing stale reservation for user ${user.getUsername()}, timeslot ${userReservation}`);
        user.clearReservedTimeslot();
      }
    }

    // Check if the student has reached the maximum number of bookings for this list
    const db = getDB();
    const bookingsCount = await db.get(
      `SELECT COUNT(*) as count FROM timeslots
       WHERE booking_list_id = ? AND booked = 1 AND booked_by_student_id = ? AND cancelled = 0`,
      [bookingList.getId(), studentId]
    );

    // Also check bookings made by groups the student is a member of
    const groupBookingsCount = await db.get(
      `SELECT COUNT(*) as count FROM timeslots t
       JOIN group_members gm ON t.booked_by_group_id = gm.group_id
       WHERE t.booking_list_id = ? AND t.booked = 1 AND gm.student_id = ? AND t.cancelled = 0`,
      [bookingList.getId(), studentId]
    );

    const totalBookings = bookingsCount.count + groupBookingsCount.count;
    if (totalBookings >= bookingList.maxBookingsPerStudent) {
      return res.status(400).json({
        error: `You have reached the maximum number of bookings (${bookingList.maxBookingsPerStudent}) for this list`
      });
    }

    // Reserve the timeslot (10 seconds)
    const reservationDuration = 10000; // 10 seconds
    const expiresAt = Date.now() + reservationDuration;
    timeslot.reserve(reservationDuration);
    user.reserveTimeslot(timeslotId);

    // Update the database
    await db.run(
      'UPDATE timeslots SET reserved_until = ? WHERE id = ?',
      [expiresAt, timeslotId]
    );

    // Save to persistent storage for server restart recovery
    if (model.persistentStorage) {
      await model.persistentStorage.saveReservation(timeslotId, sessionId, studentId, expiresAt);
    }

    // Set up a timeout to automatically cancel the reservation
    const timeoutId = setTimeout(async () => {
      try {
        // Check if the timeslot is still reserved by this user
        const currentTimeslot = model.findTimeslotById(timeslotId);
        if (currentTimeslot && currentTimeslot.isReserved() && !currentTimeslot.isBooked()) {
          // Cancel the reservation
          currentTimeslot.reservedUntil = null;

          // Update the database
          const db = getDB();
          await db.run(
            'UPDATE timeslots SET reserved_until = NULL WHERE id = ?',
            [timeslotId]
          );

          // Clear the user's reservation
          const currentUser = model.findUserById(sessionId);
          if (currentUser && currentUser.getReservedTimeslot() === timeslotId) {
            currentUser.clearReservedTimeslot();
          }

          // Remove from persistent storage
          if (model.persistentStorage) {
            await model.persistentStorage.removeReservation(timeslotId);
          }

          // Broadcast the cancellation
          if (model.io) {
            model.io.emit('timeslot_reservation_cancelled', currentTimeslot.toJSON());
          }

          console.log(`Reservation for timeslot ${timeslotId} expired`);
        }
      } catch (error) {
        console.error('Error cancelling reservation:', error);
      }
    }, reservationDuration);

    // Store the timeout ID
    model.reservationTimeouts[timeslotId] = timeoutId;

    // Broadcast the reservation
    if (model.io) {
      model.io.emit('timeslot_reserved', timeslot.toJSON());
    }

    // Return the timeslot
    res.status(200).json(timeslot.toJSON());
  } catch (error) {
    console.error('Error reserving timeslot:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Book a timeslot
 */
router.post('/timeslots/:id/book', async (req, res) => {
  const { id } = req.params;
  const timeslotId = Number(id);
  const { id: sessionId, studentId } = req.session;
  const { groupId } = req.body;

  if (isNaN(timeslotId)) {
    return res.status(400).json({ error: 'Invalid timeslot ID' });
  }

  // Check if user is authenticated
  const user = model.findUserById(sessionId);
  if (!user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    // Find the timeslot
    const timeslot = model.findTimeslotById(timeslotId);
    if (!timeslot) {
      return res.status(404).json({ error: 'Timeslot not found' });
    }

    // Find the booking list
    const bookingList = model.findBookingListById(timeslot.getBookingListId());
    if (!bookingList) {
      return res.status(404).json({ error: 'Booking list not found' });
    }

    // Check if booking is allowed for this list
    if (!bookingList.isBookingAllowed()) {
      return res.status(403).json({ error: 'Booking is not currently allowed for this list' });
    }

    // Check if the timeslot is already booked
    if (timeslot.isBooked()) {
      return res.status(400).json({ error: 'Timeslot is already booked' });
    }

    // Check if the timeslot is reserved by this user
    const userReservation = user.getReservedTimeslot();
    if (userReservation !== timeslotId) {
      return res.status(403).json({ error: 'You can only book a timeslot that you have reserved' });
    }

    // If booking for a group, check if the user is a member of the group
    if (groupId) {
      const group = model.findGroupById(Number(groupId));
      if (!group) {
        return res.status(404).json({ error: 'Group not found' });
      }

      if (!group.isMember(studentId)) {
        return res.status(403).json({ error: 'You are not a member of this group' });
      }

      // Check if the group has reached the maximum number of bookings for this list
      const db = getDB();
      const bookingsCount = await db.get(
        `SELECT COUNT(*) as count FROM timeslots
         WHERE booking_list_id = ? AND booked = 1 AND booked_by_group_id = ? AND cancelled = 0`,
        [bookingList.getId(), groupId]
      );

      if (bookingsCount.count >= bookingList.maxBookingsPerStudent) {
        return res.status(400).json({
          error: `This group has reached the maximum number of bookings (${bookingList.maxBookingsPerStudent}) for this list`
        });
      }
    } else {
      // Check if the student has reached the maximum number of bookings for this list
      const db = getDB();
      const bookingsCount = await db.get(
        `SELECT COUNT(*) as count FROM timeslots
         WHERE booking_list_id = ? AND booked = 1 AND booked_by_student_id = ? AND cancelled = 0`,
        [bookingList.getId(), studentId]
      );

      // Also check bookings made by groups the student is a member of
      const groupBookingsCount = await db.get(
        `SELECT COUNT(*) as count FROM timeslots t
         JOIN group_members gm ON t.booked_by_group_id = gm.group_id
         WHERE t.booking_list_id = ? AND t.booked = 1 AND gm.student_id = ? AND t.cancelled = 0`,
        [bookingList.getId(), studentId]
      );

      const totalBookings = bookingsCount.count + groupBookingsCount.count;
      if (totalBookings >= bookingList.maxBookingsPerStudent) {
        return res.status(400).json({
          error: `You have reached the maximum number of bookings (${bookingList.maxBookingsPerStudent}) for this list`
        });
      }
    }

    // Book the timeslot
    const groupIdNum = groupId ? Number(groupId) : null;
    timeslot.book(studentId, groupIdNum);

    // Update the database
    const db = getDB();
    await db.run(
      `UPDATE timeslots
       SET booked = 1,
           booked_by_student_id = ?,
           booked_by_group_id = ?,
           reserved_until = NULL,
           cancelled = 0,
           cancelled_at = NULL,
           cancelled_by_id = NULL
       WHERE id = ?`,
      [studentId, groupIdNum, timeslotId]
    );

    console.log(`Booked timeslot ${timeslotId} by student ${studentId}${groupIdNum ? ` for group ${groupIdNum}` : ''}`);

    // Clear the user's reservation
    user.clearReservedTimeslot();

    // Clear any existing reservation timeout
    if (model.reservationTimeouts[timeslotId]) {
      clearTimeout(model.reservationTimeouts[timeslotId]);
      delete model.reservationTimeouts[timeslotId];
    }

    // Remove from persistent storage
    if (model.persistentStorage) {
      await model.persistentStorage.removeReservation(timeslotId);
    }

    // Broadcast the booking
    if (model.io) {
      model.io.emit('timeslot_booked', timeslot.toJSON());
    }

    // Return the timeslot
    res.status(200).json(timeslot.toJSON());
  } catch (error) {
    console.error('Error booking timeslot:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Cancel a booking
 */
router.post('/timeslots/:id/cancel', async (req, res) => {
  const { id } = req.params;
  const timeslotId = Number(id);
  const { id: sessionId, studentId } = req.session;

  if (isNaN(timeslotId)) {
    return res.status(400).json({ error: 'Invalid timeslot ID' });
  }

  // Check if user is authenticated
  const user = model.findUserById(sessionId);
  if (!user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    // Find the timeslot
    const timeslot = model.findTimeslotById(timeslotId);
    if (!timeslot) {
      return res.status(404).json({ error: 'Timeslot not found' });
    }

    // Find the booking list
    const bookingList = model.findBookingListById(timeslot.getBookingListId());
    if (!bookingList) {
      return res.status(404).json({ error: 'Booking list not found' });
    }

    // Check if cancellation is allowed for this list
    if (!bookingList.isCancellationAllowed()) {
      return res.status(403).json({ error: 'Cancellation is not currently allowed for this list' });
    }

    // Check if the timeslot is booked
    if (!timeslot.isBooked()) {
      return res.status(400).json({ error: 'Timeslot is not booked' });
    }

    // Check if the timeslot is booked by this student or a group they belong to
    if (Number(timeslot.bookedByStudentId) !== Number(studentId)) {
      // If booked by a group, check if the student is a member
      if (timeslot.bookedByGroupId) {
        console.log(`Checking if student ${studentId} is a member of group ${timeslot.bookedByGroupId}`);
        const group = model.findGroupById(Number(timeslot.bookedByGroupId));

        if (!group) {
          console.log(`Group ${timeslot.bookedByGroupId} not found`);
          return res.status(403).json({ error: 'You cannot cancel a booking that you did not make' });
        }

        // Check if the student is a member of the group
        const isMember = group.isMember(Number(studentId));
        console.log(`Student ${studentId} is member of group ${timeslot.bookedByGroupId}: ${isMember}`);

        if (!isMember) {
          return res.status(403).json({ error: 'You cannot cancel a booking that you did not make' });
        }
      } else {
        return res.status(403).json({ error: 'You cannot cancel a booking that you did not make' });
      }
    }

    // Cancel the booking
    timeslot.cancel(studentId);

    // Get current timestamp
    const now = new Date().toISOString();

    // Update the database
    const db = getDB();
    await db.run(
      `UPDATE timeslots
       SET cancelled = 1,
           cancelled_at = ?,
           cancelled_by_id = ?
       WHERE id = ?`,
      [now, studentId, timeslotId]
    );

    console.log(`Cancelled timeslot ${timeslotId} by student ${studentId} at ${now}`);

    // Create a JSON representation with explicit cancelled flag
    const timeslotJSON = timeslot.toJSON();

    // Ensure the cancelled flag is explicitly set to true
    timeslotJSON.cancelled = true;
    timeslotJSON.cancelledAt = new Date().toISOString();
    timeslotJSON.cancelledById = Number(studentId);

    // Add additional information for client-side display
    const bookingListForTimeslot = model.findBookingListByTimeslotId(timeslotId);
    if (bookingListForTimeslot) {
      timeslotJSON.bookingListId = bookingListForTimeslot.getId();
      timeslotJSON.bookingListTitle = bookingListForTimeslot.getTitle();
    }

    // If booked by a group, include group information
    if (timeslot.bookedByGroupId) {
      const group = model.findGroupById(Number(timeslot.bookedByGroupId));
      if (group) {
        timeslotJSON.groupName = group.getName();
      }
    }

    // Broadcast the cancellation
    if (model.io) {
      console.log('Emitting timeslot_cancelled event:', timeslotJSON);
      model.io.emit('timeslot_cancelled', timeslotJSON);
    }

    // Return the timeslot
    res.status(200).json(timeslotJSON);
  } catch (error) {
    console.error('Error cancelling booking:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all locations
 */
router.get('/locations', async (req, res) => {
  try {
    // Get all locations
    const locations = model.getLocations();

    // Return the locations
    res.status(200).json({ locations });
  } catch (error) {
    console.error('Error getting locations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all examination types
 */
router.get('/examination-types', async (req, res) => {
  try {
    // Get all examination types
    const examinationTypes = model.getExaminationTypes();

    // Return the examination types
    res.status(200).json({ examinationTypes });
  } catch (error) {
    console.error('Error getting examination types:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all visible booking lists
 */
router.get('/booking-lists', async (req, res) => {
  try {
    // Get all visible booking lists
    const bookingLists = model.getVisibleBookingLists();

    // Convert to JSON and return
    const bookingListsJSON = bookingLists.map(list => list.toJSON());
    res.status(200).json({ bookingLists: bookingListsJSON });
  } catch (error) {
    console.error('Error getting booking lists:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all bookings for the current user
 */
router.get('/my-bookings', async (req, res) => {
  const { studentId } = req.session;

  if (!studentId) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    const db = getDB();

    // Get all bookings for this student (both individual and group bookings)
    const individualBookings = await db.all(
      `SELECT t.*, bl.title as booking_list_title, l.name as location_name, l.id as location_id
       FROM timeslots t
       JOIN booking_lists bl ON t.booking_list_id = bl.id
       LEFT JOIN locations l ON t.location_id = l.id
       WHERE t.booked_by_student_id = ?`,
      [studentId]
    );

    // Get all group bookings for groups the student is a member of
    const groupBookings = await db.all(
      `SELECT t.*, bl.title as booking_list_title, g.name as group_name, l.name as location_name, l.id as location_id
       FROM timeslots t
       JOIN booking_lists bl ON t.booking_list_id = bl.id
       JOIN groups g ON t.booked_by_group_id = g.id
       JOIN group_members gm ON g.id = gm.group_id
       LEFT JOIN locations l ON t.location_id = l.id
       WHERE gm.student_id = ? AND t.booked_by_group_id IS NOT NULL`,
      [studentId]
    );

    // Combine the bookings and remove duplicates by timeslot ID
    const allBookings = [...individualBookings, ...groupBookings];

    // Create a Map to deduplicate by timeslot ID
    const bookingsMap = new Map();
    allBookings.forEach(booking => {
      // If we already have this booking and it's not a duplicate, skip it
      if (!bookingsMap.has(booking.id)) {
        bookingsMap.set(booking.id, booking);
      }
    });

    // Convert back to array
    const bookings = Array.from(bookingsMap.values());

    console.log(`Returning ${bookings.length} bookings after deduplication (from ${allBookings.length} total)`);

    // Return the deduplicated bookings
    res.status(200).json({ bookings });
  } catch (error) {
    console.error('Error getting user bookings:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Export the router
export default { router };

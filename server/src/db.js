/**
 * db.js - Database Management Module
 *
 * This module handles all database-related operations for the booking system:
 * - Establishing connection to the SQLite database
 * - Creating database schema (tables) if they don't exist
 * - Setting up default data (admin users, locations, examination types)
 * - Providing access to the database connection
 */

import sqlite3 from 'sqlite3'; // SQLite database driver
import { open } from 'sqlite'; // Promise-based wrapper for sqlite3

// Database connection object (initialized in initDB)
let db = null;

/**
 * Initialize the database connection and set up schema
 *
 * This function:
 * 1. Opens a connection to the SQLite database file
 * 2. Creates necessary tables if they don't exist
 * 3. Sets up default admin user with proper validation
 * 4. Creates default locations and examination types if none exist
 *
 * @returns {Promise<void>} A promise that resolves when initialization is complete
 */
export async function initDB() {
  // Open connection to the SQLite database file
  db = await open({
    filename: './database.sqlite',  // Database file path (relative to server directory)
    driver: sqlite3.Database        // Use sqlite3 as the driver
  });

  // Create tables if they don't exist using SQL DDL statements
  await db.exec(`
    -- Admin users table for authentication
    CREATE TABLE IF NOT EXISTS admins (
      id INTEGER PRIMARY KEY AUTOINCREMENT,  -- Unique identifier
      username TEXT UNIQUE NOT NULL,         -- Admin username (must be unique)
      password TEXT NOT NULL                 -- Admin password (stored securely)
    );

    -- Students table for authentication
    CREATE TABLE IF NOT EXISTS students (
      id INTEGER PRIMARY KEY AUTOINCREMENT,  -- Unique identifier
      username TEXT UNIQUE NOT NULL,         -- Student username (must be unique)
      password TEXT NOT NULL,                -- Student password (stored securely)
      email TEXT UNIQUE,                     -- Student email (optional)
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Groups table for student groups
    CREATE TABLE IF NOT EXISTS groups (
      id INTEGER PRIMARY KEY AUTOINCREMENT,  -- Unique identifier
      name TEXT NOT NULL,                    -- Group name
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Group members table (many-to-many relationship)
    CREATE TABLE IF NOT EXISTS group_members (
      id INTEGER PRIMARY KEY AUTOINCREMENT,  -- Unique identifier
      group_id INTEGER NOT NULL,             -- Reference to the group
      student_id INTEGER NOT NULL,           -- Reference to the student
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (group_id) REFERENCES groups (id),
      FOREIGN KEY (student_id) REFERENCES students (id),
      UNIQUE(group_id, student_id)           -- Prevent duplicate memberships
    );

    -- Locations table for booking locations
    CREATE TABLE IF NOT EXISTS locations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,  -- Unique identifier
      name TEXT NOT NULL,                    -- Location name
      description TEXT                       -- Location description
    );

    -- Examination types table
    CREATE TABLE IF NOT EXISTS examination_types (
      id INTEGER PRIMARY KEY AUTOINCREMENT,  -- Unique identifier
      name TEXT NOT NULL,                    -- Type name (e.g., "Lab Presentation", "Oral Exam")
      description TEXT                       -- Type description
    );

    -- Booking lists table
    CREATE TABLE IF NOT EXISTS booking_lists (
      id INTEGER PRIMARY KEY AUTOINCREMENT,  -- Unique identifier
      title TEXT NOT NULL,                   -- List title
      description TEXT,                      -- List description
      location_id INTEGER,                   -- Default location for all timeslots
      examination_type_id INTEGER,           -- Type of examination
      booking_start_date TEXT,               -- Date when booking becomes available (YYYY-MM-DD)
      booking_end_date TEXT,                 -- Date when booking closes (YYYY-MM-DD)
      visibility_start_date TEXT,            -- Date when list becomes visible (YYYY-MM-DD)
      visibility_end_date TEXT,              -- Date when list becomes hidden (YYYY-MM-DD)
      cancellation_deadline TEXT,            -- Last date for cancellation (YYYY-MM-DD)
      max_bookings_per_student INTEGER DEFAULT 1, -- Maximum bookings per student/group
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (location_id) REFERENCES locations (id),
      FOREIGN KEY (examination_type_id) REFERENCES examination_types (id)
    );

    -- Timeslots table for bookable slots
    CREATE TABLE IF NOT EXISTS timeslots (
      id INTEGER PRIMARY KEY AUTOINCREMENT,  -- Unique identifier
      booking_list_id INTEGER NOT NULL,      -- Reference to the booking list
      date TEXT NOT NULL,                    -- Date of the timeslot (YYYY-MM-DD)
      time TEXT NOT NULL,                    -- Time of the timeslot (HH:MM)
      location_id INTEGER,                   -- Location (room) for the session (overrides booking list default)
      booked BOOLEAN DEFAULT 0,              -- Whether the timeslot is booked (0=false, 1=true)
      booked_by_student_id INTEGER,          -- Student who booked it (if booked)
      booked_by_group_id INTEGER,            -- Group that booked it (if booked by a group)
      reserved_until INTEGER,                -- Timestamp when reservation expires (if reserved)
      cancelled BOOLEAN DEFAULT 0,           -- Whether the booking was cancelled
      cancelled_at TIMESTAMP,                -- When the booking was cancelled
      cancelled_by_id INTEGER,               -- Who cancelled the booking
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (booking_list_id) REFERENCES booking_lists (id),
      FOREIGN KEY (location_id) REFERENCES locations (id),
      FOREIGN KEY (booked_by_student_id) REFERENCES students (id),
      FOREIGN KEY (booked_by_group_id) REFERENCES groups (id),
      FOREIGN KEY (cancelled_by_id) REFERENCES students (id)
    );

    -- Active reservations table for persistent live resources
    CREATE TABLE IF NOT EXISTS active_reservations (
      timeslot_id INTEGER PRIMARY KEY,       -- Reference to the timeslot
      session_id TEXT NOT NULL,              -- User session ID
      student_id INTEGER NOT NULL,           -- Student who made the reservation
      expires_at INTEGER NOT NULL,           -- When the reservation expires (timestamp)
      created_at INTEGER NOT NULL,           -- When the reservation was created (timestamp)
      FOREIGN KEY (timeslot_id) REFERENCES timeslots (id),
      FOREIGN KEY (student_id) REFERENCES students (id)
    );

    -- User sessions table for persistent session state
    CREATE TABLE IF NOT EXISTS user_sessions (
      session_id TEXT PRIMARY KEY,           -- Session ID
      student_id INTEGER,                    -- Associated student ID
      username TEXT NOT NULL,                -- Username
      last_activity INTEGER NOT NULL,        -- Last activity timestamp
      current_page TEXT DEFAULT '/',         -- Current page user was on
      state_data TEXT DEFAULT '{}',          -- Additional state data as JSON
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (student_id) REFERENCES students (id)
    );

    -- Live interactions table for temporary user interactions
    CREATE TABLE IF NOT EXISTS live_interactions (
      session_id TEXT PRIMARY KEY,           -- Session ID
      interaction_type TEXT NOT NULL,        -- Type of interaction (form_data, booking_process, etc.)
      data TEXT NOT NULL,                    -- Interaction data as JSON
      last_updated INTEGER NOT NULL,         -- Last update timestamp
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- User navigation state table for tracking booking flow
    CREATE TABLE IF NOT EXISTS user_navigation_state (
      session_id TEXT PRIMARY KEY,           -- Session ID
      current_page TEXT,                     -- Current page URL
      selected_timeslot_id INTEGER,          -- Selected timeslot ID
      booking_type TEXT,                     -- 'individual' or 'group'
      selected_group_id INTEGER,             -- Selected group ID (if group booking)
      reservation_expires_at INTEGER,        -- When the reservation expires
      created_at INTEGER NOT NULL,           -- Creation timestamp
      updated_at INTEGER NOT NULL            -- Last update timestamp
    );
  `);

  // Insert default admin user if not exists
  const admin1Exists = await db.get('SELECT * FROM admins WHERE username = ?', ['admin1']);
  if (!admin1Exists) {
    await db.run('INSERT INTO admins (username, password) VALUES (?, ?)', ['admin1', 'admin123']);
    console.log('Added default admin user (admin1) to database');
  }

  // Insert default locations if the locations table is empty
  const locationsCount = await db.get('SELECT COUNT(*) as count FROM locations');
  if (locationsCount.count === 0) {
    await db.run('INSERT INTO locations (name, description) VALUES (?, ?)', ['Room A', 'Main examination room']);
    await db.run('INSERT INTO locations (name, description) VALUES (?, ?)', ['Room B', 'Secondary examination room']);
    await db.run('INSERT INTO locations (name, description) VALUES (?, ?)', ['Online', 'Virtual examination via Zoom']);
    console.log('Added default locations to database');
  }

  // Insert default examination types if the examination_types table is empty
  const examinationTypesCount = await db.get('SELECT COUNT(*) as count FROM examination_types');
  if (examinationTypesCount.count === 0) {
    await db.run('INSERT INTO examination_types (name, description) VALUES (?, ?)', ['Lab Presentation', 'Presentation of laboratory work']);
    await db.run('INSERT INTO examination_types (name, description) VALUES (?, ?)', ['Oral Exam', 'Verbal examination of course material']);
    await db.run('INSERT INTO examination_types (name, description) VALUES (?, ?)', ['Project Demo', 'Demonstration of project work']);
    console.log('Added default examination types to database');
  }

  console.log('Database initialized');
}

/**
 * Get the database connection
 * @returns {object} The database connection object
 * @throws {Error} If the database has not been initialized
 */
export function getDB() {
  if (!db) {
    throw new Error('Database not initialized. Call initDB() first.');
  }
  return db;
}

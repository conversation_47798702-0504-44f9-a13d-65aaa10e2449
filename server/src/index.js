/**
 * index.js - Main Server Entry Point
 *
 * This file is the central entry point for the booking system's backend.
 * It sets up the Express server, configures middleware, initializes the database,
 * establishes routes, and sets up Socket.io for real-time communication.
 */

import express from 'express';
import { createServer } from 'http';
import { createServer as createHttpsServer } from 'https';
import { Server } from 'socket.io';
import session from 'express-session';
import { fileURLToPath } from 'url';
import { dirname, resolve as resolvePath } from 'path';
import { readFileSync } from 'fs';
import cors from 'cors';
import helmet from 'helmet';
import { initDB } from './db.js';
import model from './model.js';
import reservationManager from './utils/reservationManager.js';

// Import controllers
import auth from './controllers/auth.controller.js';
import admin from './controllers/admin.controller.js';
import timeslot from './controllers/timeslot.controller.js';
import group from './controllers/group.controller.js';

// Define server ports and create Express application
const httpPort = 8989;
const httpsPort = 8990;
const app = express();

// Create both HTTP and HTTPS servers
let server;
let httpsServer;

try {
  // Try to create HTTPS server with self-signed certificates
  const httpsOptions = {
    key: readFileSync(resolvePath(dirname(fileURLToPath(import.meta.url)), '../certs/server.key')),
    cert: readFileSync(resolvePath(dirname(fileURLToPath(import.meta.url)), '../certs/server.cert'))
  };
  httpsServer = createHttpsServer(httpsOptions, app);
  server = createServer(app); // HTTP server for redirect
  console.log('HTTPS certificates loaded successfully');
} catch (error) {
  console.log('HTTPS certificates not found, running HTTP only');
  server = createServer(app);
}

const io = new Server(httpsServer || server, {
  cors: {
    origin: httpsServer ? "https://localhost:8990" : "http://localhost:8989",
    credentials: true
  }
});

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configure helmet for security headers
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"],
      fontSrc: ["'self'", "https://cdn.jsdelivr.net"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// Configure session middleware
app.use(session({
  secret: 'booking-system-secret-key-change-in-production',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: !!httpsServer, // Use secure cookies if HTTPS is available
    maxAge: 48 * 60 * 60 * 1000, // 48 hours
    httpOnly: true,
    sameSite: 'lax'
  }
}));

// Note: Both HTTP and HTTPS servers serve the same application
// Users can access either http://localhost:8989 or https://localhost:8990

// Configure CORS
app.use(cors({
  origin: [
    'http://localhost:5173',
    'https://localhost:5173',
    'http://localhost:8989',
    'https://localhost:8990'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Serve static files from the client/dist directory
app.use(express.static(resolvePath(__dirname, '../../client/dist')));

// Register middlewares to parse request bodies
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Bind REST controllers to /api/* routes
app.use('/api', auth.router);
app.use('/api', admin.router);
app.use('/api', auth.requireAuth, timeslot.router);
app.use('/api', auth.requireAuth, group.router);

// Catch-all route to serve the SPA for client-side routing
app.get('*', (req, res) => {
  res.sendFile(resolvePath(__dirname, '../../client/dist/index.html'));
});

// Set up Socket.io connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Initialize the application
async function init() {
  try {
    // Initialize the database
    await initDB();
    console.log('Database initialized');

    // Load data from the database into memory
    await model.loadData();
    console.log('Data loaded into memory');

    // Load persistent live resources
    await reservationManager.loadPersistentData();
    console.log('Persistent live resources loaded');

    // Set the Socket.io instance in the model for broadcasting updates
    model.setIo(io);

    // Set reservation manager in model
    model.setReservationManager(reservationManager);

    // Set up a periodic cleanup of inactive users (every 5 minutes)
    const cleanupInterval = setInterval(async () => {
      try {
        // Clean up users who have been inactive for more than 60 minutes
        const inactivityThreshold = 60 * 60 * 1000; // 60 minutes
        const cleanedCount = await model.cleanupInactiveUsers(inactivityThreshold);
        if (cleanedCount > 0) {
          console.log(`Cleaned up ${cleanedCount} inactive users`);
        }
      } catch (error) {
        console.error('Error during user cleanup:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Set up a periodic cleanup of stale reservations (every 1 minute)
    const reservationCleanupInterval = setInterval(async () => {
      try {
        await model.clearStaleReservations();
        await reservationManager.cleanupExpiredData();
      } catch (error) {
        console.error('Error during reservation cleanup:', error);
      }
    }, 60 * 1000); // 1 minute

    // Initial cleanup of stale reservations
    await model.clearStaleReservations();

    // Handle graceful shutdown
    const gracefulShutdown = async () => {
      console.log('Shutting down server...');

      // Stop the cleanup intervals
      clearInterval(cleanupInterval);
      clearInterval(reservationCleanupInterval);

      // Close the HTTP server
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    };

    // Listen for termination signals
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

    // Start the servers
    if (httpsServer) {
      // Start HTTPS server
      httpsServer.listen(httpsPort, () => {
        console.log(`HTTPS Server listening on https://localhost:${httpsPort}/`);
      });

      // Start HTTP server (independent, no redirect)
      server.listen(httpPort, () => {
        console.log(`HTTP Server listening on http://localhost:${httpPort}/`);
      });
    } else {
      // Start HTTP server only
      server.listen(httpPort, () => {
        console.log(`HTTP Server listening on http://localhost:${httpPort}/`);
      });
    }

  } catch (error) {
    console.error('Error initializing application:', error);
    process.exit(1);
  }
}

// Start the application
init();

/**
 * model.js - Central Data Management Module
 *
 * This module serves as the central "brain" of the application, managing:
 * - In-memory data collections (users, groups, booking lists, timeslots)
 * - Data synchronization with the database
 * - Business logic for reservations, bookings, etc.
 * - Real-time updates via Socket.io
 */

import { getDB } from './db.js';
import User from './models/user.model.js';
import Group from './models/group.model.js';
import Timeslot from './models/timeslot.model.js';
import BookingList from './models/booking-list.model.js';

/**
 * Model class - Central data manager for the application
 */
class Model {
  /**
   * Create a new Model instance
   * Initializes empty collections for all entity types
   */
  constructor() {
    // Collections of entities, indexed by ID for fast lookup
    this.users = {};           // Map of session ID -> User object
    this.students = {};        // Map of student ID -> User object
    this.groups = {};          // Map of group ID -> Group object
    this.bookingLists = {};    // Map of booking list ID -> BookingList object
    this.timeslots = {};       // Map of timeslot ID -> Timeslot object
    this.locations = {};       // Map of location ID -> Location object
    this.examinationTypes = {}; // Map of examination type ID -> ExaminationType object

    // Socket.io instance (set by index.js)
    this.io = null;

    // Reservation manager instance (set by index.js)
    this.reservationManager = null;

    // Reservation timeouts (timeslotId -> timeout)
    this.reservationTimeouts = {};
  }

  /**
   * Set the Socket.io instance for real-time updates
   * @param {object} io - The Socket.io instance
   */
  setIo(io) {
    this.io = io;
  }

  /**
   * Set the reservation manager instance
   * @param {object} reservationManager - The reservation manager instance
   */
  setReservationManager(reservationManager) {
    this.reservationManager = reservationManager;
  }

  /**
   * Load all data from the database
   * This is called during server startup to populate the in-memory collections
   * @returns {Promise<void>} A promise that resolves when all data is loaded
   */
  async loadData() {
    const db = getDB();

    try {
      // Load locations
      const locations = await db.all('SELECT * FROM locations');
      for (const location of locations) {
        this.locations[location.id] = location;
      }
      console.log(`Loaded ${locations.length} locations`);

      // Load examination types
      const examinationTypes = await db.all('SELECT * FROM examination_types');
      for (const type of examinationTypes) {
        this.examinationTypes[type.id] = type;
      }
      console.log(`Loaded ${examinationTypes.length} examination types`);

      // Load booking lists
      const bookingLists = await db.all('SELECT * FROM booking_lists');
      for (const list of bookingLists) {
        this.bookingLists[list.id] = new BookingList(
          list.id,
          list.title,
          list.description,
          list.location_id,
          list.examination_type_id,
          list.booking_start_date,
          list.booking_end_date,
          list.visibility_start_date,
          list.visibility_end_date,
          list.cancellation_deadline,
          list.max_bookings_per_student
        );
      }
      console.log(`Loaded ${bookingLists.length} booking lists`);

      // Load timeslots
      const timeslots = await db.all('SELECT * FROM timeslots');
      for (const slot of timeslots) {
        this.timeslots[slot.id] = new Timeslot(
          slot.id,
          slot.booking_list_id,
          slot.date,
          slot.time,
          slot.location_id,
          Boolean(slot.booked),
          slot.booked_by_student_id,
          slot.booked_by_group_id,
          slot.reserved_until,
          Boolean(slot.cancelled),
          slot.cancelled_at,
          slot.cancelled_by_id
        );
      }
      console.log(`Loaded ${timeslots.length} timeslots`);

      // Load students (not loading passwords)
      const students = await db.all('SELECT id, username, email FROM students');
      for (const student of students) {
        this.students[student.id] = new User(
          student.id,
          student.username,
          student.email
        );
      }
      console.log(`Loaded ${students.length} students`);

      // Load groups
      const groups = await db.all('SELECT * FROM groups');
      for (const group of groups) {
        this.groups[group.id] = new Group(
          group.id,
          group.name
        );
      }
      console.log(`Loaded ${groups.length} groups`);

      // Load group members
      const groupMembers = await db.all('SELECT * FROM group_members');
      for (const member of groupMembers) {
        const group = this.groups[member.group_id];
        const student = this.students[member.student_id];

        if (group && student) {
          group.addMember(member.student_id);
          student.addGroup(member.group_id);
        }
      }
      console.log(`Loaded ${groupMembers.length} group memberships`);

    } catch (error) {
      console.error('Error loading data from database:', error);
      throw error;
    }
  }

  /**
   * Create a new user session
   * @param {string} sessionId - The session ID from express-session
   * @param {string} username - The username of the user
   * @param {number} studentId - The student ID (optional)
   * @returns {User} The created user
   */
  createUser(sessionId, username, studentId = null) {
    const user = new User(studentId, username);
    this.users[sessionId] = user;

    // Save user session to persistent storage
    if (this.reservationManager && studentId) {
      this.reservationManager.saveUserSession(sessionId, {
        studentId,
        username,
        lastActivity: Date.now(),
        reservedTimeslotId: null,
        additionalData: {}
      }).catch(error => {
        console.error('Error saving user session:', error);
      });
    }

    return user;
  }

  /**
   * Find a user by session ID
   * @param {string} sessionId - The session ID
   * @returns {User|undefined} The user, or undefined if not found
   */
  findUserById(sessionId) {
    return this.users[sessionId];
  }

  /**
   * Find a student by ID
   * @param {number} id - The student ID
   * @returns {User|undefined} The student, or undefined if not found
   */
  findStudentById(id) {
    return this.students[id];
  }

  /**
   * Find a student by username
   * @param {string} username - The username to search for
   * @returns {User|undefined} The student, or undefined if not found
   */
  findStudentByUsername(username) {
    return Object.values(this.students).find(student => student.getUsername() === username);
  }

  /**
   * Find a group by ID
   * @param {number} id - The group ID
   * @returns {Group|undefined} The group, or undefined if not found
   */
  findGroupById(id) {
    return this.groups[id];
  }

  /**
   * Find a booking list by ID
   * @param {number} id - The booking list ID
   * @returns {BookingList|undefined} The booking list, or undefined if not found
   */
  findBookingListById(id) {
    return this.bookingLists[id];
  }

  /**
   * Find a timeslot by ID
   * @param {number} id - The timeslot ID
   * @returns {Timeslot|undefined} The timeslot, or undefined if not found
   */
  findTimeslotById(id) {
    return this.timeslots[id];
  }

  /**
   * Find a booking list by timeslot ID
   * @param {number} timeslotId - The ID of the timeslot
   * @returns {BookingList|undefined} The booking list, or undefined if not found
   */
  findBookingListByTimeslotId(timeslotId) {
    // Convert to number to ensure consistent comparison
    const timeslotIdNum = Number(timeslotId);

    // Iterate through all booking lists
    for (const bookingListId in this.bookingLists) {
      const bookingList = this.bookingLists[bookingListId];

      // Check if this booking list contains the timeslot
      const timeslots = this.getTimeslotsForBookingList(Number(bookingListId));
      if (timeslots.some(t => Number(t.getId()) === timeslotIdNum)) {
        return bookingList;
      }
    }

    return undefined;
  }

  /**
   * Get all visible booking lists
   * @returns {BookingList[]} Array of visible booking lists
   */
  getVisibleBookingLists() {
    return Object.values(this.bookingLists).filter(list => list.isVisible());
  }

  /**
   * Get all timeslots for a booking list
   * @param {number} bookingListId - The booking list ID
   * @returns {Timeslot[]} Array of timeslots
   */
  getTimeslotsForBookingList(bookingListId) {
    return Object.values(this.timeslots).filter(
      slot => slot.getBookingListId() === bookingListId
    );
  }

  /**
   * Get all locations
   * @returns {Object[]} Array of locations
   */
  getLocations() {
    return Object.values(this.locations);
  }

  /**
   * Get all examination types
   * @returns {Object[]} Array of examination types
   */
  getExaminationTypes() {
    return Object.values(this.examinationTypes);
  }

  /**
   * Find users who have reserved a specific timeslot
   * @param {number} timeslotId - The timeslot ID
   * @returns {User[]} Array of users who have reserved the timeslot
   */
  findUsersByReservedTimeslot(timeslotId) {
    return Object.values(this.users).filter(
      user => user.getReservedTimeslot() === timeslotId
    );
  }

  /**
   * Clean up inactive users
   * @param {number} inactivityThresholdMs - The inactivity threshold in milliseconds
   * @returns {Promise<number>} The number of users cleaned up
   */
  async cleanupInactiveUsers(inactivityThresholdMs) {
    const userIds = Object.keys(this.users);
    let cleanedCount = 0;

    for (const userId of userIds) {
      const user = this.users[userId];

      if (user.isInactiveSince(inactivityThresholdMs)) {
        // If the user has a reserved timeslot, cancel it
        const reservedTimeslotId = user.getReservedTimeslot();
        if (reservedTimeslotId) {
          try {
            await this.cancelReservation(reservedTimeslotId);
            console.log(`Cancelled reservation ${reservedTimeslotId} for inactive user ${user.getUsername()}`);
          } catch (error) {
            console.error(`Error cancelling reservation for inactive user: ${error.message}`);
          }
        }

        // Remove the user
        delete this.users[userId];
        cleanedCount++;
      }
    }

    console.log(`Cleaned up ${cleanedCount} inactive users`);
    return cleanedCount;
  }

  /**
   * Create a new Timeslot object (without saving to database)
   * @param {number} id - The timeslot ID
   * @param {number} bookingListId - The booking list ID
   * @param {string} date - The date (YYYY-MM-DD)
   * @param {string} time - The time (HH:MM)
   * @param {number} locationId - The location ID
   * @returns {Timeslot} The created timeslot object
   */
  createTimeslotObject(id, bookingListId, date, time, locationId = null) {
    return new Timeslot(
      id,
      bookingListId,
      date,
      time,
      locationId,
      false, // booked
      null,  // bookedByStudentId
      null,  // bookedByGroupId
      null,  // reservedUntil
      false, // cancelled
      null,  // cancelledAt
      null   // cancelledById
    );
  }

  /**
   * Clear stale reservations
   * This is called periodically to clean up expired reservations
   * @returns {Promise<void>} A promise that resolves when all stale reservations are cleared
   */
  async clearStaleReservations() {
    try {
      console.log('Clearing stale reservations...');
      const db = getDB();

      // Update all timeslots with expired reservations
      await db.run(
        'UPDATE timeslots SET reserved_until = NULL WHERE reserved_until < ?',
        [Date.now()]
      );

      // Clear all reservations in memory
      Object.values(this.timeslots).forEach(timeslot => {
        if (timeslot.isReserved() && timeslot.reservedUntil < Date.now()) {
          timeslot.reservedUntil = null;
        }
      });

      // Clear all user reservations
      Object.values(this.users).forEach(user => {
        const reservedTimeslotId = user.getReservedTimeslot();
        if (reservedTimeslotId) {
          const timeslot = this.findTimeslotById(reservedTimeslotId);
          if (!timeslot || !timeslot.isReserved()) {
            console.log(`Clearing stale reservation for user ${user.getUsername()}, timeslot ${reservedTimeslotId}`);
            user.clearReservedTimeslot();
          }
        }
      });

      console.log('Stale reservations cleared');
    } catch (error) {
      console.error('Error clearing stale reservations:', error);
    }
  }
}

// Export a singleton instance of the Model class
export default new Model();

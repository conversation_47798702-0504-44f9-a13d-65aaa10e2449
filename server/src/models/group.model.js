/**
 * Class representing a Group of students
 */
class Group {
  /**
   * Create a group
   * @param {number} id - The group's ID
   * @param {string} name - The name of the group
   */
  constructor(id, name) {
    this.id = id;
    this.name = name;
    this.members = []; // Array of student IDs
  }

  /**
   * Get the ID of the group
   * @returns {number} The ID of the group
   */
  getId() {
    return this.id;
  }

  /**
   * Get the name of the group
   * @returns {string} The name of the group
   */
  getName() {
    return this.name;
  }

  /**
   * Set the name of the group
   * @param {string} name - The new name of the group
   */
  setName(name) {
    this.name = name;
  }

  /**
   * Add a member to the group
   * @param {number} studentId - The ID of the student to add
   */
  addMember(studentId) {
    if (!this.members.includes(studentId)) {
      this.members.push(studentId);
    }
  }

  /**
   * Remove a member from the group
   * @param {number} studentId - The ID of the student to remove
   */
  removeMember(studentId) {
    this.members = this.members.filter(id => id !== studentId);
  }

  /**
   * Get the members of the group
   * @returns {number[]} Array of student IDs
   */
  getMembers() {
    return this.members;
  }

  /**
   * Check if a student is a member of the group
   * @param {number} studentId - The ID of the student to check
   * @returns {boolean} Whether the student is a member of the group
   */
  isMember(studentId) {
    // Convert studentId to a number to ensure consistent comparison
    const studentIdNum = Number(studentId);

    // Check if any member ID (also converted to number) matches the student ID
    return this.members.some(memberId => Number(memberId) === studentIdNum);
  }

  /**
   * Convert the group to a plain object
   * @returns {Object} The group as a plain object
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      members: this.members
    };
  }
}

export default Group;

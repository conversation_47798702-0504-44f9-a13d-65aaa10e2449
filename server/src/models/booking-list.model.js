/**
 * Class representing a Booking List
 */
class BookingList {
  /**
   * Create a booking list
   * @param {number} id - The booking list's ID
   * @param {string} title - The title of the booking list
   * @param {string} description - The description of the booking list
   * @param {number} locationId - The default location ID for timeslots in this list
   * @param {number} examinationTypeId - The examination type ID for this list
   * @param {string} bookingStartDate - The date when booking becomes available (YYYY-MM-DD)
   * @param {string} bookingEndDate - The date when booking closes (YYYY-MM-DD)
   * @param {string} visibilityStartDate - The date when the list becomes visible (YYYY-MM-DD)
   * @param {string} visibilityEndDate - The date when the list becomes hidden (YYYY-MM-DD)
   * @param {string} cancellationDeadline - The last date for cancellation (YYYY-MM-DD)
   * @param {number} maxBookingsPerStudent - The maximum number of bookings per student/group
   */
  constructor(
    id,
    title,
    description = null,
    locationId = null,
    examinationTypeId = null,
    bookingStartDate = null,
    bookingEndDate = null,
    visibilityStartDate = null,
    visibilityEndDate = null,
    cancellationDeadline = null,
    maxBookingsPerStudent = 1
  ) {
    this.id = id;
    this.title = title;
    this.description = description;
    this.locationId = locationId;
    this.examinationTypeId = examinationTypeId;
    this.bookingStartDate = bookingStartDate;
    this.bookingEndDate = bookingEndDate;
    this.visibilityStartDate = visibilityStartDate;
    this.visibilityEndDate = visibilityEndDate;
    this.cancellationDeadline = cancellationDeadline;
    this.maxBookingsPerStudent = maxBookingsPerStudent;
  }

  /**
   * Get the ID of the booking list
   * @returns {number} The ID of the booking list
   */
  getId() {
    return this.id;
  }

  /**
   * Get the title of the booking list
   * @returns {string} The title of the booking list
   */
  getTitle() {
    return this.title;
  }

  /**
   * Set the title of the booking list
   * @param {string} title - The new title of the booking list
   */
  setTitle(title) {
    this.title = title;
  }

  /**
   * Get the description of the booking list
   * @returns {string|null} The description of the booking list, or null if not set
   */
  getDescription() {
    return this.description;
  }

  /**
   * Set the description of the booking list
   * @param {string} description - The new description of the booking list
   */
  setDescription(description) {
    this.description = description;
  }

  /**
   * Check if booking is currently allowed
   * @returns {boolean} Whether booking is currently allowed
   */
  isBookingAllowed() {
    // Get current date as YYYY-MM-DD
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // If no booking start/end dates are set, booking is always allowed
    if (!this.bookingStartDate && !this.bookingEndDate) {
      return true;
    }

    try {
      // If only start date is set, check if today is after start date
      if (this.bookingStartDate && !this.bookingEndDate) {
        // Handle different date formats by converting to Date objects
        const startDate = new Date(this.bookingStartDate);
        return today >= startDate;
      }

      // If only end date is set, check if today is before end date
      if (!this.bookingStartDate && this.bookingEndDate) {
        const endDate = new Date(this.bookingEndDate);
        return today <= endDate;
      }

      // If both dates are set, check if today is between them
      const startDate = new Date(this.bookingStartDate);
      const endDate = new Date(this.bookingEndDate);
      return today >= startDate && today <= endDate;
    } catch (error) {
      console.error('Error comparing dates:', error);
      // Default to allowing booking if there's an error
      return true;
    }
  }

  /**
   * Check if the booking list is currently visible
   * @returns {boolean} Whether the booking list is currently visible
   */
  isVisible() {
    // Get current date
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // If no visibility start/end dates are set, the list is always visible
    if (!this.visibilityStartDate && !this.visibilityEndDate) {
      return true;
    }

    try {
      // If only start date is set, check if today is after start date
      if (this.visibilityStartDate && !this.visibilityEndDate) {
        const startDate = new Date(this.visibilityStartDate);
        return today >= startDate;
      }

      // If only end date is set, check if today is before end date
      if (!this.visibilityStartDate && this.visibilityEndDate) {
        const endDate = new Date(this.visibilityEndDate);
        return today <= endDate;
      }

      // If both dates are set, check if today is between them
      const startDate = new Date(this.visibilityStartDate);
      const endDate = new Date(this.visibilityEndDate);
      return today >= startDate && today <= endDate;
    } catch (error) {
      console.error('Error comparing visibility dates:', error);
      // Default to visible if there's an error
      return true;
    }
  }

  /**
   * Check if cancellation is currently allowed
   * @returns {boolean} Whether cancellation is currently allowed
   */
  isCancellationAllowed() {
    // If no cancellation deadline is set, cancellation is always allowed
    if (!this.cancellationDeadline) {
      return true;
    }

    try {
      // Check if today is before the cancellation deadline
      const today = new Date();
      const deadline = new Date(this.cancellationDeadline);
      return today <= deadline;
    } catch (error) {
      console.error('Error comparing cancellation deadline:', error);
      // Default to allowing cancellation if there's an error
      return true;
    }
  }

  /**
   * Convert the booking list to a plain object
   * @returns {Object} The booking list as a plain object
   */
  toJSON() {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      locationId: this.locationId,
      examinationTypeId: this.examinationTypeId,
      bookingStartDate: this.bookingStartDate,
      bookingEndDate: this.bookingEndDate,
      visibilityStartDate: this.visibilityStartDate,
      visibilityEndDate: this.visibilityEndDate,
      cancellationDeadline: this.cancellationDeadline,
      maxBookingsPerStudent: this.maxBookingsPerStudent,
      bookingAllowed: this.isBookingAllowed(),
      visible: this.isVisible(),
      cancellationAllowed: this.isCancellationAllowed()
    };
  }
}

export default BookingList;

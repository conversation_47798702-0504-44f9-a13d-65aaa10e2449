/**
 * Class representing a Timeslot
 */
class Timeslot {
  /**
   * Create a timeslot
   * @param {number} id - The timeslot's ID
   * @param {number} bookingListId - The ID of the booking list this timeslot belongs to
   * @param {string} date - The date of the timeslot (YYYY-MM-DD)
   * @param {string} time - The time of the timeslot (HH:MM)
   * @param {number} locationId - The ID of the location (optional, overrides booking list default)
   * @param {boolean} booked - Whether the timeslot is booked
   * @param {number} bookedByStudentId - The ID of the student who booked the timeslot
   * @param {number} bookedByGroupId - The ID of the group that booked the timeslot
   * @param {number} reservedUntil - Timestamp when the reservation expires
   * @param {boolean} cancelled - Whether the booking was cancelled
   * @param {string} cancelledAt - When the booking was cancelled
   * @param {number} cancelledById - Who cancelled the booking
   */
  constructor(
    id,
    bookingListId,
    date,
    time,
    locationId = null,
    booked = false,
    bookedByStudentId = null,
    bookedByGroupId = null,
    reservedUntil = null,
    cancelled = false,
    cancelledAt = null,
    cancelledById = null
  ) {
    // Core properties
    this.id = id;
    this.bookingListId = bookingListId;
    this.date = date || new Date().toISOString().split('T')[0]; // Default to today if not provided
    this.time = time;
    this.locationId = locationId;

    // Booking state properties
    this.booked = booked;
    this.bookedByStudentId = bookedByStudentId;
    this.bookedByGroupId = bookedByGroupId;
    this.reservedUntil = reservedUntil;

    // Cancellation properties
    this.cancelled = cancelled;
    this.cancelledAt = cancelledAt;
    this.cancelledById = cancelledById;
  }

  /**
   * Get the ID of the timeslot
   * @returns {number} The ID of the timeslot
   */
  getId() {
    return this.id;
  }

  /**
   * Get the booking list ID of the timeslot
   * @returns {number} The booking list ID
   */
  getBookingListId() {
    return this.bookingListId;
  }

  /**
   * Get the date of the timeslot
   * @returns {string} The date in YYYY-MM-DD format
   */
  getDate() {
    return this.date;
  }

  /**
   * Get the time of the timeslot
   * @returns {string} The time in HH:MM format
   */
  getTime() {
    return this.time;
  }

  /**
   * Get the location ID of the timeslot
   * @returns {number|null} The location ID, or null if not set
   */
  getLocationId() {
    return this.locationId;
  }

  /**
   * Set the location ID of the timeslot
   * @param {number} locationId - The new location ID
   */
  setLocationId(locationId) {
    this.locationId = locationId;
  }

  /**
   * Check if the timeslot is booked
   * @returns {boolean} Whether the timeslot is booked and not cancelled
   */
  isBooked() {
    return this.booked && !this.cancelled;
  }

  /**
   * Check if the timeslot is reserved
   * @returns {boolean} Whether the timeslot is currently reserved
   */
  isReserved() {
    // A timeslot is reserved if it has a reservation timestamp in the future
    return this.reservedUntil !== null && this.reservedUntil > Date.now();
  }

  /**
   * Check if the timeslot is cancelled
   * @returns {boolean} Whether the timeslot is cancelled
   */
  isCancelled() {
    return this.cancelled;
  }

  /**
   * Permanently book the timeslot
   * @param {number} studentId - The ID of the student booking the timeslot
   * @param {number} groupId - The ID of the group booking the timeslot (optional)
   */
  book(studentId, groupId = null) {
    this.booked = true;
    this.bookedByStudentId = studentId;
    this.bookedByGroupId = groupId;
    this.reservedUntil = null; // Clear any existing reservation
  }

  /**
   * Reserve the timeslot for a limited time
   * @param {number} durationMs - The duration of the reservation in milliseconds
   */
  reserve(durationMs) {
    this.reservedUntil = Date.now() + durationMs;
  }

  /**
   * Cancel the booking
   * @param {number} cancelledById - The ID of the student who cancelled the booking
   */
  cancel(cancelledById) {
    // Explicitly set cancelled to true
    this.cancelled = true;

    // Set cancellation timestamp
    this.cancelledAt = new Date().toISOString();

    // Set the ID of the student who cancelled the booking
    this.cancelledById = cancelledById;

    // Keep the booking information for history, but mark it as cancelled
    // The booked flag remains true to indicate it was booked at some point

    console.log(`Timeslot ${this.id} cancelled by student ${cancelledById} at ${this.cancelledAt}`);
    console.log(`Timeslot cancelled status: ${this.cancelled}`);
  }

  /**
   * Convert the timeslot to a plain object
   * @returns {Object} The timeslot as a plain object
   */
  toJSON() {
    // Determine if the timeslot is currently reserved
    const isCurrentlyReserved = this.isReserved();

    // Only include the reservation timestamp if it's still valid
    const effectiveReservedUntil = isCurrentlyReserved ? this.reservedUntil : null;

    // Create the JSON representation
    const json = {
      id: this.id,
      bookingListId: this.bookingListId,
      date: this.date,
      time: this.time,
      locationId: this.locationId,
      booked: this.booked,
      bookedByStudentId: this.bookedByStudentId,
      bookedByGroupId: this.bookedByGroupId,
      reserved: isCurrentlyReserved,
      reservedUntil: effectiveReservedUntil,

      // Always explicitly include the cancelled flag
      cancelled: this.cancelled === true,

      // Include cancellation details if available
      cancelledAt: this.cancelledAt,
      cancelledById: this.cancelledById
    };

    console.log(`Timeslot ${this.id} toJSON - cancelled status: ${json.cancelled}`);

    return json;
  }
}

export default Timeslot;

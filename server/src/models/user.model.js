/**
 * Class representing a User (student)
 */
class User {
  /**
   * Create a user
   * @param {number} id - The user's ID
   * @param {string} username - The username of the user
   * @param {string} email - The email of the user (optional)
   */
  constructor(id, username, email = null) {
    this.id = id;
    this.username = username;
    this.email = email;
    this.reservedTimeslot = null;
    this.lastActivity = Date.now();
    this.groups = []; // Groups the user belongs to
  }

  /**
   * Get the ID of the user
   * @returns {number} The ID of the user
   */
  getId() {
    return this.id;
  }

  /**
   * Get the username of the user
   * @returns {string} The username of the user
   */
  getUsername() {
    return this.username;
  }

  /**
   * Get the email of the user
   * @returns {string|null} The email of the user, or null if not set
   */
  getEmail() {
    return this.email;
  }

  /**
   * Set the email of the user
   * @param {string} email - The new email of the user
   */
  setEmail(email) {
    this.email = email;
  }

  /**
   * Add a group to the user's groups
   * @param {number} groupId - The ID of the group
   */
  addGroup(groupId) {
    if (!this.groups.includes(groupId)) {
      this.groups.push(groupId);
    }
  }

  /**
   * Remove a group from the user's groups
   * @param {number} groupId - The ID of the group
   */
  removeGroup(groupId) {
    this.groups = this.groups.filter(id => id !== groupId);
  }

  /**
   * Get the groups the user belongs to
   * @returns {number[]} Array of group IDs
   */
  getGroups() {
    return this.groups;
  }

  /**
   * Reserve a timeslot for this user
   * @param {number} timeslotId - The ID of the timeslot
   */
  reserveTimeslot(timeslotId) {
    this.reservedTimeslot = timeslotId;
    this.updateActivity();
  }

  /**
   * Clear the reserved timeslot
   */
  clearReservedTimeslot() {
    this.reservedTimeslot = null;
    this.updateActivity();
  }

  /**
   * Get the reserved timeslot ID
   * @returns {number|null} The ID of the reserved timeslot, or null if none
   */
  getReservedTimeslot() {
    return this.reservedTimeslot;
  }

  /**
   * Update the last activity timestamp
   */
  updateActivity() {
    this.lastActivity = Date.now();
  }

  /**
   * Check if the user has been inactive for a specified duration
   * @param {number} durationMs - The duration in milliseconds
   * @returns {boolean} Whether the user has been inactive for the specified duration
   */
  isInactiveSince(durationMs) {
    return Date.now() - this.lastActivity > durationMs;
  }

  /**
   * Convert the user to a plain object
   * @returns {Object} The user as a plain object
   */
  toJSON() {
    return {
      id: this.id,
      username: this.username,
      email: this.email,
      groups: this.groups,
      reservedTimeslot: this.reservedTimeslot,
      lastActivity: this.lastActivity
    };
  }
}

export default User;

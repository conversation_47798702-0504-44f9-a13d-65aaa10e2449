/**
 * liveState.js - Middleware for tracking live user interactions
 * 
 * Saves temporary user state like form data, current page, etc.
 * so it can be restored after server restart
 */

/**
 * Middleware to save live interaction state
 */
export function saveLiveInteraction(interactionType) {
  return async (req, res, next) => {
    if (req.session && req.session.id && req.method === 'POST') {
      const sessionId = req.session.id;
      
      // Save the interaction data
      const interactionData = {
        type: interactionType,
        data: {
          url: req.originalUrl,
          method: req.method,
          body: req.body,
          timestamp: Date.now()
        }
      };

      // Save to persistent storage if available
      if (req.app.locals.persistentStorage) {
        try {
          await req.app.locals.persistentStorage.saveLiveInteraction(sessionId, interactionData);
        } catch (error) {
          console.error('Error saving live interaction:', error);
        }
      }
    }
    
    next();
  };
}

/**
 * Middleware to track page navigation
 */
export function trackPageNavigation(req, _res, next) {
  if (req.session && req.session.id && req.session.studentId) {
    const sessionId = req.session.id;
    const currentPage = req.originalUrl;

    // Update current page in persistent storage
    if (req.app.locals.persistentStorage) {
      req.app.locals.persistentStorage.updateUserSession(sessionId, currentPage).catch(error => {
        console.error('Error updating page navigation:', error);
      });
    }
  }

  next();
}

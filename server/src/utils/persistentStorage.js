/**
 * persistentStorage.js - Enhanced Persistent Storage for Live Resources
 * 
 * Ensures no data is lost during server restarts by persisting:
 * - Active reservations with exact timestamps
 * - User sessions and their state
 * - Live interactions and temporary data
 */

import { getDB } from '../db.js';

class PersistentStorageManager {
  constructor() {
    this.activeReservations = new Map(); // timeslotId -> reservation details
    this.userSessions = new Map(); // sessionId -> user state
    this.liveInteractions = new Map(); // sessionId -> interaction state
  }

  /**
   * Save active reservation to persistent storage
   */
  async saveReservation(timeslotId, sessionId, studentId, expiresAt) {
    const db = getDB();
    
    try {
      await db.run(
        `INSERT OR REPLACE INTO active_reservations 
         (timeslot_id, session_id, student_id, expires_at, created_at) 
         VALUES (?, ?, ?, ?, ?)`,
        [timeslotId, sessionId, studentId, expiresAt, Date.now()]
      );

      this.activeReservations.set(timeslotId, {
        sessionId,
        studentId,
        expiresAt,
        createdAt: Date.now()
      });

      console.log(`✅ Reservation persisted: timeslot ${timeslotId} by student ${studentId}`);
    } catch (error) {
      console.error('❌ Error saving reservation:', error);
      throw error;
    }
  }

  /**
   * Remove reservation from persistent storage
   */
  async removeReservation(timeslotId) {
    const db = getDB();
    
    try {
      await db.run('DELETE FROM active_reservations WHERE timeslot_id = ?', [timeslotId]);
      this.activeReservations.delete(timeslotId);
      console.log(`✅ Reservation removed: timeslot ${timeslotId}`);
    } catch (error) {
      console.error('❌ Error removing reservation:', error);
      throw error;
    }
  }

  /**
   * Save user session state for persistence across restarts
   */
  async saveUserSession(sessionId, userState) {
    const db = getDB();
    
    try {
      await db.run(
        `INSERT OR REPLACE INTO user_sessions 
         (session_id, student_id, username, last_activity, current_page, state_data) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          sessionId,
          userState.studentId,
          userState.username,
          userState.lastActivity || Date.now(),
          userState.currentPage || '/',
          JSON.stringify(userState.additionalData || {})
        ]
      );

      this.userSessions.set(sessionId, userState);
      console.log(`✅ User session persisted: ${userState.username}`);
    } catch (error) {
      console.error('❌ Error saving user session:', error);
      throw error;
    }
  }

  /**
   * Save live interaction state (e.g., form data being filled)
   */
  async saveLiveInteraction(sessionId, interactionData) {
    const db = getDB();
    
    try {
      await db.run(
        `INSERT OR REPLACE INTO live_interactions 
         (session_id, interaction_type, data, last_updated) 
         VALUES (?, ?, ?, ?)`,
        [
          sessionId,
          interactionData.type,
          JSON.stringify(interactionData.data),
          Date.now()
        ]
      );

      this.liveInteractions.set(sessionId, interactionData);
    } catch (error) {
      console.error('❌ Error saving live interaction:', error);
    }
  }

  /**
   * Load all persistent data on server startup
   */
  async loadAllPersistentData() {
    const db = getDB();
    
    try {
      // Load active reservations
      const reservations = await db.all('SELECT * FROM active_reservations');
      let loadedReservations = 0;
      
      for (const reservation of reservations) {
        if (reservation.expires_at > Date.now()) {
          this.activeReservations.set(reservation.timeslot_id, {
            sessionId: reservation.session_id,
            studentId: reservation.student_id,
            expiresAt: reservation.expires_at,
            createdAt: reservation.created_at
          });
          loadedReservations++;
        } else {
          // Clean up expired reservations
          await db.run('DELETE FROM active_reservations WHERE timeslot_id = ?', [reservation.timeslot_id]);
        }
      }

      // Load user sessions
      const sessions = await db.all('SELECT * FROM user_sessions');
      let loadedSessions = 0;
      
      for (const session of sessions) {
        // Load sessions from last 48 hours
        if (session.last_activity > Date.now() - (48 * 60 * 60 * 1000)) {
          this.userSessions.set(session.session_id, {
            studentId: session.student_id,
            username: session.username,
            lastActivity: session.last_activity,
            currentPage: session.current_page,
            additionalData: JSON.parse(session.state_data || '{}')
          });
          loadedSessions++;
        } else {
          // Clean up old sessions
          await db.run('DELETE FROM user_sessions WHERE session_id = ?', [session.session_id]);
        }
      }

      // Load live interactions
      const interactions = await db.all('SELECT * FROM live_interactions WHERE last_updated > ?', [Date.now() - (60 * 60 * 1000)]); // Last hour
      let loadedInteractions = 0;
      
      for (const interaction of interactions) {
        this.liveInteractions.set(interaction.session_id, {
          type: interaction.interaction_type,
          data: JSON.parse(interaction.data),
          lastUpdated: interaction.last_updated
        });
        loadedInteractions++;
      }

      console.log(`🔄 Persistent data loaded:`);
      console.log(`   📋 ${loadedReservations} active reservations`);
      console.log(`   👤 ${loadedSessions} user sessions`);
      console.log(`   🔄 ${loadedInteractions} live interactions`);
      
      return {
        reservations: loadedReservations,
        sessions: loadedSessions,
        interactions: loadedInteractions
      };
    } catch (error) {
      console.error('❌ Error loading persistent data:', error);
      throw error;
    }
  }

  /**
   * Cleanup expired data
   */
  async cleanupExpiredData() {
    const db = getDB();
    const now = Date.now();
    
    try {
      // Clean expired reservations
      const expiredReservations = [];
      for (const [timeslotId, reservation] of this.activeReservations) {
        if (reservation.expiresAt <= now) {
          expiredReservations.push(timeslotId);
        }
      }

      for (const timeslotId of expiredReservations) {
        await this.removeReservation(timeslotId);
      }

      // Clean old sessions (older than 48 hours)
      await db.run('DELETE FROM user_sessions WHERE last_activity < ?', [now - (48 * 60 * 60 * 1000)]);
      
      // Clean old interactions (older than 1 hour)
      await db.run('DELETE FROM live_interactions WHERE last_updated < ?', [now - (60 * 60 * 1000)]);

      if (expiredReservations.length > 0) {
        console.log(`🧹 Cleaned up ${expiredReservations.length} expired reservations`);
      }
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
  }

  /**
   * Get active reservation for timeslot
   */
  getReservation(timeslotId) {
    const reservation = this.activeReservations.get(timeslotId);
    if (reservation && reservation.expiresAt > Date.now()) {
      return reservation;
    }
    return null;
  }

  /**
   * Get user session
   */
  getUserSession(sessionId) {
    return this.userSessions.get(sessionId) || null;
  }

  /**
   * Check if timeslot is reserved
   */
  isTimeslotReserved(timeslotId) {
    const reservation = this.getReservation(timeslotId);
    return reservation !== null;
  }

  /**
   * Get all active reservations
   */
  getAllActiveReservations() {
    const active = new Map();
    for (const [timeslotId, reservation] of this.activeReservations) {
      if (reservation.expiresAt > Date.now()) {
        active.set(timeslotId, reservation);
      }
    }
    return active;
  }
}

// Export singleton instance
export default new PersistentStorageManager();

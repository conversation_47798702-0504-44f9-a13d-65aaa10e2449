/**
 * sessionStore.js - Custom Express Session Store for Persistent Sessions
 * 
 * This ensures that Express sessions survive server restarts by storing
 * them in SQLite database instead of memory
 */

import { Store } from 'express-session';
import { getDB } from '../db.js';

class SQLiteSessionStore extends Store {
  constructor(options = {}) {
    super(options);
    this.tableName = options.tableName || 'express_sessions';
    this.initialized = false;
  }

  /**
   * Create the sessions table if it doesn't exist
   */
  async createTable() {
    if (this.initialized) return;

    try {
      const db = getDB();
      await db.run(`
        CREATE TABLE IF NOT EXISTS ${this.tableName} (
          sid TEXT PRIMARY KEY,
          sess TEXT NOT NULL,
          expired INTEGER NOT NULL
        )
      `);
      this.initialized = true;
      console.log('✅ Express session store table ready');
    } catch (error) {
      console.error('❌ Error creating session store table:', error);
    }
  }

  /**
   * Ensure table is created before operations
   */
  async ensureTable() {
    if (!this.initialized) {
      await this.createTable();
    }
  }

  /**
   * Get a session from the database
   */
  async get(sid, callback) {
    await this.ensureTable();
    const db = getDB();
    
    db.get(
      `SELECT sess FROM ${this.tableName} WHERE sid = ? AND expired > ?`,
      [sid, Date.now()]
    )
    .then(row => {
      if (row) {
        try {
          const session = JSON.parse(row.sess);
          console.log(`🔄 Retrieved session: ${sid.substring(0, 8)}... for user ${session.username || 'unknown'} (studentId: ${session.studentId})`);
          callback(null, session);
        } catch (error) {
          console.error('❌ Error parsing session:', error);
          callback(error);
        }
      } else {
        console.log(`❌ No session found for: ${sid.substring(0, 8)}...`);
        callback(null, null);
      }
    })
    .catch(error => {
      console.error('❌ Error getting session:', error);
      callback(error);
    });
  }

  /**
   * Save a session to the database
   */
  async set(sid, session, callback) {
    await this.ensureTable();
    const db = getDB();
    
    // Calculate expiration time
    const maxAge = session.cookie.maxAge || (24 * 60 * 60 * 1000); // 24 hours default
    const expired = Date.now() + maxAge;
    
    db.run(
      `INSERT OR REPLACE INTO ${this.tableName} (sid, sess, expired) VALUES (?, ?, ?)`,
      [sid, JSON.stringify(session), expired]
    )
    .then(() => {
      console.log(`💾 Saved session: ${sid.substring(0, 8)}... for user ${session.username || 'unknown'}`);
      if (callback) callback(null);
    })
    .catch(error => {
      console.error('❌ Error saving session:', error);
      if (callback) callback(error);
    });
  }

  /**
   * Destroy a session
   */
  destroy(sid, callback) {
    const db = getDB();
    
    db.run(`DELETE FROM ${this.tableName} WHERE sid = ?`, [sid])
    .then(() => {
      console.log(`🗑️ Destroyed session: ${sid.substring(0, 8)}...`);
      if (callback) callback(null);
    })
    .catch(error => {
      console.error('❌ Error destroying session:', error);
      if (callback) callback(error);
    });
  }

  /**
   * Touch a session (update expiration)
   */
  touch(sid, session, callback) {
    const db = getDB();
    
    const maxAge = session.cookie.maxAge || (24 * 60 * 60 * 1000);
    const expired = Date.now() + maxAge;
    
    db.run(
      `UPDATE ${this.tableName} SET expired = ? WHERE sid = ?`,
      [expired, sid]
    )
    .then(() => {
      if (callback) callback(null);
    })
    .catch(error => {
      console.error('❌ Error touching session:', error);
      if (callback) callback(error);
    });
  }

  /**
   * Get all session IDs
   */
  all(callback) {
    const db = getDB();
    
    db.all(
      `SELECT sid FROM ${this.tableName} WHERE expired > ?`,
      [Date.now()]
    )
    .then(rows => {
      const sids = rows.map(row => row.sid);
      callback(null, sids);
    })
    .catch(error => {
      console.error('❌ Error getting all sessions:', error);
      callback(error);
    });
  }

  /**
   * Get session count
   */
  length(callback) {
    const db = getDB();
    
    db.get(
      `SELECT COUNT(*) as count FROM ${this.tableName} WHERE expired > ?`,
      [Date.now()]
    )
    .then(row => {
      callback(null, row.count);
    })
    .catch(error => {
      console.error('❌ Error getting session count:', error);
      callback(error);
    });
  }

  /**
   * Clear all sessions
   */
  clear(callback) {
    const db = getDB();
    
    db.run(`DELETE FROM ${this.tableName}`)
    .then(() => {
      console.log('🧹 Cleared all sessions');
      if (callback) callback(null);
    })
    .catch(error => {
      console.error('❌ Error clearing sessions:', error);
      if (callback) callback(error);
    });
  }

  /**
   * Clean up expired sessions
   */
  async cleanup() {
    const db = getDB();
    
    try {
      const result = await db.run(
        `DELETE FROM ${this.tableName} WHERE expired <= ?`,
        [Date.now()]
      );
      
      if (result.changes > 0) {
        console.log(`🧹 Cleaned up ${result.changes} expired sessions`);
      }
    } catch (error) {
      console.error('❌ Error cleaning up sessions:', error);
    }
  }
}

export default SQLiteSessionStore;

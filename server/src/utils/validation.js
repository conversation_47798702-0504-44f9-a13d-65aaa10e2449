/**
 * validation.js - Input Validation and Sanitization Utilities
 * 
 * This module provides utilities for validating and sanitizing user input
 * to prevent XSS attacks and ensure data integrity.
 */

import { body, param, query, validationResult } from 'express-validator';
import DOMPurify from 'dompurify';
import { <PERSON><PERSON><PERSON> } from 'jsdom';

// Create a DOMPurify instance for server-side use
const window = new JSDOM('').window;
const purify = DOMPurify(window);

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param {string} input - The input string to sanitize
 * @returns {string} - The sanitized string
 */
export function sanitizeHtml(input) {
  if (typeof input !== 'string') {
    return input;
  }
  
  // Remove all HTML tags and decode entities
  return purify.sanitize(input, { 
    ALLOWED_TAGS: [], 
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true 
  });
}

/**
 * Escape HTML entities to prevent XSS
 * @param {string} input - The input string to escape
 * @returns {string} - The escaped string
 */
export function escapeHtml(input) {
  if (typeof input !== 'string') {
    return input;
  }
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Validation middleware to check for validation errors
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next function
 */
export function handleValidationErrors(req, res, next) {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
}

/**
 * Validation rules for user registration
 */
export const validateRegistration = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username can only contain letters, numbers, underscores, and hyphens')
    .customSanitizer(sanitizeHtml),
  
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('Password must be between 6 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Must be a valid email address')
    .normalizeEmail()
    .customSanitizer(sanitizeHtml)
];

/**
 * Validation rules for user login
 */
export const validateLogin = [
  body('username')
    .notEmpty()
    .withMessage('Username is required')
    .customSanitizer(sanitizeHtml),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

/**
 * Validation rules for group creation
 */
export const validateGroupCreation = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('Group name must be between 1 and 100 characters')
    .customSanitizer(sanitizeHtml)
];

/**
 * Validation rules for booking list creation
 */
export const validateBookingListCreation = [
  body('title')
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters')
    .customSanitizer(sanitizeHtml),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters')
    .customSanitizer(sanitizeHtml),
  
  body('locationId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Location ID must be a positive integer'),
  
  body('examinationTypeId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Examination type ID must be a positive integer'),
  
  body('maxBookingsPerStudent')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Max bookings per student must be between 1 and 10')
];

/**
 * Validation rules for timeslot creation
 */
export const validateTimeslotCreation = [
  body('bookingListId')
    .isInt({ min: 1 })
    .withMessage('Booking list ID must be a positive integer'),
  
  body('date')
    .matches(/^\d{4}-\d{2}-\d{2}$/)
    .withMessage('Date must be in YYYY-MM-DD format')
    .isISO8601()
    .withMessage('Date must be a valid date'),
  
  body('time')
    .matches(/^\d{1,2}:\d{2}$/)
    .withMessage('Time must be in HH:MM format'),
  
  body('locationId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Location ID must be a positive integer')
];

/**
 * Validation rules for location creation
 */
export const validateLocationCreation = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('Location name must be between 1 and 100 characters')
    .customSanitizer(sanitizeHtml),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters')
    .customSanitizer(sanitizeHtml)
];

/**
 * Validation rules for examination type creation
 */
export const validateExaminationTypeCreation = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('Examination type name must be between 1 and 100 characters')
    .customSanitizer(sanitizeHtml),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters')
    .customSanitizer(sanitizeHtml)
];

/**
 * Validation rules for ID parameters
 */
export const validateIdParam = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID must be a positive integer')
];

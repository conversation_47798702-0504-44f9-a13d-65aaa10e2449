/**
 * validation.js - Input Validation and Sanitization for XSS Protection
 */

import { body, validationResult } from 'express-validator';
import DOMPurify from 'dompurify';
import { JSDOM } from 'jsdom';

// Create DOMPurify instance for server-side use
const window = new JSDOM('').window;
const purify = DOMPurify(window);

/**
 * Escape HTML entities to prevent XSS
 * Converts <script> to &lt;script&gt;
 */
export function escapeHtml(input) {
  if (typeof input !== 'string') {
    return input;
  }
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Sanitize HTML content using DOMPurify
 */
export function sanitizeHtml(input) {
  if (typeof input !== 'string') {
    return input;
  }
  
  // Remove all HTML tags and keep only text content
  return purify.sanitize(input, { 
    ALLOWED_TAGS: [], 
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true 
  });
}

/**
 * Middleware to handle validation errors
 */
export function handleValidationErrors(req, res, next) {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
}

/**
 * Validation rules for user registration
 */
export const validateRegistration = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username can only contain letters, numbers, underscores, and hyphens')
    .customSanitizer(sanitizeHtml),
  
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('Password must be between 6 and 128 characters'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Must be a valid email address')
    .normalizeEmail()
    .customSanitizer(sanitizeHtml)
];

/**
 * Validation rules for user login
 */
export const validateLogin = [
  body('username')
    .notEmpty()
    .withMessage('Username is required')
    .customSanitizer(sanitizeHtml),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

/**
 * Validation rules for group creation
 */
export const validateGroupCreation = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('Group name must be between 1 and 100 characters')
    .customSanitizer(sanitizeHtml)
];

/**
 * Validation rules for booking list creation
 */
export const validateBookingListCreation = [
  body('title')
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters')
    .customSanitizer(sanitizeHtml),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters')
    .customSanitizer(sanitizeHtml)
];

/**
 * Apply XSS protection to all string fields in request body
 */
export function protectFromXSS(req, res, next) {
  if (req.body && typeof req.body === 'object') {
    for (const key in req.body) {
      if (typeof req.body[key] === 'string' && key !== 'password') {
        req.body[key] = escapeHtml(req.body[key]);
      }
    }
  }
  next();
}

/**
 * reservationManager.js - Enhanced Reservation Management
 * 
 * This module manages "live" resources that need to persist across server restarts.
 * It ensures that no data is lost when the server restarts, including:
 * - Active reservations with exact timestamps
 * - User session states
 * - Temporary booking states
 * - Real-time interaction states
 */

import { getDB } from '../db.js';

/**
 * ReservationManager class for handling persistent live resources
 */
class ReservationManager {
  constructor() {
    this.activeReservations = new Map(); // timeslotId -> reservation details
    this.userSessions = new Map(); // sessionId -> user state
    this.temporaryBookings = new Map(); // bookingId -> temporary booking state
  }

  /**
   * Save a reservation to persistent storage
   * @param {number} timeslotId - The timeslot ID
   * @param {string} sessionId - The user's session ID
   * @param {number} studentId - The student ID
   * @param {number} expiresAt - Timestamp when reservation expires
   */
  async saveReservation(timeslotId, sessionId, studentId, expiresAt) {
    const db = getDB();
    
    try {
      // Save to database for persistence across restarts
      await db.run(
        `INSERT OR REPLACE INTO active_reservations 
         (timeslot_id, session_id, student_id, expires_at, created_at) 
         VALUES (?, ?, ?, ?, ?)`,
        [timeslotId, sessionId, studentId, expiresAt, Date.now()]
      );

      // Keep in memory for fast access
      this.activeReservations.set(timeslotId, {
        sessionId,
        studentId,
        expiresAt,
        createdAt: Date.now()
      });

      console.log(`Reservation saved: timeslot ${timeslotId} by student ${studentId} until ${new Date(expiresAt)}`);
    } catch (error) {
      console.error('Error saving reservation:', error);
      throw error;
    }
  }

  /**
   * Remove a reservation from persistent storage
   * @param {number} timeslotId - The timeslot ID
   */
  async removeReservation(timeslotId) {
    const db = getDB();
    
    try {
      await db.run('DELETE FROM active_reservations WHERE timeslot_id = ?', [timeslotId]);
      this.activeReservations.delete(timeslotId);
      console.log(`Reservation removed: timeslot ${timeslotId}`);
    } catch (error) {
      console.error('Error removing reservation:', error);
      throw error;
    }
  }

  /**
   * Get a reservation
   * @param {number} timeslotId - The timeslot ID
   * @returns {Object|null} - Reservation details or null
   */
  getReservation(timeslotId) {
    return this.activeReservations.get(timeslotId) || null;
  }

  /**
   * Save user session state for persistence
   * @param {string} sessionId - The session ID
   * @param {Object} userState - The user state to save
   */
  async saveUserSession(sessionId, userState) {
    const db = getDB();
    
    try {
      await db.run(
        `INSERT OR REPLACE INTO user_sessions 
         (session_id, student_id, username, last_activity, reserved_timeslot_id, state_data) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          sessionId,
          userState.studentId,
          userState.username,
          userState.lastActivity,
          userState.reservedTimeslotId,
          JSON.stringify(userState.additionalData || {})
        ]
      );

      this.userSessions.set(sessionId, userState);
      console.log(`User session saved: ${sessionId} for ${userState.username}`);
    } catch (error) {
      console.error('Error saving user session:', error);
      throw error;
    }
  }

  /**
   * Remove user session
   * @param {string} sessionId - The session ID
   */
  async removeUserSession(sessionId) {
    const db = getDB();
    
    try {
      await db.run('DELETE FROM user_sessions WHERE session_id = ?', [sessionId]);
      this.userSessions.delete(sessionId);
      console.log(`User session removed: ${sessionId}`);
    } catch (error) {
      console.error('Error removing user session:', error);
      throw error;
    }
  }

  /**
   * Load all persistent data from database on server startup
   */
  async loadPersistentData() {
    const db = getDB();
    
    try {
      // Load active reservations
      const reservations = await db.all('SELECT * FROM active_reservations');
      for (const reservation of reservations) {
        // Only load non-expired reservations
        if (reservation.expires_at > Date.now()) {
          this.activeReservations.set(reservation.timeslot_id, {
            sessionId: reservation.session_id,
            studentId: reservation.student_id,
            expiresAt: reservation.expires_at,
            createdAt: reservation.created_at
          });
        } else {
          // Clean up expired reservations
          await db.run('DELETE FROM active_reservations WHERE timeslot_id = ?', [reservation.timeslot_id]);
        }
      }

      // Load user sessions
      const sessions = await db.all('SELECT * FROM user_sessions');
      for (const session of sessions) {
        // Only load recent sessions (within last 48 hours)
        if (session.last_activity > Date.now() - (48 * 60 * 60 * 1000)) {
          this.userSessions.set(session.session_id, {
            studentId: session.student_id,
            username: session.username,
            lastActivity: session.last_activity,
            reservedTimeslotId: session.reserved_timeslot_id,
            additionalData: JSON.parse(session.state_data || '{}')
          });
        } else {
          // Clean up old sessions
          await db.run('DELETE FROM user_sessions WHERE session_id = ?', [session.session_id]);
        }
      }

      console.log(`Loaded ${this.activeReservations.size} active reservations and ${this.userSessions.size} user sessions`);
    } catch (error) {
      console.error('Error loading persistent data:', error);
      throw error;
    }
  }

  /**
   * Clean up expired reservations and old sessions
   */
  async cleanupExpiredData() {
    const db = getDB();
    const now = Date.now();
    
    try {
      // Clean up expired reservations
      const expiredReservations = [];
      for (const [timeslotId, reservation] of this.activeReservations) {
        if (reservation.expiresAt <= now) {
          expiredReservations.push(timeslotId);
        }
      }

      for (const timeslotId of expiredReservations) {
        await this.removeReservation(timeslotId);
      }

      // Clean up old sessions (older than 48 hours)
      const oldSessions = [];
      const sessionTimeout = 48 * 60 * 60 * 1000; // 48 hours
      for (const [sessionId, session] of this.userSessions) {
        if (session.lastActivity < now - sessionTimeout) {
          oldSessions.push(sessionId);
        }
      }

      for (const sessionId of oldSessions) {
        await this.removeUserSession(sessionId);
      }

      if (expiredReservations.length > 0 || oldSessions.length > 0) {
        console.log(`Cleaned up ${expiredReservations.length} expired reservations and ${oldSessions.length} old sessions`);
      }
    } catch (error) {
      console.error('Error cleaning up expired data:', error);
    }
  }

  /**
   * Get all active reservations
   * @returns {Map} - Map of active reservations
   */
  getAllReservations() {
    return new Map(this.activeReservations);
  }

  /**
   * Get all user sessions
   * @returns {Map} - Map of user sessions
   */
  getAllUserSessions() {
    return new Map(this.userSessions);
  }

  /**
   * Check if a timeslot is reserved
   * @param {number} timeslotId - The timeslot ID
   * @returns {boolean} - True if reserved
   */
  isTimeslotReserved(timeslotId) {
    const reservation = this.activeReservations.get(timeslotId);
    return reservation && reservation.expiresAt > Date.now();
  }

  /**
   * Get reservation by user session
   * @param {string} sessionId - The session ID
   * @returns {number|null} - Timeslot ID or null
   */
  getReservationBySession(sessionId) {
    for (const [timeslotId, reservation] of this.activeReservations) {
      if (reservation.sessionId === sessionId && reservation.expiresAt > Date.now()) {
        return timeslotId;
      }
    }
    return null;
  }
}

// Export singleton instance
export default new ReservationManager();

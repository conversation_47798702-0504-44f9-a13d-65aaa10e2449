{"name": "booking-system-server", "version": "1.0.0", "description": "Backend for the booking system", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "restart": "pkill -f 'node src/index.js' || true && sleep 1 && npm start", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcrypt": "^5.1.0", "cors": "^2.8.5", "dompurify": "^3.2.6", "express": "^4.18.2", "express-session": "^1.17.3", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsdom": "^26.1.0", "socket.io": "^4.6.1", "sqlite": "^4.2.0", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.1.10"}}
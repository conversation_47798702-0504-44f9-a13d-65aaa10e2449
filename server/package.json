{"name": "booking-system-server", "version": "1.0.0", "description": "Backend for the booking system", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcrypt": "^5.1.0", "cors": "^2.8.5", "express": "^4.18.2", "express-session": "^1.17.3", "socket.io": "^4.6.1", "sqlite": "^4.2.0", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.1.10"}}
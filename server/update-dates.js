/**
 * update-dates.js
 *
 * This script updates the booking list dates to make them visible and bookable immediately.
 */

import { initDB, getDB } from './src/db.js';

async function updateDates() {
  try {
    console.log('Initializing database...');
    await initDB();
    const db = getDB();

    // Get current date and future dates for constraints
    const today = new Date();
    const oneMonthLater = new Date(today);
    oneMonthLater.setMonth(today.getMonth() + 1);

    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`; // Format as YYYY-MM-DD
    };

    const todayFormatted = formatDate(today);
    const oneMonthLaterFormatted = formatDate(oneMonthLater);

    // Update booking list dates
    console.log('Updating booking list dates...');
    await db.run(
      `UPDATE booking_lists SET
        booking_start_date = ?,
        booking_end_date = ?,
        visibility_start_date = ?,
        visibility_end_date = ?,
        cancellation_deadline = ?
      WHERE id = 1`,
      [
        todayFormatted,           // Booking starts today
        oneMonthLaterFormatted,   // Booking ends in one month
        todayFormatted,           // Visible from today
        oneMonthLaterFormatted,   // Visible until one month later
        oneMonthLaterFormatted    // Can cancel until one month later
      ]
    );

    console.log('Booking list dates updated successfully!');
    console.log(`\nBooking list is now visible and bookable from ${todayFormatted} to ${oneMonthLaterFormatted}`);

  } catch (error) {
    console.error('Error updating dates:', error);
  }
}

updateDates();

# Bokningssystem för Examination

Ett system för bokning av redovisningstider för laborationer och andra typer av examinationer.

## Beskrivning

I KTH-kurser behöver man ofta ha bokningslistor för bokning av till exempel labbredovisningar, projektredovisningar och muntliga tentor. Detta system erbjuder en lösning med funktioner som saknas i Canvas, såsom att bokningslistor publiceras först efter ett visst datum och att bokningar kan genomföras endast under ett bestämt tidsintervall.

## Komma igång

### Förutsättningar

- Node.js (v14 eller högre)
- npm (v6 eller högre)

### Installation

1. Klona repositoryt
2. Installera beroenden för rotprojektet:

```bash
npm install
```

3. Installera beroenden för klienten och servern:

```bash
cd client && npm install
cd ../server && npm install
```

### Starta applikationen

**Viktigt: Kör alltid startkommandot från projektets rotkatalog.**

För att starta både klienten och servern:

```bash
./start.sh
```

Eller med npm:

```bash
npm start
```

Detta kommer att:
1. Bygga klientapplikationen
2. Starta servern på http://localhost:8989
3. Starta klientens utvecklingsserver på http://localhost:5173

För att stoppa applikationen, tryck `Ctrl+C` i terminalen.

### Alternativa startmetoder

Om du bara vill starta servern:

```bash
npm run start:server
```

Om du bara vill starta klienten:

```bash
npm run start:client
```

### Testkonton

Följande konton kan användas för att testa systemet:

- **Studentkonton**:
  - Användarnamn: `kevinlam`, Lösenord: `password123`
  - Användarnamn: `allaninma`, Lösenord: `password123`

- **Administratörskonto**:
  - Användarnamn: `admin1`, Lösenord: `admin123`

### Skapa testdata

Om du behöver skapa testdata med bokningsbara tider, kör:

```bash
cd server
node create-current-booking-list.js
```

## Systemkrav

### Systemet i helhet

- **Inloggning**: Systemet kräver inloggning. I framtiden kan detta ersättas med KTH:s CAS.
- **Datalagring**: Data lagras i en SQLite-databas (inte i filer).
- **Datapersistens**: Data ska inte förloras vid systemkrasch/strömavbrott.
- **Autentisering**: All webbinteraktion med systemet (förutom inloggningen) går genom autentisering och sessioner.

### Administratörens del

Administratören kan:

1. **Hantera bokningstider**: Skapa nya, modifiera och ta bort befintliga bokningstider.
2. **Sätta tidsbegränsningar**:
   - Ange period då bokning kan genomföras (start- och slutdatum)
   - Ange period då bokningslistan är synlig (start- och slutdatum)
   - Ange deadline för avbokning
3. **Hantera platser**: Knyta en plats till varje bokningslista, med möjlighet att modifiera platsen för varje boknings-slot.
4. **Examinationstyper**: Varje bokningstid har en examinationstyp (t.ex. muntlig redovisning, skrivning i sal).
5. **Begränsa bokningar**: Begränsa antal bokningstider som kan bokas av en student eller studentgrupp.
6. **Spåra avbokningar**: Avbokade tider raderas inte utan markeras som avbokade för att administratören ska kunna se historiken.

### Studenternas del

Studenter kan:

1. **Boka/avboka tider**: Studenter kan boka och avboka tider inom de begränsningar som administratören satt.
2. **Gruppbokningar**: En bokad tid kan avbokas/modifieras av alla medlemmar i gruppen om det är en gruppbokning.
3. **Bokningshistorik**: Avbokade tider raderas inte spårlöst utan markeras som avbokade.

## Teknisk information

- **Frontend**: Vue.js med Vue Router och Vuex
- **Backend**: Node.js med Express
- **Databas**: SQLite
- **Realtidsuppdateringar**: Socket.io

## Utveckling

För att bidra till projektet:

1. Klona repositoryt
2. Installera beroenden i både client- och server-katalogerna med `npm install`
3. Följ kodstandarder och håll commits små och atomära
4. Skriv tydliga commit-meddelanden på svenska

## Gitignore

Det är bra att i början av ett projekt skapa en bra `.gitignore` så att ni inte råkar pusha upp byggfiler, paket eller annat som inte hör hemma i ett gitrepo.
En bra sida för att generera en gitignore är [gitignore.io](https://gitignore.io/). Där kan ni skriva in de språk/editors/operativsystem ni använder och därmed få en rimlig gitignore.

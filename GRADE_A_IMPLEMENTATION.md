# Grade A Implementation - Säkerhetsförbättringar

Detta dokument beskriver alla implementerade förbättringar för att uppfylla Grade A-kraven.

## ✅ **Krav 1: XSS-skydd**

### **Helmet Middleware**
- **Implementerat**: `helmet` middleware för säkerhetsheaders
- **Plats**: `server/src/index.js` (rader 64-76)
- **Funktioner**:
  - Content Security Policy (CSP)
  - X-Frame-Options
  - X-Content-Type-Options
  - Referrer Policy
  - Cross-Origin Embedder Policy

### **Express-validator**
- **Implementerat**: Omfattande input-validering
- **Plats**: `server/src/utils/validation.js`
- **Funktioner**:
  - Validering av användarregistrering
  - Validering av inloggning
  - Validering av gruppnamn
  - Validering av bokningslistor
  - Validering av tidsslots

### **Data Escaping & Sanitization**
- **HTML Escaping**: `escapeHtml()` funktion som konverterar `<script>` till `&lt;script&gt;`
- **HTML Sanitization**: `sanitizeHtml()` med DOMPurify
- **Implementerat i**: Alla controllers använder validering och sanitization

### **Exempel på skydd**:
```javascript
// Innan: Sårbar för XSS
const username = req.body.username; // Kan innehålla <script>alert('XSS')</script>

// Efter: Skyddad mot XSS
const username = sanitizeHtml(req.body.username); // Blir: alert('XSS')
```

## ✅ **Krav 2: HTTPS med Certifikat**

### **Self-signed Certifikat**
- **Skapade**: RSA 4096-bit certifikat
- **Plats**: `server/certs/server.key` och `server/certs/server.cert`
- **Giltighet**: 365 dagar
- **Kommando**: 
```bash
openssl req -x509 -newkey rsa:4096 -keyout certs/server.key -out certs/server.cert -days 365 -nodes
```

### **HTTPS Server**
- **HTTPS Port**: 8990
- **HTTP Port**: 8989 (redirectar till HTTPS)
- **Implementerat**: Dubbla servrar med automatisk redirect
- **Plats**: `server/src/index.js` (rader 34-56)

### **Säker Session-konfiguration**
```javascript
cookie: {
  secure: !!httpsServer, // Använder säkra cookies när HTTPS är tillgängligt
  httpOnly: true,
  sameSite: 'lax'
}
```

### **Starta med HTTPS**:
```bash
./start-https.sh  # Startar med HTTPS-stöd
```

## ✅ **Krav 3: Persistent Storage av Levande Resurser**

### **Enhanced Reservation Manager**
- **Plats**: `server/src/utils/reservationManager.js`
- **Funktioner**:
  - Persistent lagring av aktiva reservationer
  - Persistent lagring av användarsessioner
  - Automatisk återställning vid serveromstart
  - Cleanup av utgångna data

### **Nya Databastabeller**
```sql
-- Aktiva reservationer (överlever serveromstart)
CREATE TABLE active_reservations (
  timeslot_id INTEGER PRIMARY KEY,
  session_id TEXT NOT NULL,
  student_id INTEGER NOT NULL,
  expires_at INTEGER NOT NULL,
  created_at INTEGER NOT NULL
);

-- Användarsessioner (överlever serveromstart)
CREATE TABLE user_sessions (
  session_id TEXT PRIMARY KEY,
  student_id INTEGER,
  username TEXT NOT NULL,
  last_activity INTEGER NOT NULL,
  reserved_timeslot_id INTEGER,
  state_data TEXT
);
```

### **Levande Resurser som Bevaras**:
1. **Aktiva reservationer**: Exakt tidsstämpel när reservation går ut
2. **Användarsessioner**: Vem som är inloggad och deras tillstånd
3. **Temporära bokningar**: Pågående bokningsprocesser
4. **Realtidsinteraktioner**: Socket.io-anslutningar återställs

### **Automatisk Återställning**:
```javascript
// Vid serverstart
await reservationManager.loadPersistentData();
console.log('Persistent live resources loaded');
```

### **Test av Persistent Storage**:
1. Logga in som student
2. Reservera en tidslot (10 sekunder)
3. Starta om servern: `Ctrl+C` och `node src/index.js`
4. Reservationen finns kvar och går ut vid rätt tid

## **Säkerhetsförbättringar Sammanfattning**

### **Före (Sårbarheter)**:
- ❌ Ingen helmet middleware
- ❌ Ingen input-validering
- ❌ Ingen data escaping
- ❌ Endast HTTP
- ❌ Osäkra cookies
- ❌ Grundläggande persistent storage

### **Efter (Säkert)**:
- ✅ Helmet med CSP och säkerhetsheaders
- ✅ Express-validator för all input
- ✅ DOMPurify för HTML-sanitization
- ✅ HTTPS med self-signed certifikat
- ✅ Säkra cookies med httpOnly
- ✅ Avancerad persistent storage för levande resurser

## **Användning**

### **Starta med HTTPS**:
```bash
./start-https.sh
```

### **Åtkomst**:
- **HTTPS**: https://localhost:8990
- **HTTP**: http://localhost:8989 (redirectar till HTTPS)

### **Certifikatvarning**:
Webbläsaren kommer att varna för self-signed certifikat. Klicka "Advanced" → "Proceed to localhost (unsafe)" för att fortsätta.

## **Verifiering av Krav**

### **XSS-skydd Test**:
```javascript
// Testa med skadlig input
POST /api/register
{
  "username": "<script>alert('XSS')</script>",
  "password": "password123"
}
// Resultat: Input sanitized, inget XSS
```

### **HTTPS Test**:
```bash
curl -k https://localhost:8990/api/users/me
# Fungerar med HTTPS
```

### **Persistent Storage Test**:
1. Reservera tidslot
2. Starta om server
3. Reservation finns kvar

Alla Grade A-krav är nu uppfyllda!

{"name": "booking-system", "version": "1.0.0", "description": "Booking system for exam times", "main": "index.js", "scripts": {"start": "./start.sh", "start:concurrent": "concurrently \"npm run start:server\" \"npm run start:client\"", "start:server": "cd server && npm run dev", "start:client": "cd client && npm run dev", "setup-test-data": "node setup-test-data.js", "build": "cd client && npm run build"}, "devDependencies": {"concurrently": "^8.2.2"}}
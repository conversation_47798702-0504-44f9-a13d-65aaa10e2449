{"name": "booking-system", "version": "1.0.0", "description": "Booking system for exam times", "main": "index.js", "scripts": {"start": "bash start.sh", "start:server-only": "cd server && node src/index.js", "start:server-dev": "cd server && npm run dev", "start:https": "bash start-https.sh", "start:concurrent": "concurrently \"npm run start:server-dev\" \"npm run start:client\"", "start:client": "cd client && npm run dev", "setup-test-data": "node setup-test-data.js", "build": "cd client && npm run build", "server:start": "cd server && node src/index.js", "server:stop": "pkill -f 'node src/index.js' || true", "server:restart": "npm run server:stop && sleep 2 && npm run server:start"}, "devDependencies": {"concurrently": "^8.2.2"}}